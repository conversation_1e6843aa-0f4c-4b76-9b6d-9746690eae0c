stages:
  - deploy

variables:
  DEV_HOST: $DEV_HOST
  DEV_USER: $DEV_USER

deploy:
  stage: deploy
  image: debian:latest
  script:
    - |
      ssh $DEV_USER@$DEV_HOST << 'ENDSSH'
      echo "Connected to the server"
      cd /var/www/logru/src
      git stash
      git fetch origin
      git pull origin dev
      cd /var/www/logru
      docker compose run --rm composer install --ignore-platform-reqs
      docker exec logru-php-1 php artisan cache:clear
      docker exec logru-php-1 php artisan migrate
      echo "Deployment completed successfully!"
      ENDSSH
  only:
    - dev