<?php

namespace Tests\Unit\Jobs\FIFOJobs;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\ShipmentWarehouseItemRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceItemJob;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Mockery;
use Tests\TestCase;

class RecalculationAfterUpdateAcceptanceItemJobTest extends TestCase
{
    use WithFaker;
    use RefreshDatabase;


    private object $resource;
    private AcceptanceItemDto $newResource;
    private FifoServiceContract $fifoService;
    private ShipmentsRepositoryContract $shipmentsRepository;
    private ShipmentItemsRepositoryContract $shipmentItemsRepository;
    private WarehouseItemsRepositoryContract $warehouseItemsRepository;
    private ShipmentWarehouseItemRepositoryContract $shipmentWarehouseItemsRepository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->resource = (object)[
            'id' => $this->faker()->uuid,
            'acceptance_id' => $this->faker()->uuid,
            'product_id' => $this->faker()->uuid,
            'price' => '100',
            'quantity' => '10'
        ];

        $this->newResource = new AcceptanceItemDto(
            quantity: '10',
            acceptanceId: null,
            productId: null,
            price: '150'
        );

        $this->fifoService = Mockery::mock(FifoServiceContract::class);
        $this->shipmentsRepository = Mockery::mock(ShipmentsRepositoryContract::class);
        $this->shipmentItemsRepository = Mockery::mock(ShipmentItemsRepositoryContract::class);
        $this->warehouseItemsRepository = Mockery::mock(WarehouseItemsRepositoryContract::class);
        $this->shipmentWarehouseItemsRepository = Mockery::mock(ShipmentWarehouseItemRepositoryContract::class);

        $this->app->instance(FifoServiceContract::class, $this->fifoService);
        $this->app->instance(ShipmentsRepositoryContract::class, $this->shipmentsRepository);
        $this->app->instance(ShipmentItemsRepositoryContract::class, $this->shipmentItemsRepository);
        $this->app->instance(WarehouseItemsRepositoryContract::class, $this->warehouseItemsRepository);
        $this->app->instance(ShipmentWarehouseItemRepositoryContract::class, $this->shipmentWarehouseItemsRepository);
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_recalculates_sums_when_only_price_changes(): void
    {
        // Arrange
        $this->assertNotEquals($this->resource->price, $this->newResource->price);
        $this->assertEquals($this->resource->quantity, $this->newResource->quantity);

        $shipmentItems = collect([
            (object)[
                'shipment_item_id' => '1',
                'shipment_id' => '1',
                'quantity' => 5,
                'total_price' => 500
            ]
        ]);

        $shipmentCosts = collect([
            '1' => (object)['new_cost' => 750]
        ]);

        $this->warehouseItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($data, $id, $productId) {
                return isset($data['unit_price'])
                    && isset($data['updated_at'])
                    && isset($data['total_price'])
                    && $data['unit_price'] === $this->newResource->price
                    && $data['total_price'] === bcmul($this->newResource->price, $this->newResource->quantity, 2)
                    && $id === $this->resource->id
                    && $productId === $this->resource->product_id;
            });

        $this->shipmentWarehouseItemsRepository
            ->shouldReceive('getShipmentItemsByWarehouseItem')
            ->once()
            ->with($this->resource->id)
            ->andReturn($shipmentItems);

        $this->shipmentWarehouseItemsRepository
            ->shouldReceive('calculateShipmentCostsByItems')
            ->once()
            ->with(['1'])
            ->andReturn($shipmentCosts);

        DB::shouldReceive('table')
            ->once()
            ->with('shipment_items')
            ->andReturnSelf();

        DB::shouldReceive('upsert')
            ->once()
            ->withArgs(function ($updates, $uniqueBy, $fields) {
                return count($updates) === 1
                    && $updates[0]['total_cost'] === 750
                    && $uniqueBy === ['id']
                    && $fields === ['total_cost', 'profit', 'cost'];
            });

        $this->shipmentItemsRepository
            ->shouldReceive('getShipmentTotalsByIds')
            ->once()
            ->with(['1'])
            ->andReturn([]);

        $this->shipmentsRepository
            ->shouldReceive('upsert')
            ->once()
            ->with([], ['total_cost', 'profit']);

        // Act
        $job = new RecalculationAfterUpdateAcceptanceItemJob($this->resource, $this->newResource);
        $job->handle();
    }

    public function test_recalculates_positions_when_quantity_changes(): void
    {
        // Arrange
        $this->newResource = new AcceptanceItemDto(
            quantity: '15',
            acceptanceId: null,
            productId: null,
            price: '100'
        );

        // Проверяем, что количество изменилось
        $this->assertNotEquals($this->resource->quantity, $this->newResource->quantity);

        $shipmentItem = (object)[
            'id' => $this->faker()->uuid
        ];

        $this->warehouseItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($data, $id, $productId) {
                return isset($data['quantity'])
                    && isset($data['unit_price'])
                    && isset($data['total_price'])
                    && isset($data['updated_at'])
                    && $data['quantity'] === $this->newResource->quantity
                    && $data['unit_price'] === $this->newResource->price
                    && $data['total_price'] === bcmul($this->newResource->price, $this->newResource->quantity, 2)
                    && $id === $this->resource->acceptance_id
                    && $productId === $this->resource->product_id;
            });

        $this->warehouseItemsRepository
            ->shouldReceive('getShipmentItemIdByAcceptance')
            ->once()
            ->with($this->resource->acceptance_id, $this->resource->product_id)
            ->andReturn($shipmentItem);

        $this->fifoService
            ->shouldReceive('handle')
            ->once()
            ->with($shipmentItem->id);

        // Act
        $job = new RecalculationAfterUpdateAcceptanceItemJob($this->resource, $this->newResource);
        $job->handle();
    }

    public function test_handles_no_shipment_found(): void
    {
        // Arrange
        $this->newResource = new AcceptanceItemDto(
            quantity: '15',
            acceptanceId: null,
            productId: null,
            price: '100'
        );

        // Проверяем, что количество изменилось
        $this->assertNotEquals($this->resource->quantity, $this->newResource->quantity);

        $this->warehouseItemsRepository
            ->shouldReceive('update')
            ->once()
            ->withArgs(function ($data, $id, $productId) {
                return isset($data['quantity'])
                    && isset($data['unit_price'])
                    && isset($data['total_price'])
                    && isset($data['updated_at'])
                    && $data['quantity'] === $this->newResource->quantity
                    && $data['unit_price'] === $this->newResource->price
                    && $data['total_price'] === bcmul($this->newResource->price, $this->newResource->quantity, 2)
                    && $id === $this->resource->acceptance_id
                    && $productId === $this->resource->product_id;
            });

        $this->warehouseItemsRepository
            ->shouldReceive('getShipmentItemIdByAcceptance')
            ->once()
            ->with($this->resource->acceptance_id, $this->resource->product_id)
            ->andReturn(null);

        $this->fifoService
            ->shouldNotReceive('handle');

        // Act
        $job = new RecalculationAfterUpdateAcceptanceItemJob($this->resource, $this->newResource);
        $job->handle();
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
