<?php

namespace Tests\Unit\Console\Commands;

use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\DB;
use Mockery;
use PHPUnit\Framework\Attributes\WithoutErrorHandler;
use Tests\TestCase;

class FillStatusesTest extends TestCase
{
    use DatabaseTransactions;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем тестовые данные
        $this->createMockDataFile();

        // По умолчанию мокаем DB для успешного выполнения
        DB::shouldReceive('table')->andReturnSelf()->byDefault();
        DB::shouldReceive('insert')->andReturn(true)->byDefault();
    }

    protected function tearDown(): void
    {
        // Удаляем тестовые файлы
        $dataPath = __DIR__ . '/app/Data';
        $appPath = __DIR__ . '/app';

        // Удаляем файл статусов
        if (file_exists($dataPath . '/Statuses.php')) {
            unlink($dataPath . '/Statuses.php');
        }

        // Удаляем директорию Data
        if (is_dir($dataPath)) {
            // Убедимся, что директория пуста
            $files = scandir($dataPath);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    unlink($dataPath . '/' . $file);
                }
            }
            rmdir($dataPath);
        }

        // Удаляем директорию app
        if (is_dir($appPath)) {
            // Убедимся, что директория пуста
            $files = scandir($appPath);
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..') {
                    rmdir($appPath . '/' . $file);
                }
            }
            rmdir($appPath);
        }

        Mockery::close();
        parent::tearDown();
    }

    protected function createMockDataFile(): void
    {
        // Создаем тестовые данные для статусов
        $statusesData = [
            'types' => [
                'Заказ',
                'Задача'
            ],
            'statuses' => [
                'Заказ' => [
                    [
                        'name' => 'Новый',
                        'color' => '#FF0000'
                    ],
                    [
                        'name' => 'В обработке',
                        'color' => '#FFFF00'
                    ],
                    [
                        'name' => 'Завершен',
                        'color' => '#00FF00'
                    ]
                ],
                'Задача' => [
                    [
                        'name' => 'К выполнению',
                        'color' => '#0000FF'
                    ],
                    [
                        'name' => 'Выполняется',
                        'color' => '#FF00FF'
                    ],
                    [
                        'name' => 'Выполнена',
                        'color' => '#00FFFF'
                    ]
                ]
            ]
        ];

        // Мокаем base_path
        $this->app->bind('path.base', function () {
            return __DIR__;
        });

        // Создаем директории и файл данных
        if (!file_exists(__DIR__ . '/app/Data')) {
            mkdir(__DIR__ . '/app/Data', 0777, true);
        }

        file_put_contents(
            __DIR__ . '/app/Data/Statuses.php',
            '<?php return ' . var_export($statusesData, true) . ';'
        );
    }

    #[WithoutErrorHandler] public function test_it_should_fill_statuses_successfully(): void
    {
        // Тестируем только успешный вызов и вывод команды
        $this->artisan('app:fill-statuses')
            ->assertExitCode(0)
            ->expectsOutput('Заполняем таблицу "status_types"...')
            ->expectsOutput('Успешно!');
    }

    #[WithoutErrorHandler] public function test_it_should_handle_database_error_in_types(): void
    {
        // Мокаем исключение при вставке в таблицу типов статусов
        DB::shouldReceive('table')
            ->with('status_types')
            ->once()
            ->andReturnSelf();

        DB::shouldReceive('insert')
            ->once()
            ->andThrow(new \Exception('Database error'));

        // Проверяем, что команда корректно обрабатывает ошибку
        $this->artisan('app:fill-statuses')
            ->assertExitCode(1)
            ->expectsOutput('Заполняем таблицу "status_types"...');
    }
}
