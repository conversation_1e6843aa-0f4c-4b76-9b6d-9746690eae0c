<?php

namespace Tests\Unit\Services\Api\Internal\DocumentNumberGenerator;

use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\NumberingType;
use App\Models\Acceptance;
use App\Models\Cabinet;
use App\Models\CabinetSettings;
use App\Models\LegalDetail;
use App\Models\LegalEntity;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DocumentNumberGeneratorTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    /**
     * Тест, проверяющий генерацию базового номера документа (000001)
     */
    public function test_generates_base_number_when_no_previous_documents(): void
    {
        // Arrange
        $cabinetId = Cabinet::factory()->create()->id;
        $legalEntityId = $this->faker()->uuid;

        // Создаем настройки кабинета
        CabinetSettings::factory()->create([
            'cabinet_id' => $cabinetId,
            'numbering_type' => NumberingType::OnlyNumbers->value
        ]);

        // Создаем юридическое лицо
        $legalEntity = LegalEntity::factory()->create([
            'id' => $legalEntityId,
            'short_name' => 'Test Company'
        ]);

        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntityId,
            'type' => LegalEntityType::LEGAL->value,
            'prefix' => 'TC'
        ]);

        // Act
        $generator = new DocumentNumberGenerator(
            'acceptances',
            $cabinetId,
            null,
            $legalEntityId
        );

        $number = $generator->generateNumber();

        // Assert
        $this->assertEquals('000001', $number);
    }

    /**
     * Тест, проверяющий увеличение номера документа
     * при наличии предыдущего документа
     */
    public function test_increments_number_when_previous_document_exists(): void
    {
        // Arrange
        $cabinetId = Cabinet::factory()->create()->id;
        $legalEntityId = $this->faker()->uuid;

        // Создаем настройки кабинета
        CabinetSettings::factory()->create([
            'cabinet_id' => $cabinetId,
            'numbering_type' => NumberingType::OnlyNumbers->value,
        ]);

        // Создаем юридическое лицо
        $legalEntity = LegalEntity::factory()->create([
            'id' => $legalEntityId,
            'short_name' => 'Test Company'
        ]);

        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntityId,
            'type' => LegalEntityType::LEGAL->value,
            'prefix' => 'TC'
        ]);

        // Создаем предыдущий документ
        Acceptance::factory()->create([
            'cabinet_id' => $cabinetId,
            'legal_entity_id' => $legalEntityId,
            'number' => '000042',
        ]);

        // Act
        $generator = new DocumentNumberGenerator(
            'acceptances',
            $cabinetId,
            null,
            $legalEntityId
        );

        $number = $generator->generateNumber();

        // Assert
        $this->assertEquals('000043', $number);
    }

    /**
     * Тест, проверяющий генерацию номера с префиксом CP (Customer/Partner)
     */
    public function test_generates_cp_number_format(): void
    {
        // Arrange
        $cabinetId = Cabinet::factory()->create()->id;
        $legalEntityId = $this->faker()->uuid;
        $currentDate = Carbon::create(2023, 5, 15);
        Carbon::setTestNow($currentDate);

        // Создаем настройки кабинета
        CabinetSettings::factory()->create([
            'cabinet_id' => $cabinetId,
            'numbering_type' => NumberingType::CPNumbers->value,
        ]);

        // Создаем юридическое лицо
        $legalEntity = LegalEntity::factory()->create([
            'id' => $legalEntityId,
            'short_name' => 'Test Company',
        ]);

        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntityId,
            'type' => LegalEntityType::LEGAL->value,
            'prefix' => 'TC'
        ]);

        // Act
        $generator = new DocumentNumberGenerator(
            'acceptances',
            $cabinetId,
            null,
            $legalEntityId
        );

        $number = $generator->generateNumber();

        // Assert
        $expectedPrefix = 'TC-230515/';
        $this->assertEquals($expectedPrefix . '1', $number);
    }

    /**
     * Тест, проверяющий сохранение существующего номера
     * при типе нумерации "только цифры"
     */
    public function test_keeps_existing_number_when_only_numbers_type(): void
    {
        // Arrange
        $cabinetId = Cabinet::factory()->create()->id;
        $legalEntityId = $this->faker()->uuid;
        $existingNumber = '42A';

        // Создаем настройки кабинета
        CabinetSettings::factory()->create([
            'cabinet_id' => $cabinetId,
            'numbering_type' => NumberingType::OnlyNumbers->value,
            'global_numbering' => false
        ]);

        // Создаем юридическое лицо
        $legalEntity = LegalEntity::factory()->create([
            'id' => $legalEntityId,
            'short_name' => 'Test Company'
        ]);

        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntityId,
            'type' => LegalEntityType::LEGAL->value,
            'prefix' => 'TC'
        ]);

        // Act
        $generator = new DocumentNumberGenerator(
            'acceptances',
            $cabinetId,
            $existingNumber,
            $legalEntityId
        );

        $number = $generator->generateNumber();

        // Assert
        $this->assertEquals($existingNumber, $number);
    }

    /**
     * Тест, проверяющий генерацию номера для индивидуального предпринимателя
     * с использованием инициалов
     */
    public function test_generates_cp_number_for_individual(): void
    {
        // Arrange
        $cabinetId = Cabinet::factory()->create()->id;
        $legalEntityId = $this->faker()->uuid;
        $currentDate = Carbon::create(2023, 5, 15);
        Carbon::setTestNow($currentDate);

        // Создаем настройки кабинета
        CabinetSettings::factory()->create([
            'cabinet_id' => $cabinetId,
            'numbering_type' => NumberingType::CPNumbers->value,
            'global_numbering' => false
        ]);

        // Создаем юридическое лицо (ИП)
        $legalEntity = LegalEntity::factory()->create([
            'id' => $legalEntityId,
        ]);

        LegalDetail::factory()->create([
            'legal_entity_id' => $legalEntityId,
            'type' => LegalEntityType::INDIVIDUAL->value,
            'firstname' => 'Ivan',
            'lastname' => 'Petrov',
            'patronymic' => 'Sergeevich'
        ]);

        // Act
        $generator = new DocumentNumberGenerator(
            'acceptances',
            $cabinetId,
            null,
            $legalEntityId
        );

        $number = $generator->generateNumber();

        // Assert
        $expectedPrefix = 'PIS-230515/';
        $this->assertEquals($expectedPrefix . '1', $number);
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Сбрасываем мок времени
        parent::tearDown();
    }
}
