<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Permission;
use App\Models\Department;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\EmployeePermission;
use App\Enums\Api\Internal\PermissionScopeEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class EmployeePermissionTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_show_employee_permissions(): void
    {
        // Создаем разрешения
        // Привязываем разрешения к сотруднику
        foreach (Permission::factory()->count(3)->create() as $permission) {
            EmployeePermission::factory()->create([
                'employee_id' => $this->employee->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value
            ]);
        }

        $response = $this->getJson("/api/internal/employees/{$this->employee->id}/permissions");

        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'created_at',
                    'updated_at',
                    'employee_id',
                    'permission_id',
                    'scope',
                    'permissions'
                ]
            ]);

        // Проверяем количество разрешений
        $this->assertCount(3, $response->json());

        // Проверяем что все разрешения принадлежат нашему сотруднику
        foreach ($response->json() as $item) {
            $this->assertEquals($this->employee->id, $item['employee_id']);
        }
    }

    public function test_cannot_show_employee_permissions_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $otherEmployee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $otherEmployee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем разрешения для сотрудника из другого кабинета
        foreach (Permission::factory()->count(2)->create() as $permission) {
            EmployeePermission::factory()->create([
                'employee_id' => $otherEmployee->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value
            ]);
        }

        $response = $this->getJson("/api/internal/employees/{$otherEmployee->id}/permissions");

        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_employee_permissions(): void
    {
        $fakeId = $this->faker->uuid;

        $response = $this->getJson("/api/internal/employees/{$fakeId}/permissions");

        $response->assertStatus(404);
    }

    public function test_can_update_employee_permissions(): void
    {
        // Создаем разрешения
        $permissions = Permission::factory()->count(3)->create();

        // Создаем существующие разрешения сотрудника
        $existingPermissions = [];
        foreach ($permissions as $index => $permission) {
            $employeePermission = EmployeePermission::factory()->create([
                'employee_id' => $this->employee->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value
            ]);
            $existingPermissions[] = [
                'id' => $employeePermission->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_NONE->value // Меняем scope
            ];
        }

        // Добавляем новое разрешение
        $newPermission = Permission::factory()->create();
        $existingPermissions[] = [
            'permission_id' => $newPermission->id,
            'scope' => PermissionScopeEnum::SCOPE_ALL->value
        ];

        $response = $this->putJson("/api/internal/employees/{$this->employee->id}/permissions", [
            'permissions' => $existingPermissions
        ]);

        $response->assertStatus(204);

        // Проверяем что разрешения обновились
        foreach ($existingPermissions as $permission) {
            $this->assertDatabaseHas('employee_permissions', [
                'employee_id' => $this->employee->id,
                'permission_id' => $permission['permission_id'],
            ]);
        }
    }

    public function test_cannot_update_employee_permissions_from_other_cabinet(): void
    {
        // Создаем сотрудника в другом кабинете
        $otherEmployee = Employee::factory()->create();
        CabinetEmployee::factory()->create([
            'employee_id' => $otherEmployee->id,
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Создаем разрешения
        $permissions = Permission::factory()->count(2)->create();
        $permissionData = [];
        foreach ($permissions as $permission) {
            $permissionData[] = [
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value
            ];
        }

        $response = $this->putJson("/api/internal/employees/{$otherEmployee->id}/permissions", [
            'permissions' => $permissionData
        ]);

        $response->assertNotFound();
    }

    public function test_cannot_update_non_existent_employee_permissions(): void
    {
        $fakeId = $this->faker->uuid;

        $permissions = Permission::factory()->count(2)->create();
        $permissionData = [];
        foreach ($permissions as $permission) {
            $permissionData[] = [
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_OWN->value
            ];
        }

        $response = $this->putJson("/api/internal/employees/{$fakeId}/permissions", [
            'permissions' => $permissionData
        ]);

        $response->assertStatus(404);
    }

    public function test_update_employee_permissions_validation_errors(): void
    {
        $invalidData = [
            'permissions' => [
                [
                    'id' => 'not-a-uuid',
                    'permission_id' => 'invalid-uuid',
                    'scope' => 'invalid-scope'
                ]
            ]
        ];

        $response = $this->putJson("/api/internal/employees/{$this->employee->id}/permissions", $invalidData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'permissions.0.id',
                'permissions.0.permission_id',
                'permissions.0.scope'
            ]);
    }

    public function test_can_update_employee_permissions_with_empty_list(): void
    {
        // Сначала создаем несколько разрешений
        foreach (Permission::factory()->count(3)->create() as $permission) {
            EmployeePermission::factory()->create([
                'employee_id' => $this->employee->id,
                'permission_id' => $permission->id,
                'scope' => PermissionScopeEnum::SCOPE_ALL->value
            ]);
        }

        // Отправляем пустой список разрешений
        $response = $this->putJson("/api/internal/employees/{$this->employee->id}/permissions", [
            'permissions' => []
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['permissions']);
    }
}
