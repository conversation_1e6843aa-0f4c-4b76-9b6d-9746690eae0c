<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Bookmark;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Enums\Api\Internal\ResourcesEnum;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;

class BookmarkTest extends TestCase
{
    use WithFaker;
    use DatabaseTransactions;

    private Employee $employee;
    private Cabinet $cabinet;
    private Bookmark $bookmark;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_bookmarks(): void
    {
        Bookmark::factory()->count(10)->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        Bookmark::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        Bookmark::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/bookmarks?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'name',
                        'entity',
                        'filters'
                    ]
                ],
                'meta'
            ])
            ->assertJsonCount(10, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
            $this->assertEquals($this->employee->id, $item['employee_id']);
        }
    }

    public function test_can_get_bookmarks_by_entity(): void
    {
        $entity = ResourcesEnum::LEGAL_ENTITIES->value;

        Bookmark::factory()->count(2)->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
            'entity' => $entity
        ]);

        Bookmark::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
            'entity' => $entity
        ]);

        Bookmark::factory()->count(8)->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
            'entity' => ResourcesEnum::ACCEPTANCE_ITEMS->value
        ]);

        Bookmark::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/bookmarks?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
                'entity' => $entity
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'data',
                'meta'
            ])
            ->assertJsonCount(2, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
            $this->assertEquals($entity, $item['entity']);
            $this->assertEquals($this->employee->id, $item['employee_id']);
        }
    }

    public function test_cannot_get_bookmarks_in_other_cabinet(): void
    {
        Bookmark::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/bookmarks?' . http_build_query([
                'cabinet_id' => $this->otherCabinet->id,
            ]));

        $response->assertForbidden()
            ->assertJsonMissing(['data']);
    }

    public function test_can_view_bookmark(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $response = $this->getJson("/api/internal/bookmarks/{$bookmark->id}");

        $response->assertOk()
            ->assertJson([
                'id' => $bookmark->id,
                'cabinet_id' => $bookmark->cabinet_id,
                'employee_id' => $bookmark->employee_id,
                'name' => $bookmark->name,
                'entity' => $bookmark->entity,
            ]);
    }

    public function test_cant_view_other_employee_bookmark(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $response = $this->getJson("/api/internal/bookmarks/{$bookmark->id}");

        $response->assertNotFound();
    }

    public function test_cant_view_bookmark_in_other_cabinet(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson("/api/internal/bookmarks/{$bookmark->id}");

        $response->assertNotFound();
    }

    public function test_can_store_bookmark(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => $this->faker()->name,
            'entity' => $this->faker()->randomElement(ResourcesEnum::cases())->value,
            'filters' => json_encode($this->faker()->word),
        ];

        $response = $this->postJson("/api/internal/bookmarks", $data);

        $response->assertCreated();

        $this->assertDatabaseHas(
            'bookmarks',
            [
                'id' => $response->json('id'),
                'cabinet_id' => $data['cabinet_id'],
                'name' => $data['name'],
                'entity' => $data['entity']
            ]
        );
    }

    public function test_cant_store_bookmark_in_other_cabinet(): void
    {
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => $this->faker()->name,
            'entity' => $this->faker()->randomElement(ResourcesEnum::cases())->value,
            'data' => $this->faker()->word,
        ];

        $response = $this->postJson("/api/internal/bookmarks", $data);

        $response->assertForbidden();
    }

    public function test_can_update_bookmark_name(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $data = [
            'name' => $this->faker()->name,
        ];

        $response = $this->putJson("/api/internal/bookmarks/{$bookmark->id}", $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('bookmarks', [
            'id' => $bookmark->id,
            'cabinet_id' => $bookmark->cabinet_id,
            'employee_id' => $bookmark->employee_id,
            'name' => $data['name'],
            'entity' => $bookmark->entity,
        ]);
    }

    public function test_cant_update_bookmark_in_other_cabinet(): void
    {
        $otherCabinetBookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $data = [
            'name' => $this->faker()->name,
        ];

        $response = $this->putJson("/api/internal/bookmarks/{$otherCabinetBookmark->id}", $data);

        $response->assertNotFound();

        $this->assertDatabaseHas('bookmarks', [
            'id' => $otherCabinetBookmark->id,
            'name' => $otherCabinetBookmark->name,
        ]);
    }

    public function test_cant_update_other_employee_bookmark(): void
    {
        $otherEmployeeBookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $data = [
            'name' => $this->faker()->name,
        ];

        $response = $this->putJson("/api/internal/bookmarks/{$otherEmployeeBookmark->id}", $data);

        $response->assertNotFound();

        $this->assertDatabaseHas('bookmarks', [
            'id' => $otherEmployeeBookmark->id,
            'name' => $otherEmployeeBookmark->name
        ]);
    }

    public function test_can_delete_bookmark(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $response = $this->deleteJson("/api/internal/bookmarks/{$bookmark->id}");

        $response->assertNoContent();

        $this->assertDatabaseMissing('bookmarks', ['id' => $bookmark->id]);
    }

    public function test_cant_delete_other_employee_bookmark(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $response = $this->deleteJson("/api/internal/bookmarks/{$bookmark->id}");

        $response->assertNotFound();

        $this->assertDatabaseHas('bookmarks', ['id' => $bookmark->id]);
    }

    public function test_cant_delete_bookmark_in_other_cabinet(): void
    {
        $bookmark = Bookmark::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->deleteJson("/api/internal/bookmarks/{$bookmark->id}");

        $response->assertNotFound();

        $this->assertDatabaseHas('bookmarks', ['id' => $bookmark->id]);
    }
}
