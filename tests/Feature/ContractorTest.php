<?php

namespace Tests\Feature;

use App\Enums\Api\Internal\FilterConditionEnum;
use App\Enums\Api\Internal\LegalEntityTaxation;
use App\Enums\Api\Internal\LegalEntityType;
use App\Enums\Api\Internal\ShowOnlyEnum;
use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Contractor;
use App\Models\ContractorAccount;
use App\Models\ContractorAddress;
use App\Models\ContractorDetail;
use App\Models\ContractorGroup;
use App\Models\ContractorGroups;
use App\Models\Department;
use App\Models\Employee;
use App\Models\Status;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ContractorTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_contractors_list(): void
    {
        // Arrange
        Contractor::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем контрагента для другого кабинета
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'deleted_at',
                        'created_at',
                        'updated_at',
                        'archived_at',
                        'cabinet_id',
                        'shared_access',
                        'employee_id',
                        'department_id',
                        'title',
                        'status_id',
                        'is_buyer',
                        'is_supplier',
                        'phone',
                        'fax',
                        'email',
                        'description',
                        'code',
                        'external_code',
                        'discounts_and_prices',
                        'discount_card_number',
                        'is_default',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(3, 'data');
    }

    public function test_cannot_access_other_cabinet_contractors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
            'filters' => [
                'type' => ['value' => 'invalid_type'],
                'shared_access' => ['value' => 'not-boolean'],
                'balance' => [
                    'from' => 'not-numeric',
                    'to' => 'not-numeric'
                ],
                'created_at' => [
                    'from' => 'invalid-date',
                    'to' => 'invalid-date'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
                'filters.type.value',
                'filters.shared_access.value',
                'filters.balance.from',
                'filters.balance.to',
                'filters.created_at.from',
                'filters.created_at.to'
            ]);
    }

    public function test_index_filter_code_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST123'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'OTHER456'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'code' => 'TEST123'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'value' => 'TEST123',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.code', 'TEST123');
    }

    public function test_index_filter_code_not_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST123'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'OTHER456'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'value' => 'TEST123',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_code_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST123'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_code_not_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => 'TEST123'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'code' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'code' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_address_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $contractor->id,
            'city' => 'test',
            'street' => 'Test Street',
            'house' => 123
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'address' => [
                    'value' => 'Test Street',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_address_not_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $contractor->id,
            'city' => 'test',
            'street' => 'Test Street',
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $secondContractor->id,
            'city' => 'Other',
            'street' => 'Road',
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'address' => [
                    'value' => 'Test Street',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_address_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $contractor->id,
            'postcode' => null,
            'country' => null,
            'city' => null,
            'region' => null,
            'house' => null,
            'office' => null,
            'other' => null,
            'street' => null,
            'comment' => null
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $secondContractor->id,
            'postcode' => '123456',
            'country' => 'Russia',
            'city' => 'test',
            'region' => 'Test Region',
            'street' => 'Street',
            'house' => '123',
            'office' => '456',
            'other' => 'Test Other',
            'comment' => 'Test Comment'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'address' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $data = $response->json('data');
        $contractorIds = collect($data)->pluck('id')->toArray();

        // Проверяем, что первый контрагент (с пустым адресом) присутствует
        $this->assertContains($contractor->id, $contractorIds);
    }

    public function test_index_filter_address_not_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $contractor->id,
            'city' => 'test',
            'street' => 'Street',
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorAddress::factory()->create([
            'contractor_id' => $secondContractor->id,
            'postcode' => null,
            'country' => null,
            'city' => null,
            'region' => null,
            'house' => null,
            'office' => null,
            'other' => null,
            'street' => null,
            'comment' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'address' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        $data = $response->json('data');
        $contractorIds = collect($data)->pluck('id')->toArray();

        // Проверяем, что первый контрагент (с непустым адресом) присутствует
        $this->assertContains($contractor->id, $contractorIds);
    }

    public function test_index_filter_type(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => LegalEntityType::LEGAL->value
        ]);
        $trueContractorId = $contractor->id;
        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => LegalEntityType::INDIVIDUAL->value

        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'type' => LegalEntityType::INDIVIDUAL->value
        ]);

        // Создаем контрагента с таким же типом в другом кабинете
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'title' => LegalEntityType::LEGAL->value
        ]);
        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'type' => LegalEntityType::LEGAL->value
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'type' => ['value' => LegalEntityType::LEGAL->value]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $trueContractorId)
            ->assertJsonPath('data.0.cabinet_id', $this->cabinet->id);
    }

    public function test_index_filter_balance(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorAccount::factory()->create([
            'contractor_id' => $contractor->id,
            'balance' => 2000
        ]);

        // Создаем контрагента с таким же балансом в другом кабинете
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        ContractorAccount::factory()->create([
            'contractor_id' => $otherContractor->id,
            'balance' => 2000
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'balance' => [
                    'from' => 1500,
                    'to' => 2500
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id)
            ->assertJsonPath('data.0.cabinet_id', $this->cabinet->id);
    }

    public function test_index_filter_created_at(): void
    {
        // Arrange
        $date = '2024-01-01 00:10:00';

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'created_at' => $date
        ]);

        // Создаем контрагента с такой же датой создания в другом кабинете
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'created_at' => $date
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'created_at' => [
                    'from' => '01.01.2024 00:10',
                    'to' => '01.01.2024 23:59'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_groups(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorGroup::factory()->create([
            'contractor_id' => $contractor->id,
            'group_id' => $group->id
        ]);

        // Создаем контрагента с такой же группой в другом кабинете
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        ContractorGroup::factory()->create([
            'contractor_id' => $otherContractor->id,
            'group_id' => $group->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'groups' => [
                    'value' => [$group->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_statuses(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        // Создаем контрагента с таким же статусом в другом кабинете
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'status_id' => $status->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$status->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_show_only(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => '2024-01-01 00:10:00'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'archived_at' => null
        ]);

        // Создаем контрагента с таким же статусом архивации в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'archived_at' => '2024-01-01 00:10:00'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'show_only' => ['value' => ShowOnlyEnum::ARCHIVED->value]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id)
            ->assertJsonPath('data.0.archived_at', '2024-01-01 00:10:00');
    }

    public function test_index_filter_phone_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '+79001234567'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '+***********'
        ]);

        // Создаем контрагента с таким же телефоном в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'phone' => '+79001234567'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'value' => '+79001234567',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.phone', '+79001234567')
            ->assertJsonPath('data.0.cabinet_id', $this->cabinet->id);
    }

    public function test_index_filter_phone_not_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '+79001234567'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '+***********'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'value' => '+79001234567',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_phone_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => null
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '+79001234567'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_phone_not_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => '+79001234567'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'phone' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'phone' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_email_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>'
        ]);

        // Создаем контрагента с таким же email в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'email' => '<EMAIL>'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'value' => '<EMAIL>',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.email', '<EMAIL>')
            ->assertJsonPath('data.0.cabinet_id', $this->cabinet->id);
    }

    public function test_index_filter_email_not_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'value' => '<EMAIL>',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_email_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => null
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_email_not_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => '<EMAIL>'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'email' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'email' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_discount_card_number_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => 'CARD123'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => 'CARD456'
        ]);

        // Создаем контрагента с таким же номером карты в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'discount_card_number' => 'CARD123'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'discount_card_number' => [
                    'value' => 'CARD123',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.discount_card_number', 'CARD123');
    }

    public function test_index_filter_discount_card_number_not_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => 'CARD123'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => 'CARD456'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'discount_card_number' => [
                    'value' => 'CARD123',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_discount_card_number_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => null
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => 'CARD123'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'discount_card_number' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_discount_card_number_not_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => 'CARD123'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'discount_card_number' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'discount_card_number' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_inn_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'inn' => '*********0'
        ]);

        // Создаем контрагента с таким же ИНН в другом кабинете
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);
        ContractorDetail::factory()->create([
            'contractor_id' => $otherContractor->id,
            'inn' => '*********0'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'value' => '*********0',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_inn_not_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'inn' => '*********0'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        ContractorDetail::factory()->create([
            'contractor_id' => $secondContractor->id,
            'inn' => '0987654321'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'value' => '*********0',
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_inn_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'inn' => null
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        ContractorDetail::factory()->create([
            'contractor_id' => $secondContractor->id,
            'inn' => '0987654321'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_inn_not_empty_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'inn' => '*********0'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
        ContractorDetail::factory()->create([
            'contractor_id' => $secondContractor->id,
            'inn' => null
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'inn' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_groups_in_condition(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorGroup::factory()->create([
            'group_id' => $group->id,
            'contractor_id' => $contractor->id
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'groups' => [
                    'value' => [$group->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_groups_not_in_condition(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorGroup::factory()->create([
            'group_id' => $group->id,
            'contractor_id' => $contractor->id
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'groups' => [
                    'value' => [$group->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);
        foreach ($response->json('data') as $item) {
            $this->assertNotEquals($contractor->id, $item['id']);
        }
    }

    public function test_index_filter_groups_empty_condition(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorGroup::factory()->create([
            'group_id' => $group->id,
            'contractor_id' => $secondContractor->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'groups' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_groups_not_empty_condition(): void
    {
        // Arrange
        $group = ContractorGroups::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        ContractorGroup::factory()->create([
            'group_id' => $group->id,
            'contractor_id' => $contractor->id
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'groups' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_statuses_in_condition(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$status->id],
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_statuses_not_in_condition(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'value' => [$status->id],
                    'condition' => FilterConditionEnum::NOT_IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $secondContractor->id);
    }

    public function test_index_filter_statuses_empty_condition(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => FilterConditionEnum::EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(0, 'data');
    }

    public function test_index_filter_statuses_not_empty_condition(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'status_id' => $status->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'statuses' => [
                    'condition' => FilterConditionEnum::NOT_EMPTY->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_kpp_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'kpp' => '*********'
        ]);

        $secondContractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $secondContractor->id,
            'kpp' => '987654321'
        ]);

        // Создаем контрагента с таким же КПП в другом кабинете
        $otherContractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $otherContractor->id,
            'kpp' => '*********'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'kpp' => [
                    'value' => '*********',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_index_filter_title_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Company'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Other Company'
        ]);

        // Создаем контрагента с таким же названием в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'title' => 'Test Company'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'title' => [
                    'value' => 'Test Company',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.title', 'Test Company');
    }

    public function test_index_filter_shared_access_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'shared_access' => true
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'shared_access' => false
        ]);

        // Создаем контрагента с таким же shared_access в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'shared_access' => true
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'shared_access' => [
                    'value' => true,
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.shared_access', true);
    }

    public function test_index_filter_search_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Search'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Other Title'
        ]);

        // Создаем контрагента с таким же текстом поиска в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'title' => 'Test Search'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'Test Search',
                    'condition' => FilterConditionEnum::IN->value
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.title', 'Test Search');
    }

    public function test_index_filter_updated_at_in_condition(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => '2024-01-01 00:10:00'
        ]);

        Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'updated_at' => '2024-01-02 00:00:00'
        ]);

        // Создаем контрагента с такой же датой обновления в другом кабинете
        Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'updated_at' => '2024-01-01 00:10:00'
        ]);

        // Act
        $response = $this->getJson('/api/internal/contractors?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'updated_at' => [
                    'from' => '01.01.2024 00:00',
                    'to' => '01.01.2024 23:59'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.id', $contractor->id);
    }

    public function test_can_create_contractor_with_minimal_data(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => 'John',
                'lastname' => 'Doe',
                'patronymic' => 'Smith'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('contractors', [
            'id' => $response->json('id'),
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id
        ])
        ->assertDatabaseHas('contractor_details', [
            'contractor_id' => $response->json('id'),
            'firstname' => $data['detail']['firstname'],
            'lastname' => $data['detail']['lastname'],
            'patronymic' => $data['detail']['patronymic'],
        ]);
    }

    public function test_can_create_contractor_with_all_fields(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_buyer' => true,
            'is_supplier' => true,
            'phone' => '+79001234567',
            'fax' => '123456',
            'email' => '<EMAIL>',
            'description' => 'Test description',
            'code' => 'TEST123',
            'external_code' => 'EXT123',
            'discounts_and_prices' => 'Standard',
            'discount_card_number' => 'CARD123',
            'shared_access' => true,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'inn' => '*********0',
                'kpp' => '*********',
                'ogrn' => '*********0123',
                'okpo' => '*********0',
                'full_name' => 'Test Company Full Name',
                'taxation_type' => LegalEntityTaxation::osno->value,
            ],
            'address' => [
                'postcode' => '123456',
                'country' => 'Test Country',
                'region' => 'Test Region',
                'city' => 'Test City',
                'street' => 'Test Street',
                'house' => '123',
                'office' => '456',
                'other' => 'Additional info'
            ],
            'accounts' => [
                [
                    'bik' => '*********',
                    'payment_account' => '*********0*********0',
                    'correspondent_account' => '*********01234567',
                    'balance' => 1000.50,
                    'bank' => 'Test Bank',
                    'address' => 'Bank Address',
                    'is_main' => true
                ]
            ],
            'contacts' => [
                [
                    'full_name' => 'John Doe',
                    'position' => 'Manager',
                    'phone' => '+***********',
                    'email' => '<EMAIL>',
                    'comment' => 'Primary contact'
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $id = $response->json('id');

        $this->assertDatabaseHas('contractors', [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'is_buyer' => true,
            'is_supplier' => true,
            'phone' => '+79001234567',
            'fax' => '123456',
            'email' => '<EMAIL>',
            'description' => 'Test description',
            'code' => 'TEST123',
            'external_code' => 'EXT123',
            'discounts_and_prices' => 'Standard',
            'discount_card_number' => 'CARD123',
            'shared_access' => true,
        ]);

        $this->assertDatabaseHas('contractor_details', [
            'contractor_id' => $id,
            'type' => LegalEntityType::LEGAL->value,
            'inn' => '*********0',
            'kpp' => '*********',
            'ogrn' => '*********0123',
            'okpo' => '*********0',
            'full_name' => 'Test Company Full Name',
            'taxation_type' => LegalEntityTaxation::osno->value,
        ]);

        $this->assertDatabaseHas('contractor_addresses', [
            'contractor_id' => $id,
            'postcode' => '123456',
            'country' => 'Test Country',
            'region' => 'Test Region',
            'city' => 'Test City',
            'street' => 'Test Street',
            'house' => '123',
            'office' => '456',
            'other' => 'Additional info'
        ]);

        $this->assertDatabaseHas('contractor_accounts', [
            'contractor_id' => $id,
            'bik' => '*********',
            'payment_account' => '*********0*********0',
            'correspondent_account' => '*********01234567',
            'balance' => '0',
            'bank' => 'Test Bank',
            'address' => 'Bank Address',
            'is_main' => true
        ]);

        $this->assertDatabaseHas('contractor_contacts', [
            'contractor_id' => $id,
            'full_name' => 'John Doe',
            'position' => 'Manager',
            'phone' => '+***********',
            'email' => '<EMAIL>',
            'comment' => 'Primary contact'
        ]);
    }

    public function test_cannot_create_contractor_without_required_fields(): void
    {
        // Act
        $response = $this->postJson('/api/internal/contractors', []);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'title',
                'cabinet_id',
                'department_id',
                'employee_id',
                'detail.firstname',
                'detail.patronymic',
                'detail.lastname',
                'detail.type',
            ]);
    }

    public function test_cannot_create_contractor_with_invalid_data(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => 'invalid-uuid',
            'title' => '',
            'status_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'phone' => 'invalid-phone',
            'email' => 'invalid-email',
            'detail' => [
                'type' => 'invalid-type',
                'inn' => 'invalid-inn',
                'kpp' => 'invalid-kpp',
                'ogrn' => 'invalid-ogrn'
            ],
            'accounts' => [
                [
                    'payment_account' => 'invalid-account',
                    'correspondent_account' => 'invalid-account',
                    'balance' => 'not-a-number'
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'title',
                'status_id',
                'department_id',
                'employee_id',
                'phone',
                'email',
                'detail.type',
                'detail.inn',
                'detail.kpp',
                'detail.ogrn',
                'accounts.0.payment_account',
                'accounts.0.correspondent_account'
            ]);
    }

    public function test_cannot_create_contractor_in_other_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => 'John',
                'lastname' => 'Doe',
                'patronymic' => 'Smith'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_cannot_create_contractor_with_invalid_legal_entity_fields(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                // Отсутствует обязательное поле full_name для юр. лица
                'inn' => '*********0',
                'kpp' => '*********'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['detail.full_name']);
    }

    public function test_cannot_create_contractor_with_invalid_individual_fields(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                // Отсутствуют обязательные поля firstname, lastname, patronymic для физ. лица
                'inn' => '*********0'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'detail.firstname',
                'detail.lastname',
                'detail.patronymic'
            ]);
    }

    public function test_cannot_create_contractor_with_status_from_other_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => 'John',
                'lastname' => 'Doe',
                'patronymic' => 'Smith'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_contractor_with_department_from_other_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherDepartment = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $otherDepartment->id,
            'employee_id' => $this->employee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => 'John',
                'lastname' => 'Doe',
                'patronymic' => 'Smith'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_create_contractor_with_employee_from_other_cabinet(): void
    {
        // Arrange
        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $otherEmployee = Employee::factory()->create();

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'title' => 'Test Contractor',
            'status_id' => $status->id,
            'department_id' => $this->department->id,
            'employee_id' => $otherEmployee->id,
            'detail' => [
                'type' => LegalEntityType::INDIVIDUAL->value,
                'firstname' => 'John',
                'lastname' => 'Doe',
                'patronymic' => 'Smith'
            ]
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_update_contractor(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
        ]);

        $status = Status::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $updateData = [
            'title' => 'Updated Company Name',
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $status->id,
            'phone' => '+79001234567',
            'email' => '<EMAIL>',
            'code' => 'UPD123',
            'external_code' => 'EXT456',
            'description' => 'Updated description',
            'discount_card_number' => 'NEWCARD123',
            'shared_access' => true,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'inn' => '*********0',
                'kpp' => '*********',
                'ogrn' => '*********0123',
                'okpo' => '*********0',
                'full_name' => 'Updated Company Full Name',
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/contractors/{$contractor->id}", $updateData);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('contractors', [
            'id' => $contractor->id,
            'title' => 'Updated Company Name',
            'phone' => '+79001234567',
            'email' => '<EMAIL>',
            'code' => 'UPD123',
            'external_code' => 'EXT456',
            'description' => 'Updated description',
            'discount_card_number' => 'NEWCARD123',
            'shared_access' => true
        ]);

        $this->assertDatabaseHas('contractor_details', [
            'contractor_id' => $contractor->id,
            'inn' => '*********0',
            'kpp' => '*********',
            'ogrn' => '*********0123',
            'okpo' => '*********0',
            'full_name' => 'Updated Company Full Name'
        ]);
    }

    public function test_cannot_update_contractor_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinet = Cabinet::factory()->create();
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $otherCabinet->id
        ]);

        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $this->faker()->uuid,
            'title' => 'Updated Company',
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'full_name' => 'Updated Company Full Name',
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/contractors/{$contractor->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_update_contractor_with_invalid_data(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ]);

        $invalidData = [
            'title' => '', // пустое название
            'cabinet_id' => 'invalid-uuid',
            'department_id' => 'invalid-uuid',
            'employee_id' => 'invalid-uuid',
            'phone' => 'invalid-phone',
            'email' => 'invalid-email',
            'detail' => [
                'type' => 'invalid-type',
                'inn' => 'invalid-inn',
                'kpp' => 'invalid-kpp',
                'ogrn' => 'invalid-ogrn'
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/contractors/{$contractor->id}", $invalidData);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'title',
                'department_id',
                'employee_id',
                'phone',
                'email',
                'detail.type',
                'detail.inn',
                'detail.kpp',
                'detail.ogrn',
                'detail.firstname',
                'detail.patronymic',
                'detail.lastname'
            ]);
    }

    public function test_cannot_update_non_existent_contractor(): void
    {
        // Act
        $updateData = [
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $this->faker()->uuid,
            'title' => 'Updated Company',
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'full_name' => 'Updated Company Full Name',
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ];

        $response = $this->putJson("/api/internal/contractors/" . $this->faker->uuid(), $updateData);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_update_contractor_with_status_from_other_cabinet(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id
        ]);

        ContractorDetail::factory()->create([
            'contractor_id' => $contractor->id,
            'type' => LegalEntityType::LEGAL->value,
            'full_name' => 'Test Company'
        ]);

        $otherCabinetStatus = Status::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        $updateData = [
            'title' => 'Updated Company Name',
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id,
            'employee_id' => $this->employee->id,
            'status_id' => $otherCabinetStatus->id,
            'detail' => [
                'type' => LegalEntityType::LEGAL->value,
                'full_name' => 'Updated Company Name',
                'taxation_type' => LegalEntityTaxation::osno->value
            ]
        ];

        // Act
        $response = $this->putJson("/api/internal/contractors/{$contractor->id}", $updateData);

        // Assert
        $response->assertNotFound();
    }

    public function test_can_show_contractor(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/contractors/{$contractor->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'deleted_at',
                'created_at',
                'updated_at',
                'archived_at',
                'cabinet_id',
                'shared_access',
                'employee_id',
                'department_id',
                'title',
                'status_id',
                'is_buyer',
                'is_supplier',
                'phone',
                'fax',
                'email',
                'description',
                'code',
                'external_code',
                'discounts_and_prices',
                'discount_card_number',
                'is_default'
            ]);

        $this->assertEquals($contractor->id, $response->json('id'));
        $this->assertEquals($this->cabinet->id, $response->json('cabinet_id'));
    }

    public function test_cannot_show_contractor_from_other_cabinet(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/contractors/{$contractor->id}");

        // Assert
        $response->assertNotFound();
    }

    public function test_cannot_show_non_existent_contractor(): void
    {
        // Act
        $response = $this->getJson("/api/internal/contractors/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_contractor(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/contractors/{$contractor->id}");

        // Assert
        $response->assertStatus(204);
        $this->assertSoftDeleted('contractors', ['id' => $contractor->id]);
    }

    public function test_cannot_delete_contractor_from_other_cabinet(): void
    {
        // Arrange
        $contractor = Contractor::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/contractors/{$contractor->id}");

        // Assert
        $response->assertNotFound();
        $this->assertDatabaseHas('contractors', ['id' => $contractor->id]);
    }

    public function test_cannot_delete_non_existent_contractor(): void
    {
        // Act
        $response = $this->deleteJson("/api/internal/contractors/" . $this->faker->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_bulk_copy_contractors(): void
    {
        // Arrange
        $contractors = [];
        for ($i = 0; $i < 3; $i++) {
            $contractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'title' => "Contractor for copy"
            ]);
        }

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => collect($contractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/bulk-copy', $data);

        // Assert
        $response->assertStatus(204);

        $this->assertEquals(
            6,
            Contractor::query()
                ->where('title', 'Contractor for copy')
                ->where('cabinet_id', $this->cabinet->id)
                ->count()
        );

    }

    public function test_cannot_bulk_copy_contractors_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetContractors = [];
        for ($i = 0; $i < 3; $i++) {
            $otherCabinetContractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->otherCabinet->id,
                'title' => "Test cannot copy"
            ]);
        }

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => collect($otherCabinetContractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/bulk-copy', $data);

        // Assert
        $response->assertStatus(403);

        // Проверяем что новые записи не были созданы
        $this->assertEquals(
            0,
            Contractor::query()
                ->where('title', 'Test cannot copy')
                ->where('cabinet_id', $this->cabinet->id)
                ->count()
        );
    }

    public function test_can_bulk_delete_contractors(): void
    {
        // Arrange
        $contractors = [];
        for ($i = 0; $i < 3; $i++) {
            $contractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->cabinet->id
            ]);
        }

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => collect($contractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->deleteJson('/api/internal/contractors/bulk-delete', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($contractors as $contractor) {
            $this->assertSoftDeleted('contractors', [
                'id' => $contractor->id
            ]);
        }
    }

    public function test_cannot_bulk_delete_contractors_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetContractors = [];
        for ($i = 0; $i < 3; $i++) {
            $otherCabinetContractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->otherCabinet->id
            ]);
        }

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => collect($otherCabinetContractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->deleteJson('/api/internal/contractors/bulk-delete', $data);

        // Assert
        $response->assertStatus(403);

        foreach ($otherCabinetContractors as $contractor) {
            $this->assertDatabaseHas('contractors', [
                'id' => $contractor->id,
                'deleted_at' => null
            ]);
        }
    }

    public function test_can_archive_contractors(): void
    {
        // Arrange
        $contractors = [];
        for ($i = 0; $i < 3; $i++) {
            $contractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'archived_at' => null
            ]);
        }

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => collect($contractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/archive', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($contractors as $contractor) {
            $this->assertDatabaseHas('contractors', [
                'id' => $contractor->id,
            ]);
            $this->assertNotNull(Contractor::find($contractor->id)->archived_at);
        }
    }

    public function test_cannot_archive_contractors_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetContractors = [];
        for ($i = 0; $i < 3; $i++) {
            $otherCabinetContractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->otherCabinet->id,
                'archived_at' => null
            ]);
        }

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => collect($otherCabinetContractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/archive', $data);

        // Assert
        $response->assertStatus(403);

        foreach ($otherCabinetContractors as $contractor) {
            $this->assertDatabaseHas('contractors', [
                'id' => $contractor->id,
                'archived_at' => null
            ]);
        }
    }

    public function test_can_unarchive_contractors(): void
    {
        // Arrange
        $contractors = [];
        for ($i = 0; $i < 3; $i++) {
            $contractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->cabinet->id,
                'archived_at' => now()
            ]);
        }

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => collect($contractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/unarchive', $data);

        // Assert
        $response->assertStatus(204);

        foreach ($contractors as $contractor) {
            $this->assertDatabaseHas('contractors', [
                'id' => $contractor->id,
                'archived_at' => null
            ]);
        }
    }

    public function test_cannot_unarchive_contractors_from_other_cabinet(): void
    {
        // Arrange
        $otherCabinetContractors = [];
        for ($i = 0; $i < 3; $i++) {
            $otherCabinetContractors[] = Contractor::factory()->create([
                'cabinet_id' => $this->otherCabinet->id,
                'archived_at' => now()
            ]);
        }

        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'ids' => collect($otherCabinetContractors)->pluck('id')->toArray()
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/unarchive', $data);

        // Assert
        $response->assertStatus(403);

        foreach ($otherCabinetContractors as $contractor) {
            $this->assertDatabaseHas('contractors', [
                'id' => $contractor->id
            ]);
            $this->assertNotNull(Contractor::find($contractor->id)->archived_at);
        }
    }

    public function test_bulk_operations_validation_errors(): void
    {
        // Тест с пустыми данными
        $emptyData = [];
        $response = $this->postJson('/api/internal/contractors/bulk-copy', $emptyData);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'ids']);

        // Тест с неверным форматом UUID
        $invalidUuidData = [
            'cabinet_id' => 'not-a-uuid',
            'ids' => ['also-not-a-uuid']
        ];
        $response = $this->postJson('/api/internal/contractors/bulk-copy', $invalidUuidData);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['cabinet_id', 'ids.0']);

        // Тест с пустым массивом ids
        $emptyIdsData = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => []
        ];
        $response = $this->postJson('/api/internal/contractors/bulk-copy', $emptyIdsData);
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['ids']);
    }

    public function test_cannot_bulk_copy_non_existent_contractors(): void
    {
        // Arrange
        $nonExistentIds = [
            $this->faker->uuid(),
            $this->faker->uuid()
        ];

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $nonExistentIds
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/bulk-copy', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_bulk_delete_non_existent_contractors(): void
    {
        // Arrange
        $nonExistentIds = [
            $this->faker->uuid(),
            $this->faker->uuid()
        ];

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $nonExistentIds
        ];

        // Act
        $response = $this->deleteJson('/api/internal/contractors/bulk-delete', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_archive_non_existent_contractors(): void
    {
        // Arrange
        $nonExistentIds = [
            $this->faker->uuid(),
            $this->faker->uuid()
        ];

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $nonExistentIds
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/archive', $data);

        // Assert
        $response->assertStatus(404);
    }

    public function test_cannot_unarchive_non_existent_contractors(): void
    {
        // Arrange
        $nonExistentIds = [
            $this->faker->uuid(),
            $this->faker->uuid()
        ];

        $data = [
            'cabinet_id' => $this->cabinet->id,
            'ids' => $nonExistentIds
        ];

        // Act
        $response = $this->postJson('/api/internal/contractors/unarchive', $data);

        // Assert
        $response->assertStatus(404);
    }
}
