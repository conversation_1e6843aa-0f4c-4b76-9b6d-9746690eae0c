<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\CabinetSettings;
use App\Models\CabinetEmployee;
use App\Models\ProductSizNames;
use App\Models\ProductSizTypes;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductSizTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    private User $user;
    private Cabinet $cabinet;
    private Employee $employee;
    private Department $department;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $this->user = User::factory()->create();
        $this->actingAs($this->user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $this->user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $this->user->id,
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_siz_list(): void
    {
        // Arrange
        foreach (ProductSizNames::factory()->count(10)->create() as $sizName) {
            ProductSizTypes::factory()->create(
                ['product_siz_name_id' => $sizName->id]
            );
        }
        // Act
        $response = $this->getJson('/api/internal/product/siz');

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure([
                'sizNames' => [
                    '*' => [
                        'id',
                        'name',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                    ]
                ],
                'sizTypes' => [
                    '*' => [
                        'id',
                        'title',
                        'code',
                        'product_siz_name_id',
                        'deleted_at',
                        'created_at',
                        'updated_at'
                    ]
                ]
            ]);

        $this->assertCount(10, $response->json('sizNames'));
        $this->assertCount(10, $response->json('sizTypes'));
    }

    public function test_can_get_empty_siz_list(): void
    {
        // Act
        $response = $this->getJson('/api/internal/product/siz');

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure([
                'sizNames' => [],
                'sizTypes' => []
            ]);

        $this->assertEmpty($response->json('sizNames'));
        $this->assertEmpty($response->json('sizTypes'));
    }
}
