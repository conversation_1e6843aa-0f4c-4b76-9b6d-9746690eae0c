<?php

namespace Tests\Feature;

use App\Models\Cabinet;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\SalesChannel;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DepartmentTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
    }

    public function test_can_get_departments_list(): void
    {
        // Arrange
        Department::factory()->count(3)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Создаем департамент для другого кабинета
        Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/departments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 15
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'deleted_at',
                        'cabinet_id',
                        'name',
                        'sorting',
                        'is_default',
                        'sales_channel_id',
                        'is_common',
                        'sort',
                    ]
                ],
                'meta' => [
                    'current_page',
                    'per_page',
                    'last_page',
                    'total'
                ]
            ]);

        $response->assertJsonCount(3, 'data');
    }

    public function test_cannot_access_other_cabinet_departments(): void
    {
        // Act
        $response = $this->getJson('/api/internal/departments?' . http_build_query([
            'cabinet_id' => $this->otherCabinet->id
        ]));

        // Assert
        $response->assertStatus(403);
    }

    public function test_index_validation_errors(): void
    {
        // Act
        $response = $this->getJson('/api/internal/departments?' . http_build_query([
            'cabinet_id' => 'invalid-uuid',
            'page' => 0,
            'per_page' => 101,
            'sortDirection' => 'invalid',
        ]));

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'page',
                'per_page',
                'sortDirection',
            ]);
    }

    public function test_index_filter_search(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Department'
        ]);

        Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Other Department'
        ]);

        // Создаем департамент с таким же названием в другом кабинете
        Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Department'
        ]);

        // Act
        $response = $this->getJson('/api/internal/departments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'filters' => [
                'search' => [
                    'value' => 'Test'
                ]
            ]
        ]));

        // Assert
        $response->assertStatus(200);

        foreach ($response->json('data') as $item) {
            $this->assertStringContainsString('Test', $item['name']);
        }
    }

    public function test_can_create_department(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'New Test Department',
            'sorting' => 10
        ];

        // Act
        $response = $this->postJson('/api/internal/departments', $data);

        // Assert
        $response->assertStatus(201)
            ->assertJsonStructure(['id']);

        $this->assertDatabaseHas('departments', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'New Test Department',
            'sorting' => 10
        ]);
    }

    public function test_create_validation_errors(): void
    {
        // Act
        $response = $this->postJson('/api/internal/departments', [
            'cabinet_id' => 'invalid-uuid',
            'name' => '',
            'sorting' => 'not-integer'
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors([
                'cabinet_id',
                'name',
                'sorting'
            ]);
    }

    public function test_cannot_create_department_for_other_cabinet(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'name' => 'Test Department',
            'sorting' => 10
        ];

        // Act
        $response = $this->postJson('/api/internal/departments', $data);

        // Assert
        $response->assertStatus(403);
    }

    public function test_can_show_department(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Department',
            'sorting' => 5
        ]);

        // Act
        $response = $this->getJson("/api/internal/departments/{$department->id}");

        // Assert
        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'created_at',
                'updated_at',
                'deleted_at',
                'cabinet_id',
                'name',
                'sorting',
                'is_default',
                'sales_channel_id',
                'is_common',
                'sort'
            ])
            ->assertJsonPath('id', $department->id)
            ->assertJsonPath('name', 'Test Department')
            ->assertJsonPath('cabinet_id', $this->cabinet->id);
    }

    public function test_cannot_show_department_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->getJson("/api/internal/departments/{$department->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_update_department(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Name',
            'sorting' => 5
        ]);

        $data = [
            'name' => 'Updated Name',
            'sorting' => 15
        ];

        // Act
        $response = $this->putJson("/api/internal/departments/{$department->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('departments', [
            'id' => $department->id,
            'name' => 'Updated Name',
            'sorting' => 15
        ]);
    }

    public function test_update_validation_errors(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/departments/{$department->id}", [
            'name' => '',
            'sorting' => 'not-integer'
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'sorting']);
    }

    public function test_cannot_update_department_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->putJson("/api/internal/departments/{$department->id}", [
            'name' => 'Updated Name',
            'sorting' => 10
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_can_delete_department(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/departments/{$department->id}");

        // Assert
        $response->assertStatus(204);

        $this->assertSoftDeleted('departments', [
            'id' => $department->id
        ]);
    }

    public function test_cannot_delete_department_from_other_cabinet(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->otherCabinet->id
        ]);

        // Act
        $response = $this->deleteJson("/api/internal/departments/{$department->id}");

        // Assert
        $response->assertStatus(404);
    }

    public function test_index_with_sorting(): void
    {
        // Arrange
        Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'A Department',
            'sorting' => 1
        ]);

        Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Z Department',
            'sorting' => 2
        ]);

        // Act - сортировка по убыванию
        $response = $this->getJson('/api/internal/departments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'sortField' => 'name',
            'sortDirection' => 'desc'
        ]));

        // Assert
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertEquals('Z Department', $data[0]['name']);
        $this->assertEquals('A Department', $data[1]['name']);
    }

    public function test_index_with_pagination(): void
    {
        // Arrange
        Department::factory()->count(5)->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        // Act
        $response = $this->getJson('/api/internal/departments?' . http_build_query([
            'cabinet_id' => $this->cabinet->id,
            'page' => 1,
            'per_page' => 2
        ]));

        // Assert
        $response->assertStatus(200)
            ->assertJsonCount(2, 'data')
            ->assertJsonPath('meta.current_page', 1)
            ->assertJsonPath('meta.per_page', 2)
            ->assertJsonPath('meta.total', 5)
            ->assertJsonPath('meta.last_page', 3);
    }

    public function test_show_nonexistent_department(): void
    {
        // Act
        $response = $this->getJson('/api/internal/departments/nonexistent-id');

        // Assert
        $response->assertStatus(404);
    }

    public function test_update_nonexistent_department(): void
    {
        // Act
        $response = $this->putJson('/api/internal/departments/' . $this->faker()->uuid(), [
            'name' => 'Updated Name',
            'sorting' => 10
        ]);

        // Assert
        $response->assertStatus(404);
    }

    public function test_delete_nonexistent_department(): void
    {
        // Act
        $response = $this->deleteJson('/api/internal/departments/' . $this->faker()->uuid());

        // Assert
        $response->assertStatus(404);
    }

    public function test_create_department_with_sales_channel(): void
    {
        // Arrange
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Sales Department',
            'sorting' => 10,
            'sales_channel_id' => SalesChannel::factory()->create(
                ['cabinet_id' => $this->cabinet->id]
            )->id
        ];

        // Act
        $response = $this->postJson('/api/internal/departments', $data);

        // Assert
        $response->assertStatus(201);

        $this->assertDatabaseHas('departments', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Sales Department',
            'sales_channel_id' => $data['sales_channel_id']
        ]);
    }

    public function test_update_department_with_sales_channel(): void
    {
        // Arrange
        $department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Original Department',
            'sorting' => 5
        ]);

        $salesChannelId = SalesChannel::factory()->create(
            ['cabinet_id' => $this->cabinet->id]
        )->id;

        $data = [
            'name' => 'Updated Department',
            'sorting' => 15,
            'sales_channel_id' => $salesChannelId
        ];

        // Act
        $response = $this->putJson("/api/internal/departments/{$department->id}", $data);

        // Assert
        $response->assertStatus(204);

        $this->assertDatabaseHas('departments', [
            'id' => $department->id,
            'name' => 'Updated Department',
            'sales_channel_id' => $salesChannelId
        ]);
    }

    public function test_create_department_with_invalid_sales_channel_id(): void
    {
        // Act
        $response = $this->postJson('/api/internal/departments', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Department',
            'sorting' => 10,
            'sales_channel_id' => 'invalid-uuid'
        ]);

        // Assert
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['sales_channel_id']);
    }

    public function test_create_department_with_negative_sorting(): void
    {
        // Act
        $response = $this->postJson('/api/internal/departments', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Department',
            'sorting' => -5
        ]);

        // Assert
        $response->assertStatus(201);

        $this->assertDatabaseHas('departments', [
            'cabinet_id' => $this->cabinet->id,
            'name' => 'Test Department',
            'sorting' => -5
        ]);
    }
}
