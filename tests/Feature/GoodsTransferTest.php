<?php


use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\CabinetEmployee;
use App\Models\CabinetSettings;
use App\Models\Department;
use App\Models\Employee;
use App\Models\GoodsTransfer;
use App\Models\LegalEntity;
use App\Models\Status;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithFaker;
use Random\RandomException;
use Tests\TestCase;

class GoodsTransferTest extends TestCase
{
    use WithFaker;
    use DatabaseTransactions;

    private Employee $employee;
    private Cabinet $cabinet;
    private Cabinet $otherCabinet;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');

        $this->employee = Employee::factory()->create([
            'user_id' => $user->id,
        ]);

        $this->cabinet = Cabinet::factory()->create([
            'user_id' => $user->id,
        ]);

        CabinetSettings::factory()->create([
            'cabinet_id' => $this->cabinet->id
        ]);

        $this->otherCabinet = Cabinet::factory()->create();

        CabinetEmployee::factory()->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id
        ]);
        $this->department = Department::factory()->create([
            'cabinet_id' => $this->cabinet->id,
        ]);
    }

    public function test_can_get_goods_transfers(): void
    {
        GoodsTransfer::factory()->count(10)->create([
            'employee_id' => $this->employee->id,
            'cabinet_id' => $this->cabinet->id,
            'department_id' => $this->department->id
        ]);

        GoodsTransfer::factory()->count(2)->create([
            'cabinet_id' => $this->cabinet->id,
        ]);

        GoodsTransfer::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/goods-transfers?' . http_build_query([
                'cabinet_id' => $this->cabinet->id,
            ]));

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'created_at',
                        'updated_at',
                        'cabinet_id',
                        'employee_id',
                        'department_id',
                        'is_common',
                        'number',
                        'date_from',
                        'status_id',
                        'held',
                        'legal_entity_id',
                        'to_warehouse_id',
                        'from_warehouse_id',
                        'currency_id',
                        'currency_value',
                        'comment',
                        'overhead_cost',
                        'total_price'
                    ]
                ],
                'meta'
            ])
            ->assertJsonCount(10, 'data');

        foreach ($response->json('data') as $item) {
            $this->assertEquals($this->cabinet->id, $item['cabinet_id']);
            $this->assertEquals($this->employee->id, $item['employee_id']);
        }
    }

    public function test_cannot_get_goods_transfers_in_other_cabinet(): void
    {
        GoodsTransfer::factory()->count(10)->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson('/api/internal/goods-transfers?' . http_build_query([
                'cabinet_id' => $this->otherCabinet->id,
            ]));

        $response->assertForbidden()
            ->assertJsonMissing(['data']);
    }

    public function test_can_view_goods_transfer(): void
    {
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $response = $this->getJson("/api/internal/goods-transfers/{$transfer->id}");

        $response->assertOk()
            ->assertJson([
                'id' => $transfer->id,
                'cabinet_id' => $transfer->cabinet_id,
                'employee_id' => $transfer->employee_id,
                'department_id' => $transfer->department_id,
                'is_common' => $transfer->is_common,
                'number' => $transfer->number,
            ]);
    }

    public function test_cant_view_transfer_in_other_cabinet(): void
    {
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->getJson("/api/internal/goods-transfers/{$transfer->id}");

        $response->assertNotFound();
    }

    /**
     * @throws RandomException
     */
    public function test_can_store_goods_transfer(): void
    {
        $data = [
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'is_common' => true,
            'number' => (string)random_int(1, 100000),
            'date_from' => now()->format('Y-m-d'),
            'status_id' => Status::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'held' => $this->faker()->boolean(),
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'to_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'from_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_value' => $this->faker()->randomFloat(2, 0, 100),
            'comment' => $this->faker()->text(),
            'overhead_cost' => $this->faker()->randomFloat(2, 0, 100),
            'total_price' => $this->faker()->randomFloat(2, 0, 100)
        ];

        $response = $this->postJson("/api/internal/goods-transfers", $data);

        $response->assertCreated();

        $this->assertDatabaseHas(
            'goods_transfers',
            [
                'id' => $response->json('id'),
                'cabinet_id' => $data['cabinet_id'],
                'employee_id' => $data['employee_id'],
                'department_id' => $data['department_id'],
                'is_common' => $data['is_common'],
                'status_id' => $data['status_id'],
                'held' => $data['held'],
                'legal_entity_id' => $data['legal_entity_id'],
                'to_warehouse_id' => $data['to_warehouse_id'],
                'from_warehouse_id' => $data['from_warehouse_id'],
                'currency_id' => $data['currency_id'],
                'currency_value' => $data['currency_value'],
                'comment' => $data['comment'],
                'overhead_cost' => $data['overhead_cost'],
                'total_price' => $data['total_price']
            ]
        );
    }

    public function test_cant_store_transfer_in_other_cabinet(): void
    {
        $data = [
            'cabinet_id' => $this->otherCabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'is_common' => true,
            'number' => (string)random_int(1, 100000),
            'date_from' => now()->format('Y-m-d'),
            'status_id' => Status::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'held' => $this->faker()->boolean(),
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'to_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'from_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->otherCabinet->id])->id,
            'currency_value' => $this->faker()->randomFloat(2, 0, 100),
            'comment' => $this->faker()->text(),
            'overhead_cost' => $this->faker()->randomFloat(2, 0, 100),
            'total_price' => $this->faker()->randomFloat(2, 0, 100)
        ];

        $response = $this->postJson("/api/internal/goods-transfers", $data);

        $response->assertForbidden();
    }

    public function test_can_update_transfer(): void
    {
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
        ]);

        $data = [
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'to_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'from_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'comment' => $this->faker()->text(),
        ];

        $response = $this->putJson("/api/internal/goods-transfers/{$transfer->id}", $data);

        $response->assertNoContent();

        $this->assertDatabaseHas('goods_transfers', [
            'id' => $transfer->id,
            'cabinet_id' => $transfer->cabinet_id,
            'employee_id' => $transfer->employee_id,
            'department_id' => $transfer->department_id,
            'comment' => $data['comment']
        ]);
    }

    public function test_cant_update_transfer_in_other_cabinet(): void
    {
        $otherCabinetTransfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $data = [
            'employee_id' => $this->employee->id,
            'department_id' => $this->department->id,
            'legal_entity_id' => LegalEntity::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'to_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'from_warehouse_id' => Warehouse::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'currency_id' => CabinetCurrency::factory()->create(['cabinet_id' => $this->cabinet->id])->id,
            'comment' => $this->faker()->text(),
        ];

        $response = $this->putJson("/api/internal/goods-transfers/{$otherCabinetTransfer->id}", $data);

        $response->assertNotFound();

        $this->assertDatabaseHas('goods_transfers', [
            'id' => $otherCabinetTransfer->id,
            'comment' => $otherCabinetTransfer->comment,
        ]);
    }

    public function test_can_delete_transfer(): void
    {
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->cabinet->id,
            'employee_id' => $this->employee->id,
        ]);

        $response = $this->deleteJson("/api/internal/goods-transfers/{$transfer->id}");

        $response->assertNoContent();

        $this->assertDatabaseMissing('goods_transfers', ['id' => $transfer->id]);
    }

    public function test_cant_delete_transfer_in_other_cabinet(): void
    {
        $transfer = GoodsTransfer::factory()->create([
            'cabinet_id' => $this->otherCabinet->id,
        ]);

        $response = $this->deleteJson("/api/internal/goods-transfers/{$transfer->id}");

        $response->assertNotFound();

        $this->assertDatabaseHas('goods_transfers', ['id' => $transfer->id]);
    }
}
