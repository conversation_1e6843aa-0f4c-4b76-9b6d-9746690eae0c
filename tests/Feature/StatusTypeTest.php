<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\StatusType;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class StatusTypeTest extends TestCase
{
    use RefreshDatabase;
    use WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Создаем и аутентифицируем пользователя
        $user = User::factory()->create();
        $this->actingAs($user, 'sanctum');
    }

    public function test_can_get_status_types_list(): void
    {
        // Arrange
        $statusTypes = StatusType::factory()->count(3)->create([
            'name' => fn () => $this->faker->unique()->word()
        ]);

        // Act
        $response = $this->getJson('/api/internal/status-types');

        // Assert
        $response->assertOk()
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'name',
                    'created_at',
                    'updated_at'
                ]
            ]);
    }
}
