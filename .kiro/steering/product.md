# Product Overview

**M1 Laravel Backend** - LOG WMS (Warehouse Management System)

A Laravel-based backend API for warehouse management operations. The system appears to handle logistics and warehouse operations with integrations to external services like Ozon, Wildberries, and DaData for Russian market operations.

## Key Features
- Warehouse management system functionality
- API-first architecture with Scramble documentation
- Integration with Russian e-commerce platforms (Ozon, Wildberries)
- Address validation via DaData service
- Excel import/export capabilities
- Multi-language support (Russian localization)

## Target Market
Russian logistics and e-commerce operations, focusing on warehouse management and marketplace integrations.