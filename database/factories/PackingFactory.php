<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Department;
use App\Models\MeasurementUnit;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Packing>
 */
class PackingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cabinet = Cabinet::factory()->create();

        return [
            'cabinet_id' => $cabinet->id,
            'name' => $this->faker->word,
            'description' => $this->faker->text,
            'length' => $this->faker->randomFloat(2, 0, 999999),
            'width' => $this->faker->randomFloat(2, 0, 999999),
            'height' => $this->faker->randomFloat(2, 0, 999999),
            'measurement_unit_size_id' => MeasurementUnit::factory()->create([
                'cabinet_id' => $cabinet->id,
            ])->id,
            'weight' => $this->faker->randomFloat(2, 0, 999999),
            'measurement_unit_weight_id' => MeasurementUnit::factory()->create([
                'cabinet_id' => $cabinet->id
            ])->id,
            'volume' => $this->faker->randomFloat(2, 0, 999999),
            'measurement_unit_volume_id' => MeasurementUnit::factory()->create([
                'cabinet_id' => $cabinet->id
            ])->id,
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory()->create([
                'cabinet_id' => $cabinet->id
            ])->id
        ];
    }


}
