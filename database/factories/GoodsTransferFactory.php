<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\CabinetCurrency;
use App\Models\Department;
use App\Models\Employee;
use App\Models\LegalEntity;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GoodsTransfer>
 */
class GoodsTransferFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'department_id' => Department::factory(),
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'is_common' => $this->faker->boolean(),
            'number' => random_int(1, 100000),
            'date_from' => $this->faker->date,
            'status_id' => null,
            'held' => $this->faker->boolean(),
            'legal_entity_id' => LegalEntity::factory(),
            'to_warehouse_id' => Warehouse::factory(),
            'from_warehouse_id' => Warehouse::factory(),
            'currency_id' => CabinetCurrency::factory(),
            'currency_value' => $this->faker->randomFloat(2, 0, 100),
            'comment' => $this->faker->text(),
            'overhead_cost' => $this->faker->randomFloat(2, 0, 100),
            'total_price' => $this->faker->randomFloat(2, 0, 100)
        ];
    }
}
