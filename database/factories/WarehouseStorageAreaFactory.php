<?php

namespace Database\Factories;

use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseStorageArea>
 */
class WarehouseStorageAreaFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->word,
            'description' => $this->faker->text,
            'temperature_from' => $this->faker->randomFloat(),
            'temperature_to' => $this->faker->randomFloat(),
            'warehouse_id' => Warehouse::factory()
        ];
    }
}
