<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserSettingsFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'inn' => 123123123123,
            'language' => $this->faker->languageCode,
            'printing_documents' => false,
            'additional_fields' => 0,
            'start_screen' => 'asdasd',
            'update_reports_automatically' => $this->faker->boolean,
            'signature_sent_emails' => 'asdasd',
            'discount' => 'asdasd',
            'image' => $this->faker->imageUrl
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
