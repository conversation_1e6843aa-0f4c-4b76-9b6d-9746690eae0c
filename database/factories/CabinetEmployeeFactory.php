<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CabinetEmployee>
 */
class CabinetEmployeeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
