<?php

namespace Database\Factories;

use App\Models\Cabinet;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WarehouseItem>
 */
class DepartmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => \Str::orderedUuid()->toString(),
            'cabinet_id' => Cabinet::factory(),
            'name' => $this->faker->name,
            'sorting' => mt_rand(0, 100),
            'is_default' => $this->faker->boolean(),
            'sales_channel_id' => null,
            'is_common' => $this->faker->boolean,
            'sort' => random_int(0, 100)
        ];
    }
}
