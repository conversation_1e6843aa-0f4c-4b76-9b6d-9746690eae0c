<?php

namespace Database\Factories;

use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Contractor;
use App\Models\Department;
use App\Models\LegalEntity;
use App\Models\CabinetCurrency;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shipment>
 */
class ShipmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'cabinet_id' => Cabinet::factory(),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'number' => mt_rand(1, 100000),
            'date_from' => $this->faker->date,
            'status_id' => null,
            'held' => $this->faker->boolean(),
            'legal_entity_id' => LegalEntity::factory(),
            'contractor_id' => Contractor::factory(),
            'warehouse_id' => Warehouse::factory(),
            'sales_channel_id' => null,
            'currency_id' => CabinetCurrency::factory(),
            'consignee_id' => null,
            'transporter_id' => null,
            'cargo_name' => $this->faker->word,
            'shipper_instructions' => $this->faker->text,
            'venicle' => $this->faker->name,
            'venicle_number' => mt_rand(1, 100000),
            'total_seats' => mt_rand(1, 100000),
            'goverment_contract_id' => mt_rand(1, 100000),
            'comment' => $this->faker->text,
            'price_includes_vat' => $this->faker->boolean(),
            'overhead_cost' => mt_rand(1, 100000),
            'total_cost' => 0,
            'profit' => 0
        ];
    }
}
