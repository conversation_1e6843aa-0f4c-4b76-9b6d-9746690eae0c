<?php

namespace Database\Factories;

use App\Models\Status;
use App\Models\Cabinet;
use App\Models\Employee;
use App\Models\Warehouse;
use App\Models\Contractor;
use App\Models\LegalEntity;
use App\Models\Department;
use App\Traits\HasOrderedUuid;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VendorOrder>
 */
class VendorOrderFactory extends Factory
{
    use HasOrderedUuid;
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->uuid,
            'cabinet_id' => Cabinet::factory(),
            'number' => $this->faker->buildingNumber,
            'date_from' => $this->faker->date,
            'status_id' => Status::factory(),
            'held' => $this->faker->boolean,
            'waiting' => $this->faker->boolean,
            'legal_entity_id' => LegalEntity::factory(),
            'contractor_id' => Contractor::factory(),
            'plan_date' => $this->faker->date,
            'warehouse_id' => Warehouse::factory(),
            'total_price' => mt_rand(0, 100000000),
            'employee_id' => Employee::factory(),
            'department_id' => Department::factory(),
            'is_common' => $this->faker->boolean,
            'comment' => $this->faker->text,
            'has_vat' => true,
            'price_includes_vat' => true,
        ];
    }
}
