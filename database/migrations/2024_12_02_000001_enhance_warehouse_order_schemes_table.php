<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('warehouse_order_schemes', function (Blueprint $table) {
            // Уровень валидации операций
            $table->enum('validation_level', ['none', 'warning', 'strict'])
                  ->default('warning')
                  ->after('control_operational_balances')
                  ->comment('Уровень валидации операций в ордерной схеме');
            
            // Автоматическое резервирование при создании заказов
            $table->boolean('auto_reserve_orders')
                  ->default(true)
                  ->after('validation_level')
                  ->comment('Автоматически резервировать товары при создании заказов');
            
            // Срок действия резервов по умолчанию (в днях)
            $table->integer('reserve_expiry_days')
                  ->default(30)
                  ->after('auto_reserve_orders')
                  ->comment('Срок действия резервов по умолчанию в днях');
            
            // Разрешить отрицательные остатки
            $table->boolean('allow_negative_stock')
                  ->default(false)
                  ->after('reserve_expiry_days')
                  ->comment('Разрешить отрицательные остатки на складе');
            
            // Строгое соблюдение FIFO
            $table->boolean('strict_fifo')
                  ->default(true)
                  ->after('allow_negative_stock')
                  ->comment('Строгое соблюдение принципа FIFO при списании');
            
            // Обязательный контроль качества
            $table->boolean('quality_control_required')
                  ->default(false)
                  ->after('strict_fifo')
                  ->comment('Обязательный контроль качества при поступлении товаров');
            
            // За сколько дней до истечения блокировать товары
            $table->integer('expiry_control_days')
                  ->default(7)
                  ->after('quality_control_required')
                  ->comment('За сколько дней до истечения срока годности блокировать товары');
            
            // Периодичность инвентаризаций (в днях)
            $table->integer('stocktake_frequency_days')
                  ->nullable()
                  ->after('expiry_control_days')
                  ->comment('Периодичность проведения инвентаризаций в днях');
            
            // Автоматически создавать ордера при операциях
            $table->boolean('auto_create_orders')
                  ->default(true)
                  ->after('stocktake_frequency_days')
                  ->comment('Автоматически создавать приходные/расходные ордера');
        });
    }

    public function down(): void
    {
        Schema::table('warehouse_order_schemes', function (Blueprint $table) {
            $table->dropColumn([
                'validation_level',
                'auto_reserve_orders',
                'reserve_expiry_days',
                'allow_negative_stock',
                'strict_fifo',
                'quality_control_required',
                'expiry_control_days',
                'stocktake_frequency_days',
                'auto_create_orders'
            ]);
        });
    }
};
