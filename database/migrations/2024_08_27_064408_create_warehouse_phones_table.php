<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_phones', function (Blueprint $table) {
            $table->uuid('id')->primary();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->string('phone', 15)->nullable();
            $table->text('comment')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_phones');
    }
};
