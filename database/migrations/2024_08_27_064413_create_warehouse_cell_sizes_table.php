<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_cell_sizes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->string('name', 255);
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();

            //Размеры
            $table->boolean('unlimited_size')->default(false);
            $table->string('height')->nullable();
            $table->string('width')->nullable();
            $table->string('length')->nullable();
            $table->foreignUuid('measurement_unit_size_id')->nullable()->references('id')->on('measurement_units');
            $table->string('volume')->nullable();
            $table->foreignUuid('measurement_unit_volume_id')->nullable()->references('id')->on('measurement_units');

            //Грузоподъемность
            $table->boolean('unlimited_load_capacity')->default(false);
            $table->string('load_capacity')->nullable();
            $table->foreignUuid('measurement_unit_load_capacity_id')->nullable()->references('id')->on('measurement_units');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_cell_sizes');
    }
};
