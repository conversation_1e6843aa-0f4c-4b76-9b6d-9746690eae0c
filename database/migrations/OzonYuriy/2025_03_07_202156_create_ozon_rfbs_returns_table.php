<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_rfbs_returns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->unsignedBigInteger('ozon_company_id');

            $table->string('client_name')->nullable();
            $table->timestamp('rfbs_created_at')->useCurrent();
            $table->string('order_number')->nullable(); // TODO сейчас мало данных, какое то поле должно быть уникальным
            $table->string('posting_number')->nullable();
            $table->string('product_currency_code')->nullable();
            $table->string('product_name')->nullable();
            $table->string('product_offer_id')->nullable();
            $table->decimal('product_price', 10, 2)->default(0);
            $table->unsignedBigInteger('product_sku')->nullable();
            $table->unsignedBigInteger('return_id')->nullable();
            $table->string('return_number')->nullable();
            $table->string('state_state')->nullable();
            $table->string('state_state_name')->nullable();
            $table->string('state_group_state')->nullable();
            $table->string('state_money_return_state_name')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_rfbs_returns');
    }
};
