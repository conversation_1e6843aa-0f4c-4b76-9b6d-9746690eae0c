<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unified_products', function (Blueprint $table) {
            $table->uuid()->primary();
            $table->string('article'); // Артикул
            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete(); // Идентификатор кабинета
            $table->uuid('entity_id'); // ID сущности
            $table->string('entity_type'); // Тип сущности (название таблицы)
            $table->timestamps();

            // Индексы для оптимизации запросов
            $table->index(['article', 'cabinet_id']);
            $table->index(['entity_id', 'entity_type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unified_products');
    }
};
