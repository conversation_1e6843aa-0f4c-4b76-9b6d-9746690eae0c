<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('warehouse_reservations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();
            
            // Связь с партией товара на складе
            $table->foreignUuid('warehouse_item_id')->constrained()->cascadeOnDelete();
            
            // Связь с позицией заказа
            $table->foreignUuid('customer_order_item_id')->constrained()->cascadeOnDelete();
            
            // Количество зарезервированного товара из данной партии
            $table->integer('reserved_quantity');
            
            // Дата резервирования
            $table->dateTime('reserved_at');
            
            // Статус резерва (reserved, shipped, cancelled)
            $table->string('status')->default('reserved');
            
            // Индексы для быстрого поиска
            $table->index(['warehouse_item_id', 'status']);
            $table->index(['customer_order_item_id', 'status']);
            $table->index('reserved_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('warehouse_reservations');
    }
};
