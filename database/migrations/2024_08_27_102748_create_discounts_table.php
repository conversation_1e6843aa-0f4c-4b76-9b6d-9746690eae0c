<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('employee_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->timestamps();
            $table->softDeletes();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();
            $table->string('name');                                // Название скидки
            $table->string('type')->nullable();                     // Тип Специальная цена, Бонусная программа, Накопительная скидка, Персональная скидка, Округление копеек
            $table->boolean('status')->default(1);                  // Статус
            $table->foreignUuid('cabinet_price_id')->nullable()->constrained(); // Использовать тип цен из карточки товара cabinet_prices
            $table->string('fixed_discount')->nullable()->default(0);    // Использовать фиксированную скидку в %
            $table->boolean('products_services')->nullable()->default(0);       // Все товары и услуги - 0 Или Отдельный - 1
            $table->boolean('contractors')->default(0);             // Все контрагенты - 0 Или Контрагенты из групп - 1
            $table->integer('accrual_rule')->nullable()->default(1);            // Правило начисления
            $table->integer('writeoff_rule')->nullable()->default(1);           // Правило списания
            $table->integer('max_proc_payment')->nullable()->default(0);  // Максимальный % оплаты
            $table->boolean('accrual_writeoff')->nullable()->default(1);        // Одновременное начисление и списание
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discounts');
    }
};
