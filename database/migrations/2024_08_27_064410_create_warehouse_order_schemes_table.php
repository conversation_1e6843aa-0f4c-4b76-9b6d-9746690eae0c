<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouse_order_schemes', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('warehouse_id')->constrained()->cascadeOnDelete();
            //При поступлении с
            $table->date('on_coming_from')->nullable();

            $table->date('on_shipment_from')->nullable();

            $table->boolean('control_operational_balances')->default(true);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouse_order_schemes');
    }
};
