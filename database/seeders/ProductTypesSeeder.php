<?php

namespace Database\Seeders;

use App\Models\ProductTypes;
use Illuminate\Database\Seeder;

class ProductTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            'Не маркируется',
            'Табачная продукция',
            'Обувь',
            'Одежда',
            'Постельное белье',
            'Духи и туалетная вода',
            'Фотокамеры и лампы-вспышки',
            'Шины и покрышки',
            'Молочная продукция',
            'Упакованная вода',
            'Альтернативная табачная продукция',
            'Никотиносодержащая продукция',
            'Биологически активные добавки к пище',
            'Антисептики',
            'Медизделия и кресла-коляски',
        ];

        foreach($data as $item) {

            ProductTypes::create(['name' => $item]);

        }

    }
}
