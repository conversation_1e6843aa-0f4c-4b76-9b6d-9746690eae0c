<?php

namespace App\Repositories\Goods\Products;

use App\Contracts\Repositories\ProductCategoriesRepositoryContract;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProductCategoriesRepository implements ProductCategoriesRepositoryContract
{
    use HasTimestamps;
    protected const TABLE = 'product_categories';

    public function checkPermissions(string $id, string $cabinetId): bool
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->where('cabinet_id', $cabinetId)
            ->exists();
    }

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $id)
            ->orderBy('name')
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }
}
