<?php

namespace App\Repositories\Money\OutgoingPayments;

use App\Contracts\Repositories\OutgoingPaymentItemsRepositoryContract;
use App\Traits\HasTimestamps;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class OutgoingPaymentItemsRepository implements OutgoingPaymentItemsRepositoryContract
{
    use HasTimestamps;
    protected const TABLE = 'outgoing_payment_items';

    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        return DB::table(self::TABLE . ' as opi')
            ->join('documents as d', 'opi.document_id', '=', 'd.documentable_id')
            ->leftJoin('acceptances as a', function ($join) {
                $join->on('d.documentable_id', '=', 'a.id')
                    ->where('d.documentable_type', '=', 'acceptances');
            })
            ->leftJoin('shipments as s', function ($join) {
                $join->on('d.documentable_id', '=', 's.id')
                    ->where('d.documentable_type', '=', 'shipments');
            })
            ->leftJoin('vendor_orders as vo', function ($join) {
                $join->on('d.documentable_id', '=', 'vo.id')
                    ->where('d.documentable_type', '=', 'vendor_orders');
            })
            ->leftJoin('customer_orders as co', function ($join) {
                $join->on('d.documentable_id', '=', 'co.id')
                    ->where('d.documentable_type', '=', 'customer_orders');
            })
            ->join('legal_entities', function ($join) {
                $join->on('a.legal_entity_id', '=', 'legal_entities.id')
                    ->orOn('s.legal_entity_id', '=', 'legal_entities.id')
                    ->orOn('vo.legal_entity_id', '=', 'legal_entities.id')
                    ->orOn('co.legal_entity_id', '=', 'legal_entities.id');
            })
            ->join('contractors', function ($join) {
                $join->on('a.contractor_id', '=', 'contractors.id')
                    ->orOn('s.contractor_id', '=', 'contractors.id')
                    ->orOn('vo.contractor_id', '=', 'contractors.id')
                    ->orOn('co.contractor_id', '=', 'contractors.id');
            })
            ->where('opi.outgoing_payment_id', $id)
            ->select(
                'opi.*',
                'd.documentable_type as document_type',
                'legal_entities.short_name as legal_entity_title',
                'contractors.title as contractor_title',
                DB::raw('COALESCE(a.number, s.number, vo.number, co.number) as number'),
                DB::raw('COALESCE(a.held, s.held, vo.held, co.held) as held'),
                DB::raw('COALESCE(a.date_from, s.date_from, vo.date_from, co.date_from) as date'),
                DB::raw('COALESCE(a.total_price, s.total_price, vo.total_price, co.total_price) as total_price'),
                // Вычисление оставшейся суммы к оплате
                DB::raw('
                    COALESCE(
                        CAST(COALESCE(a.total_price, s.total_price, vo.total_price, co.total_price) AS NUMERIC) -
                        (SELECT SUM(CAST(opi2.paid_in AS NUMERIC))
                         FROM ' . self::TABLE . ' as opi2
                         WHERE opi2.document_id = opi.document_id
                         GROUP BY opi2.document_id),
                    CAST(COALESCE(a.total_price, s.total_price, vo.total_price, co.total_price) AS NUMERIC)) as amount_left_to_pay')
            )
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function getWhereOutgoingPaymentInIds(array $ids): Collection
    {
        return DB::table(self::TABLE)
            ->whereIn('outgoing_payment_id', $ids)
            ->get();
    }
}
