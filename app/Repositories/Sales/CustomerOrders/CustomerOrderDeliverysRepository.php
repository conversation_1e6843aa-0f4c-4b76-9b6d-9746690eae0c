<?php

namespace App\Repositories\Sales\CustomerOrders;

use App\Contracts\Repositories\CustomerOrderDeliveryRepositoryContract;
use Illuminate\Support\Facades\DB;

class CustomerOrderDeliverysRepository implements CustomerOrderDeliveryRepositoryContract
{
    private const TABLE = 'customer_order_delivery_infos';
    public function upsert(array $items): void
    {
        DB::table(self::TABLE)
            ->upsert(
                $items,
                ['order_id'],
                [
                    'comment',
                    'post_code',
                    'country',
                    'region',
                    'city',
                    'street',
                    'house',
                    'office',
                    'other',
                    'updated_at'
                ]
            );
    }
}
