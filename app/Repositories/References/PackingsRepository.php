<?php

namespace App\Repositories\References;

use App\Contracts\Repositories\PackingsRepositoryContract;
use App\Entities\PackingEntity;
use App\Traits\HasTimestamps;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class PackingsRepository implements PackingsRepositoryContract
{
    use HasTimestamps;

    private const TABLE = 'packings';
    public function get(
        string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $query = $this->getEntity();

        [$baseFields, $relationFields] = $query->parseFields($fields);

        $query->select($baseFields)
            ->where('cabinet_id', $id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($query, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        $query->when(isset($filters['search']['value']), function ($query) use ($filters) {
            $query->where('name', 'ILIKE', '%'.$filters['search']['value'].'%')
                ->orWhere('description', 'ILIKE', '%'.$filters['search']['value'].'%');
        });

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update(
                array_merge(
                    $data,
                    ['updated_at' => Carbon::now()]
                )
            );
    }

    public function show(string $id): ?object
    {
        return DB::table('packings')
            ->join('measurement_units as mu_size', 'mu_size.id', '=', 'packings.measurement_unit_size_id')
            ->join('measurement_units as mu_weight', 'mu_weight.id', '=', 'packings.measurement_unit_weight_id')
            ->join('measurement_units as mu_volume', 'mu_volume.id', '=', 'packings.measurement_unit_volume_id')
            ->select([
                'packings.id',
                'packings.updated_at',
                'packings.created_at',
                'packings.cabinet_id',
                'packings.employee_id',
                'packings.department_id',
                'packings.name',
                'packings.description',
                'packings.length',
                'packings.width',
                'packings.height',
                'packings.weight',
                'packings.volume',
                DB::raw('
                    COALESCE(
                        JSON_BUILD_OBJECT(
                            \'id\', mu_size.id,
                            \'created_at\', mu_size.created_at,
                            \'updated_at\', mu_size.updated_at,
                            \'name\', mu_size.name,
                            \'conversion_factor\', mu_size.conversion_factor,
                            \'cabinet_id\', mu_size.cabinet_id,
                            \'group_id\', mu_size.group_id,
                            \'is_default\', mu_size.is_default,
                            \'code\', mu_size.code,
                            \'short_name\', mu_size.short_name
                        ), \'{}\'
                    ) AS measurement_unit_size
                '),
                DB::raw('
                    COALESCE(
                        JSON_BUILD_OBJECT(
                            \'id\', mu_weight.id,
                            \'created_at\', mu_weight.created_at,
                            \'updated_at\', mu_weight.updated_at,
                            \'name\', mu_weight.name,
                            \'conversion_factor\', mu_weight.conversion_factor,
                            \'cabinet_id\', mu_weight.cabinet_id,
                            \'group_id\', mu_weight.group_id,
                            \'is_default\', mu_weight.is_default,
                            \'code\', mu_weight.code,
                            \'short_name\', mu_weight.short_name
                        ), \'{}\'
                    ) AS measurement_unit_weight
                '),
                DB::raw('
                    COALESCE(
                        JSON_BUILD_OBJECT(
                            \'id\', mu_volume.id,
                            \'created_at\', mu_volume.created_at,
                            \'updated_at\', mu_volume.updated_at,
                            \'name\', mu_volume.name,
                            \'conversion_factor\', mu_volume.conversion_factor,
                            \'cabinet_id\', mu_volume.cabinet_id,
                            \'group_id\', mu_volume.group_id,
                            \'is_default\', mu_volume.is_default,
                            \'code\', mu_volume.code,
                            \'short_name\', mu_volume.short_name
                        ), \'{}\'
                    ) AS measurement_unit_volume
                '),
            ])
            ->where('packings.id', $id)
            ->groupBy(['packings.id','mu_size.id','mu_volume.id','mu_weight.id'])
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    private function getEntity(): PackingEntity
    {
        return new PackingEntity();
    }
}
