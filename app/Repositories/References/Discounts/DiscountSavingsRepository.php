<?php

namespace App\Repositories\References\Discounts;

use App\Contracts\Repositories\DiscountSavingsRepositoryContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Entities\DiscountSavingEntity;
use App\Entities\EntityBuilder;
use App\Traits\Archivable;
use App\Traits\HasTimestamps;
use App\Traits\SoftDeletable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class DiscountSavingsRepository implements DiscountSavingsRepositoryContract
{
    use HasTimestamps;
    use SoftDeletable;
    use Archivable;

    protected const string TABLE = 'discount_savings';

    public function __construct(
        private readonly AuthorizationServiceContract $authService,
        private readonly DiscountSavingEntity $entity
    ) {
    }

    public function get(
        ?string $id = null,
        array $filters = [],
        array $fields = ['*'],
        ?string $sortField = null,
        string $sortDirection = 'asc',
        int $page = 1,
        int $perPage = 15
    ): Collection {
        $this->authService->init();
        [$baseFields, $relationFields] = $this->entity->parseFields($fields);

        $query = $this->entity;
        $query->select($baseFields)
            ->where('is_system', true)
            ->orWhere(function (EntityBuilder $query) use ($id) {
                $query->where('is_system', false)
                    ->where('cabinet_id', $id);
            });

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($this->entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        return $query
            ->when($sortField, function ($query) use ($sortField, $sortDirection) {
                return $query->sort($sortField, $sortDirection);
            })
            ->paginate($perPage, $page)
            ->get();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)
            ->insert($data);
    }

    public function oldDiscountSavingsIdsForDiscount(string $resourceId): ?Collection
    {
        return DB::table(self::TABLE) // текущие
            ->where('discount_id', $resourceId)
            ->pluck('id');
    }

    public function deleteOldDiscountSavingsWhereSavingsIds(array $recordsToDelete): int
    {
        return DB::table(self::TABLE)
            ->whereIn('id', $recordsToDelete)
            ->delete();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['discount_id', 'amount','procent'],
                ['discount_id', 'amount','procent']
            );
    }

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->update($data);
    }

    public function show(string $id): ?object
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->first();
    }

    public function delete(string $id): int
    {
        return DB::table(self::TABLE)
            ->where('id', $id)
            ->delete();
    }

    public function getEntity(): DiscountSavingEntity
    {
        return $this->entity;
    }


}
