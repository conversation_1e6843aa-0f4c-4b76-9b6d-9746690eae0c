<?php

namespace App\Repositories\Workspace\Permissions\Roles;

use App\Contracts\Repositories\RolePermissionsRepositoryContract;
use App\Traits\HasTimestamps;
use DB;

class RolePermissionsRepository implements RolePermissionsRepositoryContract
{
    use HasTimestamps;
    protected const TABLE = 'role_permissions';

    public function insert(array $data): bool
    {
        $data = $this->setTimestamps($data);

        return DB::table(self::TABLE)->insert($data);
    }

    public function deleteWhereNotInIds(string $roleId, array $ids): int
    {
        return DB::table(self::TABLE)
            ->where('role_id', $roleId)
            ->whereNotIn('id', $ids)
            ->delete();
    }

    public function upsert(array $data): int
    {
        return DB::table(self::TABLE)
            ->upsert(
                $data,
                ['id'],
                ['scope']
            );
    }
}
