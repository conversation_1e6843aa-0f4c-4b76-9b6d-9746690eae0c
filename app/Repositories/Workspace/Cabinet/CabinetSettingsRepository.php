<?php

namespace App\Repositories\Workspace\Cabinet;

use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use Illuminate\Support\Facades\DB;

class CabinetSettingsRepository implements CabinetSettingRepositoryContract
{
    private const TABLE = 'cabinet_settings';

    public function update(string $id, array $data): int
    {
        return DB::table(self::TABLE)
            ->where('cabinet_id', $id)
            ->update($data);
    }
    public function show(string $id): ?object
    {
        return DB::table(self::TABLE . ' as cs')
            ->leftJoin('files as logo', 'logo.id', '=', 'cs.logo_image_id')
            ->where('cs.cabinet_id', $id)
            ->select(
                [
                'cs.*',
                DB::raw('COALESCE(
                    CASE
                        WHEN logo.id IS NOT NULL THEN jsonb_build_object(
                                \'id\', logo.id,
                                \'created_at\', logo.created_at,
                                \'updated_at\', logo.updated_at,
                                \'name\', logo.name,
                                \'path\', logo.path,
                                \'size\', logo.size,
                                \'mime_type\', logo.mime_type,
                                \'is_private\', logo.is_private,
                                \'employee_id\', logo.employee_id,
                                \'type\', logo.type
                            )
                        ELSE \'[]\'::jsonb
                    END,
                    \'[]\'::jsonb
                ) AS logo_image')
                ]
            )
            ->first();
    }

    public function insert(array $data): bool
    {
        return DB::table(self::TABLE)->insert($data);
    }
}
