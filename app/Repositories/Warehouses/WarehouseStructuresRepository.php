<?php

namespace App\Repositories\Warehouses;

use App\Contracts\Repositories\WarehouseStructuresRepositoryContract;
use DB;
use Illuminate\Support\Carbon;

class WarehouseStructuresRepository implements WarehouseStructuresRepositoryContract
{
    public function insert(array $data): void
    {
        DB::table('warehouse_structures')
            ->insert(
                array_merge(
                    $data,
                    ['created_at' => Carbon::now()]
                )
            );
    }
}
