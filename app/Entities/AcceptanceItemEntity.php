<?php

namespace App\Entities;

class AcceptanceItemEntity extends BaseEntity
{
    public static string $table = 'acceptance_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'acceptance_id',
        'product_id',
        'quantity',
        'price',
        'vat_rate_id',
        'discount',
        'total_price',
        'country_id',
        'gtd_number',
        'recidual',
        'total_vat_sum'
    ];

    public function acceptance(): RelationBuilder
    {
        return $this->hasOne(AcceptanceEntity::class, 'acceptance_id', 'id');
    }

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function vat_rates(): RelationBuilder
    {
        return $this->hasOne(VatRateEntity::class, 'vat_rate_id', 'id');
    }

    public function countries(): RelationBuilder
    {
        return $this->hasOne(CountryEntity::class, 'country_id', 'id');
    }
}
