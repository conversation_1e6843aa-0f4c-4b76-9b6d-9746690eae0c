<?php

namespace App\Entities;

use Closure;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use stdClass;

class EntityBuilder
{
    private Builder $query;
    private array $relations = [];
    private array $selectedFields = [];
    private array $additionalFields = [];
    private int $page = 1;
    private int $perPage = 15;
    private array $sort = [];
    private array $entityFields;
    private static array $fieldCache = [];

    public static array $operators = [
        '=', '!=', '<', '>', '<=', '>=', '<>', 'like', 'not like', 'between', 'in','ilike', 'not ilike'
    ];

    public function __construct(private readonly BaseEntity $entity)
    {
        $this->query = DB::table($entity::getTable());

        $entityClass = get_class($this->entity);
        if (!isset(self::$fieldCache[$entityClass])) {
            self::$fieldCache[$entityClass] = $entity::getFields();
        }
        $this->entityFields = self::$fieldCache[$entityClass];
    }

    public function toSql(): string
    {
        return $this->query->toSql();
    }
    public function select(array $fields): self
    {
        // Если поля не указаны или есть *, выбираем все поля
        if (empty($fields) || in_array('*', $fields)) {
            $this->selectedFields = [];
            return $this;
        }

        // Разделяем поля на основные и связанные
        $baseFields = [];
        $relationFields = [];

        foreach ($fields as $field) {
            if (method_exists($this->entity, $field)) {
                // Это связь целиком
                $relationBuilder = $this->entity->{$field}();
                $this->with($field, function ($builder) use ($relationBuilder) {
                    $builder->fields($relationBuilder->getFields());
                });
            } elseif (str_contains($field, '.')) {
                // Это поле связи (например accounts.bank)
                [$relation, $relationField] = explode('.', $field, 2);
                if (!isset($relationFields[$relation])) {
                    $relationFields[$relation] = [];
                }
                $relationFields[$relation][] = $relationField;
            } else {
                // Это обычное поле
                $baseFields[] = $field;
            }
        }

        // Устанавливаем основные поля, если они есть
        $this->selectedFields = $baseFields;

        // Добавляем связи с их полями
        foreach ($relationFields as $relation => $fields) {
            if (method_exists($this->entity, $relation)) {
                $this->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            }
        }

        return $this;
    }

    public function when(mixed $value, callable $callback): self
    {
        if ($value) {
            $callback($this);
        }
        return $this;
    }

    public function orWhereNull(string|Closure $field): self
    {
        $this->query->orWhereNull($field);
        return $this;
    }
    public function where(string|Closure $field, mixed $operator = null, mixed $value = null): self
    {
        if ($field instanceof Closure) {
            $this->query->where(function ($query) use ($field) {
                // Сохраняем оригинальный query
                $originalQuery = $this->query;
                // Устанавливаем временный query для группировки условий
                $this->query = $query;
                // Выполняем замыкание
                $field($this);
                // Восстанавливаем оригинальный query
                $this->query = $originalQuery;
            });
            return $this;
        }

        // Проверка, что поле существует в таблице
        if (!in_array($field, $this->entityFields, true)) {
            throw new InvalidArgumentException("Invalid field: $field");
        }

        if (!in_array($operator, static::$operators, true)) {
            $this->query->where($this->entity::getTable() . '.' . $field, '=', $operator);
            return $this;
        }

        // Если значение не указано для некоторых операторов
        if (in_array($operator, ['like', 'between', 'in'], true) && $value === null) {
            throw new InvalidArgumentException("Value is required when using operator: $operator");
        }

        // Если все проверки прошли, добавляем условие в запрос
        $this->query->where($this->entity::getTable() . '.' . $field, $operator, $value);
        return $this;
    }

    public function whereBetween(string $field, array $value): self
    {
        $this->query->whereBetween($this->entity::getTable() . '.' . $field, $value);
        return $this;
    }

    public function whereIn(string $field, array $value): self
    {
        if (!in_array($field, $this->entityFields, true)) {
            throw new InvalidArgumentException("Invalid field: $field");
        }

        // Если поле id, приводим значения к UUID
        if ($field === 'id') {
            $value = array_map(fn ($id) => DB::raw("'$id'::uuid"), $value);
        }

        // Если все проверки прошли, добавляем условие в запрос
        $this->query->whereIn($this->entity::getTable() . '.' . $field, $value);
        return $this;
    }

    public function whereNotIn(string $field, array|Closure $value): self
    {
        if ($value instanceof Closure) {
            $this->query->whereNotIn($field, function ($query) use ($value) {

                $originalQuery = $this->query;

                $this->query = $query;

                $value($this);

                $this->query = $originalQuery;
            });
            return $this;
        }

        if (!in_array($field, $this->entityFields, true)) {
            throw new InvalidArgumentException("Invalid field: $field");
        }


        if ($field === 'id') {
            $value = array_map(fn ($id) => DB::raw("'$id'::uuid"), $value);
        }

        $this->query->whereNotIn($this->entity::getTable() . '.' . $field, $value);
        return $this;
    }

    public function orWhere(string|Closure $field, mixed $operator = null, mixed $value = null): self
    {
        if ($field instanceof Closure) {
            $this->query->orWhere(function ($query) use ($field) {
                // Сохраняем оригинальный query
                $originalQuery = $this->query;
                // Устанавливаем временный query для группировки условий
                $this->query = $query;
                // Выполняем замыкание
                $field($this);
                // Восстанавливаем оригинальный query
                $this->query = $originalQuery;
            });
            return $this;
        }

        // Проверка, что поле существует в таблице
        if (!in_array($field, $this->entityFields, true)) {
            throw new InvalidArgumentException("Invalid field: $field");
        }

        if (!in_array($operator, static::$operators, true)) {
            $this->query->orWhere($this->entity::getTable() . '.' . $field, '=', $operator);
            return $this;
        }

        // Если значение не указано для некоторых операторов
        if (in_array($operator, ['like', 'between', 'in'], true) && $value === null) {
            throw new InvalidArgumentException("Value is required when using operator: $operator");
        }

        // Если все проверки прошли, добавляем условие в запрос
        $this->query->orWhere($this->entity::getTable() . '.' . $field, $operator, $value);
        return $this;
    }

    public function whereArray(array $conditions): self
    {
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                $operator = $value[1] ?? '=';
                $value = $value[0];
            } else {
                $operator = '=';
            }
            $this->where($field, $value, $operator);
        }
        return $this;
    }

    public function filter(array $filters): self
    {
        $this->entity::applyFilters($this->query, $filters);
        return $this;
    }

    public function sort(string $field, string $direction = 'asc'): self
    {
        // Валидация направления сортировки
        if (!in_array(strtolower($direction), ['asc', 'desc'], true)) {
            throw new InvalidArgumentException("Invalid sort direction: {$direction}");
        }

        // Проверяем, является ли поле связью (содержит точку)
        if (str_contains($field, '.')) {
            [$relation, $relationField] = explode('.', $field, 2);

            // Проверяем существование связи
            if (!method_exists($this->entity, $relation)) {
                throw new InvalidArgumentException("Invalid relation: {$relation}");
            }

            // Получаем конфигурацию связи
            $entityClass = $this->entity->{$relation}()->getEntityClass();

            // Проверяем существование поля в связанной сущности
            if (!in_array($relationField, $entityClass::getFields(), true)) {
                throw new InvalidArgumentException("Invalid field '{$relationField}' for relation '{$relation}'");
            }

            // Проверяем, что связь загружена
            if (!isset($this->relations[$relation])) {
                $this->with($relation);
            }

            // Сохраняем сортировку для связи
            $this->sort[$relation] = [
                'field' => $relationField,
                'direction' => $direction,
            ];
        } else {
            // Проверяем существование поля в основной сущности
            if (!in_array($field, $this->entityFields, true)) {
                throw new InvalidArgumentException("Invalid field: $field");
            }

            // Сортировка по полю основной таблицы
            $this->sort[$this->entity::getTable()] = [
                'field' => $field,
                'direction' => $direction,
            ];
        }

        return $this;
    }

    public function paginate(int $perPage, int $page = 1): self
    {
        $this->perPage = $perPage;
        $this->page = $page;
        return $this;
    }

    /*
     * Используется для получения связей по определенным условиям
     */
    public function with(string $relation, callable $callback = null): self
    {
        if (!method_exists($this->entity, $relation)) {
            return $this;
        }

        // Получаем конфигурацию связи из метода Entity
        $relationBuilder = $this->entity->{$relation}();

        // Создаем новый builder для связи
        $entityClass = $relationBuilder->getEntityClass();
        $builder = new self(new $entityClass());

        // Применяем callback если он есть
        if ($callback) {
            $callback($builder);
        }

        // Prepare the relation configuration
        $relationConfig = [
            'entityClass' => $entityClass,
            'localKey' => $relationBuilder->getLocalKey(),
            'foreignKey' => $relationBuilder->getForeignKey(),
            'fields' => $builder->selectedFields ?: $relationBuilder->getFields(),
            'type' => $relationBuilder->getType()
        ];

        // Include the pivot table if the relation type is many-to-many
        if ($relationBuilder->getType() === 'manyToMany') {
            $relationConfig['parentTable'] = $relationBuilder->getParentTable();
        }

        // Save the relation configuration
        $this->relations[$relation] = $relationConfig;

        return $this;
    }

    public function fields(array $fields): self
    {
        $this->selectedFields = $fields;
        return $this;
    }

    /*
     * Используется для проверки на наличие связи с определенными условиями
     */
    public function whereHas(string $relation, ?callable $callback = null): self
    {
        $relationMethod = $this->entity->$relation();
        $relatedEntity = new ($relationMethod->getEntityClass())();
        $relatedBuilder = new self($relatedEntity);
        if ($callback) {
            $callback($relatedBuilder);
        }

        $this->query->whereExists(function ($query) use ($relatedBuilder, $relationMethod) {
            $query->from($relationMethod->getEntityClass()::getTable())
                ->whereRaw("{$this->entity::getTable()}.{$relationMethod->getLocalKey()} = {$relationMethod->getEntityClass()::getTable()}.{$relationMethod->getForeignKey()}");

            // Копируем все условия из relatedBuilder в текущий запрос
            if (!empty($relatedBuilder->query->wheres)) {
                $query->mergeWheres($relatedBuilder->query->wheres, $relatedBuilder->query->getBindings());
            }

            // Копируем все подзапросы из relatedBuilder
            if (property_exists($relatedBuilder->query, 'subQueries') && !empty($relatedBuilder->query->subQueries)) {
                foreach ($relatedBuilder->query->subQueries as $subQuery) {
                    $query->addSubQuery($subQuery);
                }
            }
        });

        return $this;
    }


    public function orWhereHas(string $relation, callable $callback): self
    {
        $relationMethod = $this->entity->$relation();
        $relatedEntity = new ($relationMethod->getEntityClass())();
        $relatedBuilder = new self($relatedEntity);
        $callback($relatedBuilder);

        $this->query->orWhereExists(function ($query) use ($relatedBuilder, $relationMethod) {
            $query->from($relationMethod->getEntityClass()::getTable())
                ->whereRaw("{$this->entity::getTable()}.id = {$relationMethod->getEntityClass()::getTable()}.{$relationMethod->getForeignKey()}")
                ->mergeWheres($relatedBuilder->query->wheres, $relatedBuilder->query->getBindings());
        });

        return $this;
    }


    public function whereRaw(string $sql, array $bindings = []): self
    {
        $this->query->whereRaw($sql, $bindings);
        return $this;
    }

    public function get(): Collection
    {
        // Получаем общее количество записей до применения limit/offset
        $total = $this->query->getCountForPagination();

        // Сохраняем оригинальный запрос со всеми JOIN и полями
        $originalQuery = clone $this->query;

        // Добавляем поля выборки к оригинальному запросу
        if (empty($this->selectedFields)) {
            $originalQuery->addSelect($this->entity::getTable() . '.*');
        } else {
            // Всегда добавляем id в выборку
            $selectFields = array_unique(array_merge(['id'], $this->selectedFields));
            $originalQuery->addSelect(
                array_map(
                    fn ($field) => $this->entity::getTable() . '.' . $field,
                    $selectFields
                )
            );
        }

        // Добавляем дополнительные поля из JOIN-ов
        if (!empty($this->additionalFields)) {
            $originalQuery->addSelect($this->additionalFields);
        }

        // Обработка связей
        foreach ($this->relations as $relationName => $relation) {
            $relatedEntity = new $relation['entityClass']();
            $relatedTable = $relatedEntity::getTable();

            $fields = in_array('*', $relation['fields'])
                ? $relatedEntity::getFields()
                : $relation['fields'];

            $jsonPairs = array_reduce($fields, function ($acc, $field) use ($relatedTable) {
                $acc[] = "'$field'";
                $acc[] = "$relatedTable.$field";
                return $acc;
            }, []);

            if ($relation['type'] === 'hasMany') {
                $originalQuery->addSelect(DB::raw(
                    "COALESCE(
                        (
                            SELECT jsonb_agg(data)
                            FROM (
                                SELECT json_build_object(" . implode(',', $jsonPairs) . ") as data
                                FROM {$relatedTable}
                                WHERE {$relatedTable}.{$relation['foreignKey']} = {$this->entity::getTable()}.{$relation['localKey']}
                                AND {$relatedTable}.id IS NOT NULL
                                ORDER BY {$relatedTable}.id
                            ) subquery
                        ),
                        '[]'::jsonb
                    ) as {$relationName}"
                ));
            } elseif ($relation['type'] === 'manyToMany') {

                $parentTable = $relation['parentTable'];
                $relatedTable = $relation['entityClass']::getTable();

                $originalQuery->addSelect(DB::raw(
                    "COALESCE(
                        (
                            SELECT jsonb_agg(data)
                            FROM (
                                SELECT json_build_object(" . implode(',', $jsonPairs) . ") as data
                                FROM {$parentTable}
                                JOIN {$relatedTable} ON {$parentTable}.{$relation['foreignKey']} = {$relatedTable}.id
                                WHERE {$parentTable}.{$relation['localKey']} = {$this->entity::getTable()}.{$this->entity->getKeyName()}
                                AND {$relatedTable}.id IS NOT NULL
                                ORDER BY {$relatedTable}.id
                            ) subquery
                        ),
                        '[]'::jsonb
                    ) as {$relationName}"
                ));
            } else {
                $originalQuery->addSelect(
                    DB::raw("(
                        SELECT json_build_object(" . implode(',', $jsonPairs) . ")::jsonb
                        FROM {$relatedTable}
                        WHERE {$relatedTable}.{$relation['foreignKey']} = {$this->entity::getTable()}.{$relation['localKey']}
                        AND {$relatedTable}.id IS NOT NULL
                        LIMIT 1
                    ) as {$relationName}")
                );
            }
        }

        // Применяем пагинацию к запросу
        $offset = ($this->page - 1) * $this->perPage;
        $originalQuery->offset($offset)->limit($this->perPage);

        // Группировка по id если есть связи
        if (!empty($this->relations)) {
            $originalQuery->groupBy($this->entity::getTable() . '.id');
        }

        // Применяем сортировку для основной таблицы
        if (isset($this->sort[$this->entity::getTable()])) {
            $sort = $this->sort[$this->entity::getTable()];
            $originalQuery->orderBy(
                $this->entity::getTable() . '.' . $sort['field'],
                $sort['direction']
            );
        }

        // Получаем результаты с пагинацией
        $results = $originalQuery->get();

        if ($results->isEmpty()) {
            return new Collection([
                'data' => [],
                'meta' => [
                    'current_page' => $this->page,
                    'per_page' => $this->perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $this->perPage),
                ]
            ]);
        }

        // Маппим результаты
        $items = $results->map(function ($row) {
            return $this->mapRow($row);
        })->all();

        return new Collection([
            'data' => $items,
            'meta' => [
                'current_page' => $this->page,
                'per_page' => $this->perPage,
                'total' => $total,
                'last_page' => ceil($total / $this->perPage),
            ]
        ]);
    }

    private function mapRow($row): stdClass
    {
        $item = new stdClass();

        // Всегда добавляем id
        $item->id = $row->id;

        // Маппинг основных полей
        $fields = empty($this->selectedFields)
            ? $this->entity::getFields()
            : $this->selectedFields;

        foreach ($fields as $field) {
            if ($field !== 'id') { // Пропускаем id, так как уже добавили
                $item->{$field} = $row->{$field};
            }
        }

        // Маппинг связей
        foreach ($this->relations as $relationName => $relation) {
            $relationJson = $row->{$relationName};

            if ($relationJson === null) {
                $item->{$relationName} = null;
            } else {
                $decodedData = json_decode($relationJson, true, 512, JSON_THROW_ON_ERROR);
                $item->{$relationName} = $decodedData ?? ($relation['type'] === 'hasMany' ? [] : null);
            }
        }

        return $item;
    }

    public function join(string $table, string $first, string $operator = '=', string $second = null): self
    {
        $this->query->join($table, $first, $operator, $second);
        return $this;
    }

    public function leftJoin(string $table, string $first, string $operator = '=', string $second = null): self
    {
        $this->query->leftJoin($table, $first, $operator, $second);
        return $this;
    }

    public function whereExists(Closure $callback): self
    {
        $this->query->whereExists($callback);
        return $this;
    }

    public function whereNotExists(Closure $callback): self
    {
        $this->query->whereNotExists($callback);
        return $this;
    }

    public function unionAll(EntityBuilder $query): self
    {
        $this->query->unionAll($query->getQuery());
        return $this;
    }

    public function getQuery(): Builder
    {
        return $this->query;
    }

    public function addSelect(array $fields): self
    {
        $this->additionalFields = array_merge($this->additionalFields, $fields);
        return $this;
    }

    public function whereNull(string|Closure $field): self
    {
        $this->query->whereNull($field);
        return $this;
    }

    public function whereNotNull(string|Closure $field): self
    {
        $this->query->whereNotNull($field);
        return $this;
    }

    public function doesntHave(string $relation, callable $callback = null): self
    {
        $relationMethod = $this->entity->$relation();
        $relatedEntity = new ($relationMethod->getEntityClass())();
        $relatedBuilder = new self($relatedEntity);

        if ($callback) {
            $callback($relatedBuilder);
        }

        $this->query->whereNotExists(function ($query) use ($relatedBuilder, $relationMethod) {
            $query->from($relationMethod->getEntityClass()::getTable())
                ->whereRaw("{$this->entity::getTable()}.id = {$relationMethod->getEntityClass()::getTable()}.{$relationMethod->getForeignKey()}")
                ->when($relatedBuilder->query->wheres, function ($q) use ($relatedBuilder) {
                    $q->mergeWheres($relatedBuilder->query->wheres, $relatedBuilder->query->getBindings());
                });
        });

        return $this;
    }

    public function whereNot(string|Closure $field): self
    {
        $this->query->whereNot($field);
        return $this;
    }

}
