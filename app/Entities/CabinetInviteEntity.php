<?php

namespace App\Entities;

class CabinetInviteEntity extends BaseEntity
{
    public static string $table = 'cabinet_invites';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'email',
        'status',
        'token',
        'role_id'
    ];

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function role(): RelationBuilder
    {
        return $this->hasOne(RoleEntity::class, 'role_id', 'id');
    }
}
