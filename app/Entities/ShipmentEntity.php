<?php

namespace App\Entities;

class ShipmentEntity extends BaseEntity
{
    public static string $table = 'shipments';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'is_common',
        'payment_status',
        'number',
        'date_from',
        'status_id',
        'held',
        'legal_entity_id',
        'contractor_id',
        'warehouse_id',
        'sales_channel_id',
        'currency_id',
        'currency_value',
        'consignee_id',
        'transporter_id',
        'cargo_name',
        'shipper_instructions',
        'venicle',
        'venicle_number',
        'total_seats',
        'goverment_contract_id',
        'comment',
        'price_includes_vat',
        'overhead_cost',
        'total_cost',
        'profit',
        'total_price',
        'total_vat_sum'
    ];

    public function status(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legalEntity(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractor(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function warehouse(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function currency(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function items(): RelationBuilder
    {
        return $this->hasMany(ShipmentItemEntity::class, 'id', 'shipment_id');
    }

    public function document(): RelationBuilder
    {
        return $this->hasOne(DocumentEntity::class, 'id', 'documentable_id');
    }
    public function deliveryInfo(): RelationBuilder
    {
        return $this->hasOne(ShipmentDeliveryInfoEntity::class, 'id', 'shipment_id');
    }

    public function sales_channel(): RelationBuilder
    {
        return $this->hasOne(SaleChannelEntity::class, 'sales_channel_id', 'id');
    }
}
