<?php

namespace App\Entities;

class ShipmentItemEntity extends BaseEntity
{
    public static string $table = 'shipment_items';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'shipment_id',
        'product_id',
        'quantity',
        'price',
        'cost',
        'total_cost',
        'total_price',
        'total_vat_sum',
        'profit',
        'vat_rate_id',
        'discount',
        'recidual'
    ];

    public function products(): RelationBuilder
    {
        return $this->hasOne(ProductEntity::class, 'product_id', 'id');
    }

    public function shipments(): RelationBuilder
    {
        return $this->hasOne(ShipmentEntity::class, 'shipment_id', 'id');
    }

    public function vat_rates(): RelationBuilder
    {
        return $this->hasOne(VatRateEntity::class, 'vat_rate_id', 'id');
    }
}
