<?php

namespace App\Entities;

class OutgoingPaymentEntity extends BaseEntity
{
    public static string $table = 'outgoing_payments';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'status_id',
        'number',
        'date_from',
        'held',
        'without_closing_documents',
        'legal_entity_id',
        'contractor_id',
        'sales_channel_id',
        'sum',
        'included_vat',
        'comment',
        'currency_id',
        'currency_value',
        'bounded_sum',
        'not_bounded_sum',
        'is_imported',
        'employee_id',
        'department_id',
    ];

    public function status(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function legalEntity(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractor(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function currency(): RelationBuilder
    {
        return $this->hasOne(CabinetCurrencyEntity::class, 'currency_id', 'id');
    }

    public function employee(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function department(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function items(): RelationBuilder
    {
        return $this->hasMany(IncomingPaymentItemEntity::class, 'id', 'incoming_payment_id');
    }

    public function document(): RelationBuilder
    {
        return $this->hasOne(DocumentEntity::class, 'id', 'documentable_id');
    }
}
