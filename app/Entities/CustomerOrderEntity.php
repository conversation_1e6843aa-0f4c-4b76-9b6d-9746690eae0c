<?php

namespace App\Entities;

class CustomerOrderEntity extends BaseEntity
{
    public static string $table = 'customer_orders';

    public static array $fields = [
        'id',
        'created_at',
        'updated_at',
        'deleted_at',
        'cabinet_id',
        'employee_id',
        'department_id',
        'is_common',
        'number',
        'date_from',
        'payment_status',
        'status_id',
        'held',
        'reserve',
        'legal_entity_id',
        'contractor_id',
        'plan_date',
        'sales_channel_id',
        'warehouse_id',
        'total_price',
        'comment',
        'has_vat',
        'price_includes_vat'
    ];

    public function statuses(): RelationBuilder
    {
        return $this->hasOne(StatusEntity::class, 'status_id', 'id');
    }

    public function sales_channels(): RelationBuilder
    {
        return $this->hasOne(SalesChannelEntity::class, 'sales_channel_id', 'id');
    }

    public function legal_entities(): RelationBuilder
    {
        return $this->hasOne(LegalEntity::class, 'legal_entity_id', 'id');
    }

    public function contractors(): RelationBuilder
    {
        return $this->hasOne(ContractorEntity::class, 'contractor_id', 'id');
    }

    public function warehouses(): RelationBuilder
    {
        return $this->hasOne(WarehouseEntity::class, 'warehouse_id', 'id');
    }

    public function employees(): RelationBuilder
    {
        return $this->hasOne(EmployeeEntity::class, 'employee_id', 'id');
    }

    public function departments(): RelationBuilder
    {
        return $this->hasOne(DepartmentEntity::class, 'department_id', 'id');
    }

    public function customer_order_items(): RelationBuilder
    {
        return $this->hasMany(CustomerOrderItemEntity::class, 'id', 'order_id');
    }

    public function documents(): RelationBuilder
    {
        return $this->hasOne(DocumentEntity::class, 'id', 'documentable_id');
    }

    public function customer_order_delivery_infos(): RelationBuilder
    {
        return $this->hasOne(CustomerOrderDeliveryInfoEntity::class, 'id', 'order_id');
    }
}
