<?php

namespace App\Console\Commands;

use App\Actions\Currencies\UpdateCurrenciesAction as Action;
use Illuminate\Console\Command;

class UpdateCurrencies extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-currencies';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $this->info('Обновляем таблицу "global_currencies" используя API курсов валют ЦБ РФ...');
            $action = new Action();
            $action->handle();
            $this->info('Обновление таблицы "global_currencies" завершено.');
            return self::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Ошибка при выполнении команды: ' . $e->getMessage());
            return self::FAILURE;
        }
    }
}
