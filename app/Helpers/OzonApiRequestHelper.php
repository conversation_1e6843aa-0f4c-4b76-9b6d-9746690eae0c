<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Http;

class OzonApiRequestHelper
{
    public static function sendRequest(string $method, string $url, string $clientId, string $apiKey, array $body = [])
    {
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Client-Id' => $clientId,
            'Api-Key' => $apiKey,
        ])->$method( config('ozon.seller_url') . $url, $body);

        if ($response->failed()) {
            throw new \Exception('Ошибка API Ozon: ' . $response->body(), $response->status());
        }

        return $response->json();
    }
}
