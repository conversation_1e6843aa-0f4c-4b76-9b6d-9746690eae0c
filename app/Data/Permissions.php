<?php

use App\Enums\Api\Internal\PermissionNameEnum;

return [
    'permissions' => [
        0 => [
            'guard_name' => PermissionNameEnum::METRICS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Просматривать показатели',
            'category_name' => 'Показатели'
            ],
        1 => [
            'guard_name' => PermissionNameEnum::BIN->value,
            'require_scope' => false,
            'operations' => 'view',
            'group_name' => 'Просматривать корзину',
            'category_name' => 'Показатели'
            ],
        2 => [
            'guard_name' => PermissionNameEnum::RECOVER_DOCUMENTS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Восстанавливать документы',
            'category_name' => 'Показатели'
            ],
        3 => [
            'guard_name' => PermissionNameEnum::BIN->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Очищать корзину',
            'category_name' => 'Показатели'
            ],
        4 => [
            'guard_name' => PermissionNameEnum::AUDIT->value,
            'require_scope' => false,
            'operations' => 'view',
            'group_name' => 'Просматривать аудит',
            'category_name' => 'Показатели'
            ],

        5 => [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Заказы поставщикам',
            'category_name' => 'Закупки'
        ],
        6 => [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Заказы поставщикам',
            'category_name' => 'Закупки'
        ],
        7 => [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Заказы поставщикам',
            'category_name' => 'Закупки'
        ],
        8 => [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Заказы поставщикам',
            'category_name' => 'Закупки'
        ],
        9 => [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Заказы поставщикам',
            'category_name' => 'Закупки'
        ],
        10 => [
            'guard_name' => PermissionNameEnum::VENDOR_ORDERS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Заказы поставщикам',
            'category_name' => 'Закупки'
        ],

        11 => [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Счета поставщиков',
            'category_name' => 'Закупки'
        ],
        12 => [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Счета поставщиков',
            'category_name' => 'Закупки'
        ],
        13 => [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Счета поставщиков',
            'category_name' => 'Закупки'
        ],
        14 => [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Счета поставщиков',
            'category_name' => 'Закупки'
        ],
        15 => [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Счета поставщиков',
            'category_name' => 'Закупки'
        ],
        16 => [
            'guard_name' => PermissionNameEnum::VENDOR_INVOICES->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Счета поставщиков',
            'category_name' => 'Закупки'
        ],

        17 => [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Приемки',
            'category_name' => 'Закупки'
        ],
        18 => [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Приемки',
            'category_name' => 'Закупки'
        ],
        19 => [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Приемки',
            'category_name' => 'Закупки'
        ],
        20 => [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Приемки',
            'category_name' => 'Закупки'
        ],
        21 => [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Приемки',
            'category_name' => 'Закупки'
        ],
        22 => [
            'guard_name' => PermissionNameEnum::ACCEPTANCES->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Приемки',
            'category_name' => 'Закупки'
        ],

        23 => [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Возвраты поставщикам',
            'category_name' => 'Закупки'
        ],
        24 => [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Возвраты поставщикам',
            'category_name' => 'Закупки'
        ],
        25 => [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Возвраты поставщикам',
            'category_name' => 'Закупки'
        ],
        26 => [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Возвраты поставщикам',
            'category_name' => 'Закупки'
        ],
        27 => [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Возвраты поставщикам',
            'category_name' => 'Закупки'
        ],
        28 => [
            'guard_name' => PermissionNameEnum::VENDOR_CHARGBACK->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Возвраты поставщикам',
            'category_name' => 'Закупки'
        ],

        29 => [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Счета-фактуры полученные',
            'category_name' => 'Закупки'
        ],
        30 => [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Счета-фактуры полученные',
            'category_name' => 'Закупки'
        ],
        31 => [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Счета-фактуры полученные',
            'category_name' => 'Закупки'
        ],
        32 => [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Счета-фактуры полученные',
            'category_name' => 'Закупки'
        ],
        33 => [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Счета-фактуры полученные',
            'category_name' => 'Закупки'
        ],
        34 => [
            'guard_name' => PermissionNameEnum::RECEIVED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Счета-фактуры полученные',
            'category_name' => 'Закупки'
        ],

        35 => [
            'guard_name' => PermissionNameEnum::PROCUREMENT_MANAGEMENT->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Управление закупками',
            'category_name' => 'Закупки'
        ],

        36 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Счета покупателям',
            'category_name' => 'Продажи'
        ],
        37 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Счета покупателям',
            'category_name' => 'Продажи'
        ],
        38 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Счета покупателям',
            'category_name' => 'Продажи'
        ],
        39 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Счета покупателям',
            'category_name' => 'Продажи'
        ],
        40 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Счета покупателям',
            'category_name' => 'Продажи'
        ],
        41 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_INVOICES->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Счета покупателям',
            'category_name' => 'Продажи'
        ],

        42 => [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Отгрузки',
            'category_name' => 'Продажи'
        ],
        43 => [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Отгрузки',
            'category_name' => 'Продажи'
        ],
        44 => [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Отгрузки',
            'category_name' => 'Продажи'
        ],
        45 => [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Отгрузки',
            'category_name' => 'Продажи'
        ],
        46 => [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Отгрузки',
            'category_name' => 'Продажи'
        ],
        47 => [
            'guard_name' => PermissionNameEnum::SHIPMENTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Отгрузки',
            'category_name' => 'Продажи'
        ],

        48 => [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Полученый отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        49 => [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Полученый отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        50 => [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Полученый отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        51 => [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Полученый отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        52 => [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Полученый отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        53 => [
            'guard_name' => PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Полученый отчет комиссионера',
            'category_name' => 'Продажи'
        ],

        54 => [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Выданный отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        55 => [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Выданный отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        56 => [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Выданный отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        57 => [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Выданный отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        58 => [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Выданный отчет комиссионера',
            'category_name' => 'Продажи'
        ],
        59 => [
            'guard_name' => PermissionNameEnum::ISSUED_COMISSIONER_REPORTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Выданный отчет комиссионера',
            'category_name' => 'Продажи'
        ],

        60 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Возврат покупателя',
            'category_name' => 'Продажи'
        ],
        61 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Возврат покупателя',
            'category_name' => 'Продажи'
        ],
        62 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Возврат покупателя',
            'category_name' => 'Продажи'
        ],
        63 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Возврат покупателя',
            'category_name' => 'Продажи'
        ],
        64 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Возврат покупателя',
            'category_name' => 'Продажи'
        ],
        65 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_CHARGBACKS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Возврат покупателя',
            'category_name' => 'Продажи'
        ],

        66 => [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Счета-фактуры выданные',
            'category_name' => 'Продажи'
        ],
        67 => [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Счета-фактуры выданные',
            'category_name' => 'Продажи'
        ],
        68 => [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Счета-фактуры выданные',
            'category_name' => 'Продажи'
        ],
        69 => [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Счета-фактуры выданные',
            'category_name' => 'Продажи'
        ],
        70 => [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Счета-фактуры выданные',
            'category_name' => 'Продажи'
        ],
        71 => [
            'guard_name' => PermissionNameEnum::ISSUED_INVOICES->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Счета-фактуры выданные',
            'category_name' => 'Продажи'
        ],

        72 => [
            'guard_name' => PermissionNameEnum::PROFITABILITY->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Прибыльность',
            'category_name' => 'Показатели'
        ],
        73 => [
            'guard_name' => PermissionNameEnum::GOODS_ON_SALE->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Товары на реализации',
            'category_name' => 'Показатели'
        ],
        74 => [
            'guard_name' => PermissionNameEnum::SALES_FUNNEL->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Воронка продаж',
            'category_name' => 'Показатели'
        ],
        75 => [
            'guard_name' => PermissionNameEnum::UNIT_ECONOMICS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Юнит экономика',
            'category_name' => 'Показатели'
        ],

        76 => [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Товары и услуги',
            'category_name' => 'Товары'
        ],
        77 => [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Товары и услуги',
            'category_name' => 'Товары'
        ],
        78 => [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Товары и услуги',
            'category_name' => 'Товары'
        ],
        79 => [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Товары и услуги',
            'category_name' => 'Товары'
        ],
        80 => [
            'guard_name' => PermissionNameEnum::GOODS_AND_SERVICES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Товары и услуги',
            'category_name' => 'Товары'
        ],

        81 => [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Оприходования',
            'category_name' => 'Товары'
        ],
        82 => [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Оприходования',
            'category_name' => 'Товары'
        ],
        83 => [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Оприходования',
            'category_name' => 'Товары'
        ],
        84 => [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Оприходования',
            'category_name' => 'Товары'
        ],
        85 => [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Оприходования',
            'category_name' => 'Товары'
        ],
        86 => [
            'guard_name' => PermissionNameEnum::RECEIPTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Оприходования',
            'category_name' => 'Товары'
        ],

        87 => [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Списания',
            'category_name' => 'Товары'
        ],
        88 => [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Списания',
            'category_name' => 'Товары'
        ],
        89 => [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Списания',
            'category_name' => 'Товары'
        ],
        90 => [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Списания',
            'category_name' => 'Товары'
        ],
        91 => [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Списания',
            'category_name' => 'Товары'
        ],
        92 => [
            'guard_name' => PermissionNameEnum::WRITEOFFS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Списания',
            'category_name' => 'Товары'
        ],

        93 => [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Внутренние заказы',
            'category_name' => 'Товары'
        ],
        94 => [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Внутренние заказы',
            'category_name' => 'Товары'
        ],
        95 => [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Внутренние заказы',
            'category_name' => 'Товары'
        ],
        96 => [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Внутренние заказы',
            'category_name' => 'Товары'
        ],
        97 => [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Внутренние заказы',
            'category_name' => 'Товары'
        ],
        98 => [
            'guard_name' => PermissionNameEnum::INTERNAL_ORDERS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Внутренние заказы',
            'category_name' => 'Товары'
        ],

        99 => [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Перемещения',
            'category_name' => 'Товары'
        ],
        100 => [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Перемещения',
            'category_name' => 'Товары'
        ],
        101 => [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Перемещения',
            'category_name' => 'Товары'
        ],
        102 => [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Перемещения',
            'category_name' => 'Товары'
        ],
        103 => [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Перемещения',
            'category_name' => 'Товары'
        ],
        104 => [
            'guard_name' => PermissionNameEnum::MOVEMENTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Перемещения',
            'category_name' => 'Товары'
        ],

        105 => [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Прайс-листы',
            'category_name' => 'Товары'
        ],
        106 => [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Прайс-листы',
            'category_name' => 'Товары'
        ],
        107 => [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Прайс-листы',
            'category_name' => 'Товары'
        ],
        108 => [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Прайс-листы',
            'category_name' => 'Товары'
        ],
        109 => [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Прайс-листы',
            'category_name' => 'Товары'
        ],
        110 => [
            'guard_name' => PermissionNameEnum::PRICELISTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Прайс-листы',
            'category_name' => 'Товары'
        ],

        111 => [
            'guard_name' => PermissionNameEnum::RESIDUES->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Остатки',
            'category_name' => 'Товары'
        ],
        112 => [
            'guard_name' => PermissionNameEnum::TURNOVERS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Обороты',
            'category_name' => 'Товары'
        ],
        113 => [
            'guard_name' => PermissionNameEnum::SERIAL_NUMBERS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Серийные номера',
            'category_name' => 'Товары'
        ],

        114 => [
            'guard_name' => PermissionNameEnum::ORDER_MARKING_CODE->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Заказ кодов маркировки',
            'category_name' => 'Маркировка'
        ],
        115 => [
            'guard_name' => PermissionNameEnum::USAGE_REPORTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Отчет об использовании',
            'category_name' => 'Маркировка'
        ],
        116 => [
            'guard_name' => PermissionNameEnum::PUT_CIRCULATIONS_CODE->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Ввод в оборот кодов маркировки',
            'category_name' => 'Маркировка'
        ],
        117 => [
            'guard_name' => PermissionNameEnum::RETURN_TO_CIRCULATION->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Возврат в оборот',
            'category_name' => 'Маркировка'
        ],
        118 => [
            'guard_name' => PermissionNameEnum::WITHDRAWAL_FROM_CIRCULATION->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Вывод из оборота',
            'category_name' => 'Маркировка'
        ],
        119 => [
            'guard_name' => PermissionNameEnum::WITHDRAWAL_FROM_CIRCULATION_GTA->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Вывод из оборота (ОСУ)',
            'category_name' => 'Маркировка'
        ],
        120 => [
            'guard_name' => PermissionNameEnum::REMARKING->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Перемаркировка',
            'category_name' => 'Маркировка'
        ],
        121 => [
            'guard_name' => PermissionNameEnum::RESIDUALS_DESCRIPTION->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Описание остатков',
            'category_name' => 'Маркировка'
        ],
        122 => [
            'guard_name' => PermissionNameEnum::WRITEOFF_MARKING_CODES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Списание кодов маркировки',
            'category_name' => 'Маркировка'
        ],
        123 => [
            'guard_name' => PermissionNameEnum::PACKAGING_FORMATION->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Формирование упаковки',
            'category_name' => 'Маркировка'
        ],
        125 => [
            'guard_name' => PermissionNameEnum::PACKAGING_REMOVAL->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Изъятие из упаковки',
            'category_name' => 'Маркировка'
        ],
        127 => [
            'guard_name' => PermissionNameEnum::PACKAGING_UNPACKING->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Расформирование упаковки',
            'category_name' => 'Маркировка'
        ],
        128 => [
            'guard_name' => PermissionNameEnum::ATK_FORMATION->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Формирование АТК',
            'category_name' => 'Маркировка'
        ],
        129 => [
            'guard_name' => PermissionNameEnum::REPORT_CONNECTED_KEGS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Отчет о подключенных кегах',
            'category_name' => 'Маркировка'
        ],
        130 => [
            'guard_name' => PermissionNameEnum::MARKING_CODES->value,
            'require_scope' => true,
            'operations' => 'all',
            'group_name' => 'Коды маркировки',
            'category_name' => 'Маркировка'
        ],

        131 => [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Контрагенты',
            'category_name' => 'Контрагенты'
        ],
        132 => [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Контрагенты',
            'category_name' => 'Контрагенты'
        ],
        133 => [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Контрагенты',
            'category_name' => 'Контрагенты'
        ],
        134 => [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Контрагенты',
            'category_name' => 'Контрагенты'
        ],
        135 => [
            'guard_name' => PermissionNameEnum::CONTRACTORS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Контрагенты',
            'category_name' => 'Контрагенты'
        ],

        136 => [
            'guard_name' => PermissionNameEnum::CRM_INDICATORS_FOR_CONTRACTORS->value,
            'require_scope' => true,
            'operations' => 'all',
            'group_name' => 'Показатели CRM для контрагентов',
            'category_name' => 'Контрагенты'
        ],
        137 => [
            'guard_name' => PermissionNameEnum::CALL_LISTENING->value,
            'require_scope' => true,
            'operations' => 'all',
            'group_name' => 'Прослушивание звонков',
            'category_name' => 'Контрагенты'
        ],

        138 => [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Договоры',
            'category_name' => 'Контрагенты'
        ],
        139 => [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Договоры',
            'category_name' => 'Контрагенты'
        ],
        140 => [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Договоры',
            'category_name' => 'Контрагенты'
        ],
        141 => [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Договоры',
            'category_name' => 'Контрагенты'
        ],
        142 => [
            'guard_name' => PermissionNameEnum::CONTRACTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Договоры',
            'category_name' => 'Контрагенты'
        ],

        143 => [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Входящий платеж',
            'category_name' => 'Деньги'
        ],
        144 => [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Входящий платеж',
            'category_name' => 'Деньги'
        ],
        145 => [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Входящий платеж',
            'category_name' => 'Деньги'
        ],
        146 => [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Входящий платеж',
            'category_name' => 'Деньги'
        ],
        147 => [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Входящий платеж',
            'category_name' => 'Деньги'
        ],
        148 => [
            'guard_name' => PermissionNameEnum::INCOMING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Входящий платеж',
            'category_name' => 'Деньги'
        ],

        149 => [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Исходящий платеж',
            'category_name' => 'Деньги'
        ],
        150 => [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Исходящий платеж',
            'category_name' => 'Деньги'
        ],
        151 => [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Исходящий платеж',
            'category_name' => 'Деньги'
        ],
        152 => [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Исходящий платеж',
            'category_name' => 'Деньги'
        ],
        153 => [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Исходящий платеж',
            'category_name' => 'Деньги'
        ],
        154 => [
            'guard_name' => PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Исходящий платеж',
            'category_name' => 'Деньги'
        ],

        155 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Корректировки остатков в кассе',
            'category_name' => 'Деньги'
        ],
        156 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Корректировки остатков в кассе',
            'category_name' => 'Деньги'
        ],
        157 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Корректировки остатков в кассе',
            'category_name' => 'Деньги'
        ],
        158 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Корректировки остатков в кассе',
            'category_name' => 'Деньги'
        ],
        159 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_CASH_BALANCES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Корректировки остатков в кассе',
            'category_name' => 'Деньги'
        ],

        160 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Корректировки остатков на счете',
            'category_name' => 'Деньги'
        ],
        161 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Корректировки остатков на счете',
            'category_name' => 'Деньги'
        ],
        162 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Корректировки остатков на счете',
            'category_name' => 'Деньги'
        ],
        163 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Корректировки остатков на счете',
            'category_name' => 'Деньги'
        ],
        164 => [
            'guard_name' => PermissionNameEnum::ADJUSTMENTS_ACCOUNT_BALANCES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Корректировки остатков на счете',
            'category_name' => 'Деньги'
        ],

        165 => [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Корректировки взаиморасчетов',
            'category_name' => 'Деньги'
        ],
        166 => [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Корректировки взаиморасчетов',
            'category_name' => 'Деньги'
        ],
        167 => [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Корректировки взаиморасчетов',
            'category_name' => 'Деньги'
        ],
        168 => [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Корректировки взаиморасчетов',
            'category_name' => 'Деньги'
        ],
        169 => [
            'guard_name' => PermissionNameEnum::SETTLEMENT_ADJUSTMENTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Корректировки взаиморасчетов',
            'category_name' => 'Деньги'
        ],

        170 => [
            'guard_name' => PermissionNameEnum::SALARY_ACCRUALS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Начисления зарплаты',
            'category_name' => 'Деньги'
        ],
        171 => [
            'guard_name' => PermissionNameEnum::CASH_FLOW->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Движение денежных средств',
            'category_name' => 'Деньги'
        ],
        172 => [
            'guard_name' => PermissionNameEnum::OFFSETS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Взаиморасчеты',
            'category_name' => 'Деньги'
        ],
        173 => [
            'guard_name' => PermissionNameEnum::PROFIT_LOSS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Прибыли и убытки',
            'category_name' => 'Деньги'
        ],
        174 => [
            'guard_name' => PermissionNameEnum::SEENG_LEFTOVER_MONEY->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Видеть остатки денег',
            'category_name' => 'Деньги'
        ],

        175 => [
            'guard_name' => PermissionNameEnum::POINTS_SALE->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Точки продаж',
            'category_name' => 'Розница'
        ],
        176 => [
            'guard_name' => PermissionNameEnum::POINTS_SALE->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Точки продаж',
            'category_name' => 'Розница'
        ],
        177 => [
            'guard_name' => PermissionNameEnum::POINTS_SALE->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Точки продаж',
            'category_name' => 'Розница'
        ],
        178 => [
            'guard_name' => PermissionNameEnum::POINTS_SALE->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Точки продаж',
            'category_name' => 'Розница'
        ],

        179 => [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Смены',
            'category_name' => 'Розница'
        ],
        180 => [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Смены',
            'category_name' => 'Розница'
        ],
        181 => [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Смены',
            'category_name' => 'Розница'
        ],
        182 => [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Смены',
            'category_name' => 'Розница'
        ],
        183 => [
            'guard_name' => PermissionNameEnum::SHIFTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Смены',
            'category_name' => 'Розница'
        ],

        184 => [
            'guard_name' => PermissionNameEnum::SALES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Продажи',
            'category_name' => 'Розница'
        ],
        185 => [
            'guard_name' => PermissionNameEnum::SALES->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Продажи',
            'category_name' => 'Розница'
        ],
        186 => [
            'guard_name' => PermissionNameEnum::SALES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Продажи',
            'category_name' => 'Розница'
        ],
        187 => [
            'guard_name' => PermissionNameEnum::SALES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Продажи',
            'category_name' => 'Розница'
        ],
        188 => [
            'guard_name' => PermissionNameEnum::SALES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Продажи',
            'category_name' => 'Розница'
        ],
        189 => [
            'guard_name' => PermissionNameEnum::SALES->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Продажи',
            'category_name' => 'Розница'
        ],

        190 => [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Возвраты',
            'category_name' => 'Розница'
        ],
        191 => [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Возвраты',
            'category_name' => 'Розница'
        ],
        192 => [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Возвраты',
            'category_name' => 'Розница'
        ],
        193 => [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Возвраты',
            'category_name' => 'Розница'
        ],
        194 => [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Возвраты',
            'category_name' => 'Розница'
        ],
        195 => [
            'guard_name' => PermissionNameEnum::RETURNS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Возвраты',
            'category_name' => 'Розница'
        ],

        196 => [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Внесения',
            'category_name' => 'Розница'
        ],
        197 => [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Внесения',
            'category_name' => 'Розница'
        ],
        198 => [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Внесения',
            'category_name' => 'Розница'
        ],
        199 => [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Внесения',
            'category_name' => 'Розница'
        ],
        200 => [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Внесения',
            'category_name' => 'Розница'
        ],
        201 => [
            'guard_name' => PermissionNameEnum::INCLUSIONS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Внесения',
            'category_name' => 'Розница'
        ],

        202 => [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Выплаты',
            'category_name' => 'Розница'
        ],
        203 => [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Выплаты',
            'category_name' => 'Розница'
        ],
        204 => [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Выплаты',
            'category_name' => 'Розница'
        ],
        205 => [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Выплаты',
            'category_name' => 'Розница'
        ],
        206 => [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Выплаты',
            'category_name' => 'Розница'
        ],
        207 => [
            'guard_name' => PermissionNameEnum::PAYOUTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Выплаты',
            'category_name' => 'Розница'
        ],

        208 => [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Бонусные баллы',
            'category_name' => 'Розница'
        ],
        209 => [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Бонусные баллы',
            'category_name' => 'Розница'
        ],
        210 => [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Бонусные баллы',
            'category_name' => 'Розница'
        ],
        211 => [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Бонусные баллы',
            'category_name' => 'Розница'
        ],
        212 => [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Бонусные баллы',
            'category_name' => 'Розница'
        ],
        213 => [
            'guard_name' => PermissionNameEnum::BONUS_POINTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Бонусные баллы',
            'category_name' => 'Розница'
        ],

        214 => [
            'guard_name' => PermissionNameEnum::PREPAYMENTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Предоплаты',
            'category_name' => 'Розница'
        ],
        215 => [
            'guard_name' =>  PermissionNameEnum::PREPAYMENTS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Предоплаты',
            'category_name' => 'Розница'
        ],
        216 => [
            'guard_name' =>  PermissionNameEnum::PREPAYMENTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Предоплаты',
            'category_name' => 'Розница'
        ],
        217 => [
            'guard_name' =>  PermissionNameEnum::PREPAYMENTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Предоплаты',
            'category_name' => 'Розница'
        ],
        218 => [
            'guard_name' =>  PermissionNameEnum::PREPAYMENTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Предоплаты',
            'category_name' => 'Розница'
        ],
        219 => [
            'guard_name' =>  PermissionNameEnum::PREPAYMENTS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Предоплаты',
            'category_name' => 'Розница'
        ],

        220 => [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Возвраты предоплат',
            'category_name' => 'Розница'
        ],
        221 => [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Возвраты предоплат',
            'category_name' => 'Розница'
        ],
        222 => [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Возвраты предоплат',
            'category_name' => 'Розница'
        ],
        223 => [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Возвраты предоплат',
            'category_name' => 'Розница'
        ],
        224 => [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Возвраты предоплат',
            'category_name' => 'Розница'
        ],
        225 => [
            'guard_name' => PermissionNameEnum::PREPAYMENT_REFUNDS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Возвраты предоплат',
            'category_name' => 'Розница'
        ],

        226 => [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Техоперации',
            'category_name' => 'Производство'
        ],
        227 => [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Техоперации',
            'category_name' => 'Производство'
        ],
        228 => [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Техоперации',
            'category_name' => 'Производство'
        ],
        229 => [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Техоперации',
            'category_name' => 'Производство'
        ],
        230 => [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Техоперации',
            'category_name' => 'Производство'
        ],
        231 => [
            'guard_name' => PermissionNameEnum::TECHOPERATIONS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Техоперации',
            'category_name' => 'Производство'
        ],

        232 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Заказы на производство',
            'category_name' => 'Производство'
        ],
        233 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Заказы на производство',
            'category_name' => 'Производство'
        ],
        234 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Заказы на производство',
            'category_name' => 'Производство'
        ],
        235 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Заказы на производство',
            'category_name' => 'Производство'
        ],
        236 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Заказы на производство',
            'category_name' => 'Производство'
        ],
        237 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_ORDERS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Заказы на производство',
            'category_name' => 'Производство'
        ],

        238 => [
            'guard_name' => PermissionNameEnum::TECHCARDS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Техкарты',
            'category_name' => 'Производство'
        ],
        239 => [
            'guard_name' => PermissionNameEnum::TECHCARDS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Техкарты',
            'category_name' => 'Производство'
        ],
        240 => [
            'guard_name' => PermissionNameEnum::TECHCARDS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Техкарты',
            'category_name' => 'Производство'
        ],
        241 => [
            'guard_name' => PermissionNameEnum::TECHCARDS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Техкарты',
            'category_name' => 'Производство'
        ],

        242 => [
            'guard_name' => PermissionNameEnum::TECHPROCESSES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Техпроцессы',
            'category_name' => 'Производство'
        ],
        243 => [
            'guard_name' => PermissionNameEnum::TECHPROCESSES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Техпроцессы',
            'category_name' => 'Производство'
        ],
        244 => [
            'guard_name' => PermissionNameEnum::TECHPROCESSES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Техпроцессы',
            'category_name' => 'Производство'
        ],
        245 => [
            'guard_name' => PermissionNameEnum::TECHPROCESSES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Техпроцессы',
            'category_name' => 'Производство'
        ],

        246 => [
            'guard_name' => PermissionNameEnum::STAGES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Этапы',
            'category_name' => 'Производство'
        ],
        247 => [
            'guard_name' => PermissionNameEnum::STAGES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Этапы',
            'category_name' => 'Производство'
        ],
        248 => [
            'guard_name' => PermissionNameEnum::STAGES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Этапы',
            'category_name' => 'Производство'
        ],
        249 => [
            'guard_name' => PermissionNameEnum::STAGES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Этапы',
            'category_name' => 'Производство'
        ],

        250 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Производственные задания',
            'category_name' => 'Производство'
        ],
        251 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Производственные задания',
            'category_name' => 'Производство'
        ],
        252 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Производственные задания',
            'category_name' => 'Производство'
        ],
        253 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Производственные задания',
            'category_name' => 'Производство'
        ],
        254 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Производственные задания',
            'category_name' => 'Производство'
        ],
        255 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_TASKS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Производственные задания',
            'category_name' => 'Производство'
        ],

        256 => [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Выполнение этапов',
            'category_name' => 'Производство'
        ],
        257 => [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Выполнение этапов',
            'category_name' => 'Производство'
        ],
        258 => [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Выполнение этапов',
            'category_name' => 'Производство'
        ],
        259 => [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Выполнение этапов',
            'category_name' => 'Производство'
        ],
        260 => [
            'guard_name' => PermissionNameEnum::IMPLEMENTATION_STEPS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Выполнение этапов',
            'category_name' => 'Производство'
        ],

        261 => [
            'guard_name' => PermissionNameEnum::REMUNERATION->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Оплата труда',
            'category_name' => 'Производство'
        ],

        262 => [
            'guard_name' => PermissionNameEnum::PRODUCTION_STATUS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Статус производства',
            'category_name' => 'Производство'
        ],

        263 => [
            'guard_name' => PermissionNameEnum::SUBSCRIPTION_MANAGEMENT->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Управление подпиской',
            'category_name' => 'Настройки'
        ],

        264 => [
            'guard_name' => PermissionNameEnum::SEND_MAILS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Отправлять почту',
            'category_name' => 'Настройки'
        ],

        265 => [
            'guard_name' => PermissionNameEnum::SEE_PRICES->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Видеть себестоимость, цену закупки и прибыль товаров',
            'category_name' => 'Настройки'
        ],

        266 => [
            'guard_name' => PermissionNameEnum::EDIT_CLOSE_PERIOD_DOCUMENTS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Редактировать документы закрытого периода',
            'category_name' => 'Настройки'
        ],

        267 => [
            'guard_name' => PermissionNameEnum::EDIT_DOCUMENT_TEMPLATES->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Редактировать шаблоны документов и отчётов',
            'category_name' => 'Настройки'
        ],

        268 => [
            'guard_name' => PermissionNameEnum::EDIT_DOCUMENT_CURRENCY->value,
            'require_scope' => true,
            'operations' => 'all',
            'group_name' => 'Редактировать курс валюты документа',
            'category_name' => 'Настройки'
        ],

        269 => [
            'guard_name' => PermissionNameEnum::SCENARIOS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Сценарии',
            'category_name' => 'Сценарии'
        ],

        270 => [
            'guard_name' => PermissionNameEnum::IMPORT->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Импорт',
            'category_name' => 'Обмен данными'
        ],
        271 => [
            'guard_name' => PermissionNameEnum::EXPORT->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Экспорт',
            'category_name' => 'Обмен данными'
        ],
        272 => [
            'guard_name' => PermissionNameEnum::ONLINE_SHOPS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Интернет-магазины',
            'category_name' => 'Обмен данными'
        ],
        273 => [
            'guard_name' => PermissionNameEnum::API_ACCESS->value,
            'require_scope' => false,
            'operations' => 'all',
            'group_name' => 'Доступ по API',
            'category_name' => 'Обмен данными'
        ],

        274 => [
            'guard_name' => PermissionNameEnum::LEGAL_ENTITIES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Юр. лица',
            'category_name' => 'Справочники'
        ],
        276 => [
            'guard_name' => PermissionNameEnum::LEGAL_ENTITIES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Юр. лица',
            'category_name' => 'Справочники'
        ],
        277 => [
            'guard_name' => PermissionNameEnum::LEGAL_ENTITIES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Юр. лица',
            'category_name' => 'Справочники'
        ],
        278 => [
            'guard_name' => PermissionNameEnum::LEGAL_ENTITIES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Юр. лица',
            'category_name' => 'Справочники'
        ],

        279 => [
            'guard_name' => PermissionNameEnum::EMPLOYEES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Сотрудники',
            'category_name' => 'Справочники'
        ],
        280 => [
            'guard_name' => PermissionNameEnum::EMPLOYEES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Сотрудники',
            'category_name' => 'Справочники'
        ],
        281 => [
            'guard_name' => PermissionNameEnum::EMPLOYEES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Сотрудники',
            'category_name' => 'Справочники'
        ],
        282 => [
            'guard_name' => PermissionNameEnum::EMPLOYEES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Сотрудники',
            'category_name' => 'Справочники'
        ],

        283 => [
            'guard_name' => PermissionNameEnum::WAREHOUSES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Склады',
            'category_name' => 'Справочники'
        ],
        284 => [
            'guard_name' => PermissionNameEnum::WAREHOUSES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Склады',
            'category_name' => 'Справочники'
        ],
        285 => [
            'guard_name' => PermissionNameEnum::WAREHOUSES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Склады',
            'category_name' => 'Справочники'
        ],
        286 => [
            'guard_name' => PermissionNameEnum::WAREHOUSES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Склады',
            'category_name' => 'Справочники'
        ],
        288 => [
            'guard_name' => PermissionNameEnum::CURRENCIES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Валюты',
            'category_name' => 'Справочники'
        ],
        289 => [
            'guard_name' => PermissionNameEnum::CURRENCIES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Валюты',
            'category_name' => 'Справочники'
        ],
        290 => [
            'guard_name' => PermissionNameEnum::CURRENCIES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Валюты',
            'category_name' => 'Справочники'
        ],

        291 => [
            'guard_name' => PermissionNameEnum::PROJECTS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Проекты',
            'category_name' => 'Справочники'
        ],
        292 => [
            'guard_name' => PermissionNameEnum::PROJECTS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Проекты',
            'category_name' => 'Справочники'
        ],
        293 => [
            'guard_name' => PermissionNameEnum::PROJECTS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Проекты',
            'category_name' => 'Справочники'
        ],
        294 => [
            'guard_name' => PermissionNameEnum::PROJECTS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Проекты',
            'category_name' => 'Справочники'
        ],

        295 => [
            'guard_name' => PermissionNameEnum::ADDITIONAL_DIRECTORIES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Дополнительные справочники',
            'category_name' => 'Справочники'
        ],
        296 => [
            'guard_name' => PermissionNameEnum::ADDITIONAL_DIRECTORIES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Дополнительные справочники',
            'category_name' => 'Справочники'
        ],
        297 => [
            'guard_name' => PermissionNameEnum::ADDITIONAL_DIRECTORIES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Дополнительные справочники',
            'category_name' => 'Справочники'
        ],
        298 => [
            'guard_name' => PermissionNameEnum::ADDITIONAL_DIRECTORIES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Дополнительные справочники',
            'category_name' => 'Справочники'
        ],

        299 => [
            'guard_name' => PermissionNameEnum::COUNTRIES->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Страны',
            'category_name' => 'Справочники'
        ],
        300 => [
            'guard_name' => PermissionNameEnum::COUNTRIES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Страны',
            'category_name' => 'Справочники'
        ],
        301 => [
            'guard_name' => PermissionNameEnum::COUNTRIES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Страны',
            'category_name' => 'Справочники'
        ],
        302 => [
            'guard_name' => PermissionNameEnum::COUNTRIES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Страны',
            'category_name' => 'Справочники'
        ],

        304 => [
            'guard_name' => PermissionNameEnum::MEASUREMENT_UNITS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Единицы измерения',
            'category_name' => 'Справочники'
        ],
        305 => [
            'guard_name' => PermissionNameEnum::MEASUREMENT_UNITS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Единицы измерения',
            'category_name' => 'Справочники'
        ],
        306 => [
            'guard_name' => PermissionNameEnum::MEASUREMENT_UNITS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Единицы измерения',
            'category_name' => 'Справочники'
        ],

        307 => [
            'guard_name' => PermissionNameEnum::SALE_CHANNELS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Каналы продаж',
            'category_name' => 'Справочники'
        ],
        308 => [
            'guard_name' => PermissionNameEnum::SALE_CHANNELS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Каналы продаж',
            'category_name' => 'Справочники'
        ],
        309 => [
            'guard_name' => PermissionNameEnum::SALE_CHANNELS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Каналы продаж',
            'category_name' => 'Справочники'
        ],
        310 => [
            'guard_name' => PermissionNameEnum::SALE_CHANNELS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Каналы продаж',
            'category_name' => 'Справочники'
        ],
        312 => [
            'guard_name' => PermissionNameEnum::VAT_RATES->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Ставки НДС',
            'category_name' => 'Справочники'
        ],
        313 => [
            'guard_name' => PermissionNameEnum::VAT_RATES->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Ставки НДС',
            'category_name' => 'Справочники'
        ],
        314 => [
            'guard_name' => PermissionNameEnum::VAT_RATES->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Ставки НДС',
            'category_name' => 'Справочники'
        ],

        315 => [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Задачи',
            'category_name' => 'Задачи'
        ],
        316 => [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Задачи',
            'category_name' => 'Задачи'
        ],
        319 => [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'require_scope' => true,
            'operations' => 'perform',
            'group_name' => 'Задачи',
            'category_name' => 'Задачи'
        ],
        317 => [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Задачи',
            'category_name' => 'Задачи'
        ],
        318 => [
            'guard_name' => PermissionNameEnum::TASKS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Задачи',
            'category_name' => 'Задачи'
        ],

        320 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'require_scope' => true,
            'operations' => 'view',
            'group_name' => 'Заказы покупателей',
            'category_name' => 'Продажи'
        ],
        321 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'require_scope' => true,
            'operations' => 'print',
            'group_name' => 'Заказы покупателей',
            'category_name' => 'Продажи'
        ],
        322 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'require_scope' => false,
            'operations' => 'create',
            'group_name' => 'Заказы покупателей',
            'category_name' => 'Продажи'
        ],
        323 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'require_scope' => true,
            'operations' => 'update',
            'group_name' => 'Заказы покупателей',
            'category_name' => 'Продажи'
        ],
        324 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'require_scope' => true,
            'operations' => 'delete',
            'group_name' => 'Заказы покупателей',
            'category_name' => 'Продажи'
        ],
        325 => [
            'guard_name' => PermissionNameEnum::CUSTOMER_ORDERS->value,
            'require_scope' => true,
            'operations' => 'held',
            'group_name' => 'Заказы покупателей',
            'category_name' => 'Продажи'
        ],

    ],
    'groups' => [
        0 => [
            'name' => 'Просматривать показатели',
        ],
        1 => [
            'name' => 'Просматривать корзину',
        ],
        2 => [
            'name' => 'Восстанавливать документы',
        ],
        3 => [
            'name' => 'Очищать корзину',
        ],
        4 => [
            'name' => 'Просматривать аудит',
        ],
        5 => [
            'name' => 'Заказы поставщикам',
        ],
        6 => [
            'name' => 'Счета поставщиков',
        ],
        7 => [
            'name' => 'Приемки',
        ],
        8 => [
            'name' => 'Возвраты поставщикам',
        ],
        9 => [
            'name' => 'Счета-фактуры полученные',
        ],
        10 => [
            'name' => 'Управление закупками',
        ],
        11 => [
            'name' => 'Заказы покупателей',
        ],
        12 => [
            'name' => 'Счета покупателям',
        ],
        13 => [
            'name' => 'Отгрузки',
        ],
        14 => [
            'name' => 'Полученый отчет комиссионера',
        ],
        15 => [
            'name' => 'Выданный отчет комиссионера',
        ],
        16 => [
            'name' => 'Возврат покупателя',
        ],
        17 => [
            'name' => 'Счета-фактуры выданные',
        ],
        18 => [
            'name' => 'Прибыльность',
        ],
        19 => [
            'name' => 'Товары на реализации',
        ],
        20 => [
            'name' => 'Воронка продаж',
        ],
        21 => [
            'name' => 'Юнит экономика',
        ],
        22 => [
            'name' => 'Товары и услуги',
        ],
        23 => [
            'name' => 'Оприходования',
        ],
        24 => [
            'name' => 'Списания',
        ],
        25 => [
            'name' => 'Инвентаризация',
        ],
        26 => [
            'name' => 'Внутренние заказы',
        ],
        27 => [
            'name' => 'Перемещения',
        ],
        28 => [
            'name' => 'Прайс-листы',
        ],
        29 => [
            'name' => 'Остатки',
        ],
        30 => [
            'name' => 'Обороты',
        ],
        31 => [
            'name' => 'Серийные номера',
        ],
        32 => [
            'name' => 'Заказ кодов маркировки',
        ],
        33 => [
            'name' => 'Отчет об использовании',
        ],
        34 => [
            'name' => 'Ввод в оборот кодов маркировки',
        ],
        35 => [
            'name' => 'Возврат в оборот',
        ],
        36 => [
            'name' => 'Вывод из оборота',
        ],
        37 => [
            'name' => 'Вывод из оборота (ОСУ)',
        ],
        38 => [
            'name' => 'Перемаркировка',
        ],
        39 => [
            'name' => 'Описание остатков',
        ],
        40 => [
            'name' => 'Списание кодов маркировки',
        ],
        41 => [
            'name' => 'Формирование упаковки',
        ],
        42 => [
            'name' => 'Изъятие из упаковки',
        ],
        43 => [
            'name' => 'Расформирование упаковки',
        ],
        44 => [
            'name' => 'Формирование АТК',
        ],
        45 => [
            'name' => 'Отчет о подключенных кегах',
        ],
        46 => [
            'name' => 'Коды маркировки',
        ],
        47 => [
            'name' => 'Контрагенты',
        ],
        48 => [
            'name' => 'Показатели CRM для контрагентов',
        ],
        49 => [
            'name' => 'Прослушивание звонков',
        ],
        50 => [
            'name' => 'Договоры',
        ],
        51 => [
            'name' => 'Входящий платеж',
        ],
        52 => [
            'name' => 'Исходящий платеж',
        ],
        53 => [
            'name' => 'Корректировки остатков в кассе',
        ],
        54 => [
            'name' => 'Корректировки остатков на счете',
        ],
        55 => [
            'name' => 'Корректировки взаиморасчетов',
        ],
        56 => [
            'name' => 'Начисления зарплаты',
        ],
        57 => [
            'name' => 'Движение денежных средств',
        ],
        58 => [
            'name' => 'Взаиморасчеты',
        ],
        59 => [
            'name' => 'Точки продаж',
        ],
        60 => [
            'name' => 'Смены',
        ],
        61 => [
            'name' => 'Продажи',
        ],
        62 => [
            'name' => 'Возвраты',
        ],
        63 => [
            'name' => 'Внесения',
        ],
        64 => [
            'name' => 'Выплаты',
        ],
        65 => [
            'name' => 'Бонусные баллы',
        ],
        66 => [
            'name' => 'Предоплаты',
        ],
        67 => [
            'name' => 'Возвраты предоплат',
        ],
        68 => [
            'name' => 'Техоперации',
        ],
        69 => [
            'name' => 'Заказы на производство',
        ],
        70 => [
            'name' => 'Техкарты',
        ],
        71 => [
            'name' => 'Техпроцессы',
        ],
        72 => [
            'name' => 'Этапы',
        ],
        73 => [
            'name' => 'Производственные задания',
        ],
        74 => [
            'name' => 'Выполнение этапов',
        ],
        75 => [
            'name' => 'Оплата труда',
        ],
        76 => [
            'name' => 'Статус производства',
        ],
        77 => [
            'name' => 'Управление подпиской',
        ],
        78 => [
            'name' => 'Отправлять почту',
        ],
        79 => [
            'name' => 'Видеть себестоимость, цену закупки и прибыль товаров',
        ],
        80 => [
            'name' => 'Редактировать документы закрытого периода',
        ],
        81 => [
            'name' => 'Редактировать шаблоны документов и отчётов',
        ],
        82 => [
            'name' => 'Редактировать курс валюты документа',
        ],
        83 => [
            'name' => 'Импорт',
        ],
        84 => [
            'name' => 'Экспорт',
        ],
        85 => [
            'name' => 'Интернет-магазины',
        ],
        86 => [
            'name' => 'Доступ по API',
        ],
        87 => [
            'name' => 'Юр. лица',
        ],
        88 => [
            'name' => 'Сотрудники',
        ],
        89 => [
            'name' => 'Склады',
        ],
        90 => [
            'name' => 'Валюты',
        ],
        91 => [
            'name' => 'Проекты',
        ],
        92 => [
            'name' => 'Дополнительные справочники',
        ],
        93 => [
            'name' => 'Страны',
        ],
        94 => [
            'name' => 'Единицы измерения',
        ],
        95 => [
            'name' => 'Каналы продаж',
        ],
        96 => [
            'name' => 'Ставки НДС',
        ],
        97 => [
            'name' => 'Задачи',
        ],
        98 => [
            'name' => 'Прибыли и убытки',
        ],
        99 => [
            'name' => 'Видеть остатки денег',
        ],
        100 => [
            'name' => 'Сценарии',
        ],
    ],
    'categories' => [
        0 => [
            'name' => 'Показатели',
        ],
        1 => [
            'name' => 'Закупки',
        ],
        2 => [
            'name' => 'Продажи',
        ],
        3 => [
            'name' => 'Товары',
        ],
        4 => [
            'name' => 'Маркировка',
        ],
        5 => [
            'name' => 'Контрагенты',
        ],
        6 => [
            'name' => 'Деньги',
        ],
        7 => [
            'name' => 'Розница',
        ],
        8 => [
            'name' => 'Производство',
        ],
        9 => [
            'name' => 'Настройки',
        ],
        10 => [
            'name' => 'Сценарии',
        ],
        11 => [
            'name' => 'Обмен данными',
        ],
        12 => [
            'name' => 'Справочники',
        ],
        13 => [
            'name' => 'Задачи',
        ],
    ],
];
