<?php

declare(strict_types=1);

namespace App\Clients\Ozon\API\Endpoint;

use App\Clients\Ozon\API\AbstractEndpoint;

class FBSWarehouses extends AbstractEndpoint
{
    public function list()
    {
        return $this->postWithouBodyRequest("/v1/warehouse/list");
    }

    /**
     * Получить список методов доставки склада
     *
     * @param array $filter Фильтр для поиска методов доставки
     * @param int $limit Количество элементов в ответе (максимум 50, минимум 1)
     * @param int $offset Количество элементов для пропуска
     * @return object Ответ API со списком методов доставки
     */
    public function getDeliveryMethods(array $filter = [], int $limit = 50, int $offset = 0): object
    {
        $body = [
            'filter' => (object) $filter,
            'limit' => $limit,
            'offset' => $offset,
        ];

        return $this->postRequest("/v1/delivery-method/list", $body);
    }
}
