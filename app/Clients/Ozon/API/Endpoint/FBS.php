<?php

declare(strict_types=1);

namespace App\Clients\Ozon\API\Endpoint;

use App\Clients\Ozon\API\AbstractEndpoint;
use App\Clients\WB\Exception\ApiTimeRestrictionsException;
use InvalidArgumentException;
use stdClass;

class FBS extends AbstractEndpoint
{
    /**
     * @throws ApiTimeRestrictionsException
     */
    public function unfulfilledList(string $dir = 'desc', object $filter = new stdClass(), int $limit = 1000, int $offset = 0, object $with = new stdClass())
    {
        $body = [
            'dir' => $dir,
            'filter' => $filter,
            'limit' => $limit,
            'offset' => $offset,
        ];

        if (!empty($with)) {
            $body['with'] = $with;
        }

        return $this->postRequest("/v3/posting/fbs/unfulfilled/list", $body);
    }
    /**
     * @throws ApiTimeRestrictionsException
     */
    public function list(string $dir = 'desc', object $filter = new stdClass(), int $limit = 1000, int $offset = 0, object $with = new stdClass())
    {
        $body = [
            'dir' => $dir,
            'filter' => $filter,
            'limit' => $limit,
            'offset' => $offset,
        ];

        if (!empty($with)) {
            $body['with'] = $with;
        }

        return $this->postRequest("/v3/posting/fbs/list", $body);
    }

    /**
     * @throws ApiTimeRestrictionsException
     */
    public function fboList(string $dir = 'desc', object $filter = new stdClass(), int $limit = 1000, int $offset = 0, object $with = new stdClass())
    {
        $body = [
            'dir' => $dir,
            'filter' => $filter,
            'limit' => $limit,
            'offset' => $offset,
        ];

        if (!empty($with)) {
            $body['with'] = $with;
        }

        return $this->postRequest("/v2/posting/fbo/list", $body);
    }

    /**
     * @throws ApiTimeRestrictionsException
     */
    public function getCancelReasons(array $relatedPostingNumbers)
    {
        return $this->postRequest("/v1/posting/fbs/cancel-reason", [
            'related_posting_numbers' => $relatedPostingNumbers,
        ]);
    }

    public function cancelOrder(string $postingNumber, int $cancelReasonId, ?string $cancelReasonMessage): void
    {
        $body = [
            'posting_number' => $postingNumber,
            'cancel_reason_id' => $cancelReasonId,
        ];

        if ($cancelReasonId === 402 && is_null($cancelReasonMessage)) {
            throw new InvalidArgumentException('Cancel reason message is required for cancel reason id 402');
        }

        if ($cancelReasonId === 402 || !is_null($cancelReasonMessage)) {
            $body['cancel_reason_message'] = $cancelReasonMessage;
        }

        $this->postRequest("/v2/posting/fbs/cancel", $body);
    }

    /**
     * Отмена отдельных товаров в отправлении
     *
     * @param string $postingNumber Номер отправления
     * @param int $cancelReasonId Идентификатор причины отмены
     * @param string $cancelReasonMessage Дополнительная информация по отмене
     * @param array $items Массив товаров для отмены [['sku' => int, 'quantity' => int], ...]
     * @throws InvalidArgumentException
     * @throws ApiTimeRestrictionsException
     */
    public function cancelOrderItems(string $postingNumber, int $cancelReasonId, string $cancelReasonMessage, array $items): void
    {
        $body = [
            'posting_number' => $postingNumber,
            'cancel_reason_id' => $cancelReasonId,
            'cancel_reason_message' => $cancelReasonMessage,
            'items' => $items,
        ];

        $this->postRequest("/v2/posting/fbs/product/cancel", $body);
    }

    /**
     * Добавить информацию о стране-изготовителе товара
     *
     * @param string $postingNumber Номер отправления
     * @param int $productId Идентификатор товара в системе продавца
     * @param string $countryIsoCode Двухбуквенный код страны по стандарту ISO_3166-1
     * @return object Ответ API с product_id и is_gtd_needed
     * @throws ApiTimeRestrictionsException
     */
    public function setProductCountry(string $postingNumber, int $productId, string $countryIsoCode): object
    {
        $body = [
            'posting_number' => $postingNumber,
            'product_id' => $productId,
            'country_iso_code' => $countryIsoCode,
        ];

        return $this->postRequest("/v2/posting/fbs/product/country/set", $body);
    }

    /**
     * Получить список доступных стран-изготовителей
     *
     * @return object Список стран с ISO кодами
     * @throws ApiTimeRestrictionsException
     */
    public function getCountryList(): object
    {
        return $this->postRequest("/v2/posting/fbs/product/country/list", []);
    }

    /**
     * Собрать заказ и разделить на отправления (версия 4)
     *
     * @param string $postingNumber Номер отправления
     * @param array $packages Список упаковок с товарами
     * @param object|null $with Дополнительная информация
     * @return object Ответ API
     * @throws ApiTimeRestrictionsException
     */
    public function shipOrder(string $postingNumber, array $packages, ?object $with = null): object
    {
        $body = [
            'posting_number' => $postingNumber,
            'packages' => $packages,
        ];

        if ($with !== null) {
            $body['with'] = $with;
        }

        return $this->postRequest("/v4/posting/fbs/ship", $body);
    }

    /**
     * Получить этикетки для отправлений
     *
     * @param array $postingNumbers Массив номеров отправлений (максимум 20)
     * @return object Ответ API с PDF файлом этикеток
     * @throws ApiTimeRestrictionsException
     * @throws InvalidArgumentException
     */
    public function getPackageLabels(array $postingNumbers): object
    {
        if (count($postingNumbers) > 20) {
            throw new InvalidArgumentException('Maximum 20 posting numbers allowed per request');
        }

        if (empty($postingNumbers)) {
            throw new InvalidArgumentException('At least one posting number is required');
        }

        $body = [
            'posting_number' => $postingNumbers,
        ];

        return $this->postRequest("/v2/posting/fbs/package-label", $body);
    }

    /**
     * Создать отгрузку
     *
     * @param int $deliveryMethodId Идентификатор метода доставки
     * @param string $departureDate Дата отгрузки в формате ISO 8601
     * @return object Ответ API с carriage_id
     * @throws ApiTimeRestrictionsException
     */
    public function createCarriage(int $deliveryMethodId, string $departureDate): object
    {
        $body = [
            'delivery_method_id' => $deliveryMethodId,
            'departure_date' => $departureDate,
        ];

        return $this->postRequest("/v1/carriage/create", $body);
    }

    /**
     * Подтвердить отгрузку
     *
     * @param int $carriageId Идентификатор отгрузки
     * @param int|null $containersCount Количество грузовых мест (для доверительной приёмки)
     * @return object Ответ API
     * @throws ApiTimeRestrictionsException
     */
    public function approveCarriage(int $carriageId, ?int $containersCount = null): object
    {
        $body = [
            'carriage_id' => $carriageId,
        ];

        if ($containersCount !== null) {
            $body['containers_count'] = $containersCount;
        }

        return $this->postRequest("/v1/carriage/approve", $body);
    }

    /**
     * Получить информацию об отгрузке
     *
     * @param int $carriageId Идентификатор отгрузки
     * @return object Ответ API с детальной информацией об отгрузке
     * @throws ApiTimeRestrictionsException
     */
    public function getCarriageInfo(int $carriageId): object
    {
        $body = [
            'carriage_id' => $carriageId,
        ];

        return $this->postRequest("/v1/carriage/get", $body);
    }

    public function createArrivalPass(int $carriageId, array $arrivalPasses): object
    {
        $body = [
            'carriage_id' => $carriageId,
            'arrival_passes' => $arrivalPasses,
        ];

        return $this->postRequest("/v1/carriage/pass/create", $body);
    }

    public function updateArrivalPass(int $carriageId, array $arrivalPasses): object
    {
        $body = [
            'carriage_id' => $carriageId,
            'arrival_passes' => $arrivalPasses,
        ];

        return $this->postRequest("/v1/carriage/pass/update", $body);
    }

    public function deleteArrivalPass(int $carriageId, array $arrivalPassIds): object
    {
        $body = [
            'carriage_id' => $carriageId,
            'arrival_pass_ids' => $arrivalPassIds,
        ];

        return $this->postRequest("/v1/carriage/pass/delete", $body);
    }

    public function setCarriagePostings(int $carriageId, array $postingNumbers): object
    {
        $body = [
            'carriage_id' => $carriageId,
            'posting_numbers' => $postingNumbers,
        ];

        return $this->postRequest("/v1/carriage/set-postings", $body);
    }

    public function cancelCarriage(int $carriageId): object
    {
        $body = [
            'carriage_id' => $carriageId,
        ];

        return $this->postRequest("/v1/carriage/cancel", $body);
    }

    public function checkActStatus(int $carriageId): object
    {
        $body = [
            'id' => $carriageId,
        ];

        return $this->postRequest("/v2/posting/fbs/act/check-status", $body);
    }

    public function getActBarcode(int $carriageId): object
    {
        $body = [
            'id' => $carriageId,
        ];

        return $this->postRequest("/v2/posting/fbs/act/get-barcode", $body);
    }

    public function checkDigitalActStatus(int $carriageId): object
    {
        $body = [
            'id' => $carriageId,
        ];

        return $this->postRequest("/v2/posting/fbs/digital/act/check-status", $body);
    }

    public function getActPdf(int $carriageId): object
    {
        $body = [
            'id' => $carriageId,
        ];

        return $this->postRequest("/v2/posting/fbs/act/get-pdf", $body);
    }

    public function getDigitalActPdf(int $carriageId, string $docType = 'act_of_acceptance'): object
    {
        $body = [
            'id' => $carriageId,
            'doc_type' => $docType,
        ];

        return $this->postRequest("/v2/posting/fbs/digital/act/get-pdf", $body);
    }

    public function setCutoffDate(string $postingNumber, string $newCutoffDate): object
    {
        $body = [
            'posting_number' => $postingNumber,
            'new_cutoff_date' => $newCutoffDate,
        ];

        return $this->postRequest("/v1/posting/cutoff/set", $body);
    }

    public function setTimeslot(string $postingNumber, array $newTimeslot): object
    {
        $body = [
            'posting_number' => $postingNumber,
            'new_timeslot' => $newTimeslot,
        ];

        return $this->postRequest("/v1/posting/fbs/timeslot/set", $body);
    }

    public function getTimeslotChangeRestrictions(string $postingNumber): object
    {
        $body = [
            'posting_number' => $postingNumber,
        ];

        return $this->postRequest("/v1/posting/fbs/timeslot/change-restrictions", $body);
    }

    public function setDeliveringStatus(array $postingNumbers): object
    {
        $body = [
            'posting_number' => $postingNumbers,
        ];

        return $this->postRequest("/v2/fbs/posting/delivering", $body);
    }

    public function setLastMileStatus(array $postingNumbers): object
    {
        $body = [
            'posting_number' => $postingNumbers,
        ];

        return $this->postRequest("/v2/fbs/posting/last-mile", $body);
    }

    public function setTrackingNumbers(array $trackingNumbers): object
    {
        $body = [
            'tracking_numbers' => $trackingNumbers,
        ];

        return $this->postRequest("/v2/fbs/posting/tracking-number/set", $body);
    }

    public function setDeliveredStatus(array $postingNumbers): object
    {
        $body = [
            'posting_number' => $postingNumbers,
        ];

        return $this->postRequest("/v2/fbs/posting/delivered", $body);
    }

    public function get(string $postingNumber, object $with = new stdClass())
    {
        $body = [
            'posting_number' => $postingNumber,
        ];

        if (!empty($with)) {
            $body['with'] = $with;
        }

        return $this->postRequest("/v3/posting/fbs/get", $body);
    }
}
