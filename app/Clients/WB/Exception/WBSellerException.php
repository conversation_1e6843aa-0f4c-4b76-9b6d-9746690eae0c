<?php

declare(strict_types=1);

namespace App\Clients\WB\Exception;

/**
 * Base exception class for WB Seller API errors
 *
 * This is the parent exception class for all WB Seller API specific exceptions.
 * It provides a common type that can be caught to handle any WB Seller API error.
 */
class WBSellerException extends \Exception
{
    /**
     * @param string $message The error message
     * @param int $code The error code
     * @param \Throwable|null $previous The previous throwable used for exception chaining
     */
    public function __construct(string $message = '', int $code = 0, ?\Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}
