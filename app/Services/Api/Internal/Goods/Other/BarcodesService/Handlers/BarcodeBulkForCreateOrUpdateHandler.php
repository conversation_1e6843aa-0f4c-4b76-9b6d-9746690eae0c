<?php

namespace App\Services\Api\Internal\Goods\Other\BarcodesService\Handlers;

use App\Contracts\Repositories\BarcodesRepositoryContract;
use App\Enums\Api\Internal\BarcodeEnum;
use App\Helpers\SetIdHandler;
use App\Services\Api\Internal\Goods\Other\BarcodesService\DTO\BarcodeDTO;
use App\Traits\HasBarcodes;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class BarcodeBulkForCreateOrUpdateHandler
{
    use HasOrderedUuid;
    use HasBarcodes;

    public function __construct(
        private readonly BarcodesRepositoryContract $repository
    ) {
    }

    public function run(BarcodeDTO $dto, $codes): Collection
    {

        $errors = [];

        $barcodeTypes = [
            BarcodeEnum::EAN8->value,
            BarcodeEnum::EAN13->value,
            BarcodeEnum::GTIN->value,
            BarcodeEnum::UPC->value,
        ];

        foreach ($dto->barcodes as $k => $barcode) {
            $value = $barcode['value'] ?? null;
            if (in_array((int)$barcode['type'], $barcodeTypes) &&  $value != null) {

                $response = $this->validateGtinOrUpc($value, $barcode['type'], $k);
                if (isset($response['errors'])) {
                    $errors["barcodes.$k.value"] = [$response['message']];
                }
            } elseif ($barcode['type'] == BarcodeEnum::CODE128->value) {
                $this->validateCODE128($value, BarcodeEnum::CODE128->value, $k);
            }
        }

        $barcodes = collect($dto->barcodes);

        $filteredBarcodes = $barcodes->map(function ($item) {

            $item['is_generated'] = false;

            return $item;
        })->filter(function ($item) {
            // Включаем штрихкоды с значениями ИЛИ с существующими ID (для обновления)
            return (isset($item['value']) && $item['value'] !== null) || isset($item['id']);
        });

        if (!empty($codes)) {
            $resultBarcodes = $this->repository->getAllBarcodesWhereInValue($codes, $dto->cabinetId);

            foreach ($codes as $code) {
                if (in_array($code, $resultBarcodes->toArray())) {
                    $errors[] = "{$code} not unique";
                }
            }
        }

        // Получаем все штрихкоды с null значениями для генерации новых кодов
        $barcodesWithNullValues = $barcodes->where('value', null);
        $codesNull = $barcodesWithNullValues->pluck('type')->toArray();

        $generatedCodes = [
            BarcodeEnum::EAN8->value => [],
            BarcodeEnum::EAN13->value => [],
            BarcodeEnum::GTIN->value => [],
            BarcodeEnum::UPC->value => [],
        ];

        $defaultStartValues = [
            BarcodeEnum::EAN8->value => 20000004,
            BarcodeEnum::EAN13->value => 2000000000008,
            BarcodeEnum::GTIN->value => 20000004,
            BarcodeEnum::UPC->value => 200000000004,
        ];

        // Обрабатываем каждый штрихкод с null значением
        foreach ($barcodesWithNullValues as $index => $barcodeWithNull) {
            $code = $barcodeWithNull['type'];

            if (!array_key_exists($code, $generatedCodes)) {
                continue;
            }

            $result = $this->repository->getMaxBarcode($code, $dto->cabinetId, strlen((string)$defaultStartValues[$code]));
            $result = $result ?? $defaultStartValues[$code];

            $generatedCodes[$code][] = $result;
            $maxBarcode = max($generatedCodes[$code]);

            do {
                $newGenerateBarcode = $this->codeGenerateEanOrGtin($maxBarcode, strlen((string)$defaultStartValues[$code]));

                $existsInDb = $this->repository->getExistsInDb($code, $dto->cabinetId, $newGenerateBarcode);

                if ($existsInDb) {
                    $maxBarcode = $newGenerateBarcode;
                }
            } while ($existsInDb || in_array($newGenerateBarcode, $generatedCodes[$code]));

            $generatedCodes[$code][] = $newGenerateBarcode;

            // Обновляем существующий штрихкод или добавляем новый
            if (isset($barcodeWithNull['id'])) {
                // Обновляем существующий штрихкод в коллекции
                $filteredBarcodes = $filteredBarcodes->map(function ($item) use ($barcodeWithNull, $newGenerateBarcode) {
                    if (isset($item['id']) && $item['id'] === $barcodeWithNull['id']) {
                        $item['value'] = $newGenerateBarcode;
                        $item['is_generated'] = true;
                    }
                    return $item;
                });
            } else {
                // Добавляем новый штрихкод
                $filteredBarcodes = $this->pushBarcodes($filteredBarcodes, $code, $newGenerateBarcode);
            }
        }

        if ($errors) {
            throw \Illuminate\Validation\ValidationException::withMessages($errors);
        }

        return SetIdHandler::setIdData(
            $filteredBarcodes,
            [
                'cabinet_id'        => $dto->cabinetId,
                'barcodable_id'     => $dto->barcodableId,
                'barcodable_type'   => $dto->barcodableType,
            ],
            'barcodes'
        );

    }

    public function pushBarcodes(Collection $filteredBarcodes, int $code, int $newGenerateBarcode): Collection
    {
        return $filteredBarcodes->push([
            'type' => $code,
            'value' => $newGenerateBarcode,
            'is_generated' => true,
        ]);
    }
}
