<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeGroupsService\Handlers;

use App\Contracts\Repositories\AttributeGroupsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class AttributeGroupGetHandler
{
    public function __construct(
        private AttributeGroupsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            id: $dto->id,
            filters: $dto->filters,
            fields: $dto->fields,
            sortField: $dto->sortField,
            sortDirection: $dto->sortDirection,
            page: $dto->page,
            perPage: $dto->perPage
        );
    }
}
