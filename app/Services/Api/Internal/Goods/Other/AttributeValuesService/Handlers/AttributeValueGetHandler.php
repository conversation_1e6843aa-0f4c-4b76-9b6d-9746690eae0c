<?php

namespace App\Services\Api\Internal\Goods\Other\AttributeValuesService\Handlers;

use App\Contracts\Repositories\AttributeValuesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class AttributeValueGetHandler
{
    public function __construct(
        private AttributeValuesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            id: $dto->id,
            page: $dto->page,
            perPage: $dto->perPage,
            sortField: $dto->sortField,
            sortDirection: $dto->sortDirection,
            fields: $dto->fields,
            filters: $dto->filters
        );
    }
}
