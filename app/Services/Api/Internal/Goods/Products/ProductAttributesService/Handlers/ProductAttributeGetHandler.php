<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAttributesService\Handlers;

use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductAttributeGetHandler
{
    public function __construct(
        private ProductAttributeRepositoryContract $productAttributeRepositoryContract,
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->productAttributeRepositoryContract->get($resourceId);
    }
}
