<?php

namespace App\Services\Api\Internal\Goods\Products\ProductAttributesService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ProductAttributeRepositoryContract;
use App\Traits\HasOrderedUuid;

class ProductAttributeCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly ProductAttributeRepositoryContract $productAttributeRepositoryContract,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->productAttributeRepositoryContract->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
