<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPricesService\Handlers;

use App\Contracts\Repositories\ProductPricesRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductPriceGetAllHandler
{
    public function __construct(
        private ProductPricesRepositoryContract $productPricesRepository
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->productPricesRepository->getAll($resourceId);
    }
}
