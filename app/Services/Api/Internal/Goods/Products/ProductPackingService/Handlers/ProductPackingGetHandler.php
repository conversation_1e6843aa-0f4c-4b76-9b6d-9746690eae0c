<?php

namespace App\Services\Api\Internal\Goods\Products\ProductPackingService\Handlers;

use App\Contracts\Repositories\ProductPackingRepositoryContract;
use Illuminate\Support\Collection;

readonly class ProductPackingGetHandler
{
    public function __construct(
        private ProductPackingRepositoryContract $productRepository
    ) {
    }

    public function run(string $resourceId): Collection
    {
        return $this->productRepository->get($resourceId);
    }
}
