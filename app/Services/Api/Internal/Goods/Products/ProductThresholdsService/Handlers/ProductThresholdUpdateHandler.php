<?php

namespace App\Services\Api\Internal\Goods\Products\ProductThresholdsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ProductThresholdsRepositoryContract;
use App\Exceptions\InvalidUuidException;
use App\Services\Api\Internal\Goods\Products\ProductsService\DTO\ProductDto;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ProductThresholdUpdateHandler
{
    use HasOrderedUuid;

    private HasUpdateArrayDtoContract $dto;
    private string $resourceId;

    public function __construct(
        private readonly ProductThresholdsRepositoryContract $productThresholdsRepository,
    ) {
    }

    /**
     * @throws InvalidUuidException
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof ProductDto) {
            $this->dto = $dto;
            $this->resourceId = $this->dto->resourceId;

            if (!$this->validateUuid($this->dto->resourceId)) {
                throw new InvalidUuidException();
            }

            $resource = $this->productThresholdsRepository->getFirst($this->resourceId);

            if (!$resource) {
                throw new ResourceNotFoundException();
            }

            $this->productThresholdsRepository->update(
                $this->dto->resourceId,
                $this->dto->toUpdateArray(),
            );

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');
    }
}
