<?php

namespace App\Services\Api\Internal\GoodsTransferItemService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\GoodsTransferItemsRepositoryContract;
use App\Jobs\FIFOJobs\HandleGoodsTransferFifoJob;
use App\Services\Api\Internal\GoodsTransferItemService\DTO\GoodsTransferItemDto;
use Illuminate\Support\Facades\Queue;
use Symfony\Component\Process\Exception\InvalidArgumentException;

readonly class GoodsTransferItemsUpdateHandler
{
    public function __construct(
        private GoodsTransferItemsRepositoryContract $itemRepository,
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof GoodsTransferItemDto) {
            throw new InvalidArgumentException('Unsupported DTO type');
        }
        $this->itemRepository->update(
            $dto->resourceId,
            $dto->toUpdateArray(),
        );

        // Добавляем вызов FIFO-обработчика
        Queue::push(new HandleGoodsTransferFifoJob($dto->resourceId));
    }
}
