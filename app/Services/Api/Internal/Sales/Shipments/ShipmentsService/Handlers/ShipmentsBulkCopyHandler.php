<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

readonly class ShipmentsBulkCopyHandler
{
    use HasOrderedUuid;

    public function __construct(
        private ShipmentsRepositoryContract $shipmentsRepository,
        private ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private DocumentsRepositoryContract $documentsRepository,
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(array $resourceIds): array
    {
        $sourceShipments = DB::table('shipments')
            ->whereIn('id', $resourceIds)
            ->get();

        if ($sourceShipments->isEmpty()) {
            return [];
        }

        $time = Carbon::now();
        $newShipmentIds = [];
        $documentsData = [];
        $shipmentsData = [];
        $shipmentItemsData = [];

        foreach ($sourceShipments as $sourceShipment) {
            $newShipmentId = $this->generateUuid();
            $newShipmentIds[] = $newShipmentId;

            $documentNumberGenerator = new DocumentNumberGenerator(
                'shipments',
                $sourceShipment->cabinet_id,
                $sourceShipment->number,
                $sourceShipment->legal_entity_id
            );

            $shipmentsData[] = array_merge(
                (array) $sourceShipments->where('id', $sourceShipment->id)->first(),
                [
                    'id' => $newShipmentId,
                    'total_cost' => 0,
                    'profit' => 0,
                    'held' => false,
                    'created_at' => $time,
                    'updated_at' => $time,
                    'number' => $documentNumberGenerator->generateNumber(),
                ]
            );

            $documentsData[] = [
                'documentable_id' => $newShipmentId,
                'documentable_type' => 'shipments',
                'lft' => 1,
                'rgt' => 2,
                'parent_id' => null,
                'tree_id' => $this->generateUuid(),
                'cabinet_id' => $sourceShipment->cabinet_id,
                'created_at' => $time,
                'updated_at' => $time,
            ];

            $sourceItems = DB::table('shipment_items')
                ->where('shipment_id', $sourceShipment->id)
                ->get();

            foreach ($sourceItems as $sourceItem) {
                $shipmentItemsData[] = [
                    'id' => $this->generateUuid(),
                    'shipment_id' => $newShipmentId,
                    'product_id' => $sourceItem->product_id,
                    'vat_rate_id' => $sourceItem->vat_rate_id,
                    'quantity' => $sourceItem->quantity,
                    'price' => $sourceItem->price,
                    'total_price' => $sourceItem->total_price,
                    'total_cost' => 0,
                    'cost' => 0,
                    'profit' => 0,
    
                    'created_at' => $time,
                    'updated_at' => $time,
                ];
            }
        }

        if (!empty($shipmentsData)) {
            DB::table('shipments')->insert($shipmentsData);
            DB::table('documents')->insert($documentsData);
            DB::table('shipment_items')->insert($shipmentItemsData);
        }

        return $newShipmentIds;
    }
}
