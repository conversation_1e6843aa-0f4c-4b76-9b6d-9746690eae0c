<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentsService\Handlers;

use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;

class ShipmentsShowHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public function __construct(
        private readonly ShipmentsRepositoryContract $shipmentsRepository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        $data = $this->shipmentsRepository->show($resourceId);

        $data->delivery_info = json_decode($data->delivery_info, true);
        $data->status = json_decode($data->status, true);
        $data->contractor = json_decode($data->contractor, true);
        $data->warehouse = json_decode($data->warehouse, true);
        $data->files = json_decode($data->files, true);
        if ($data->files) {
            $this->generateUrls($data->files);
        }

        return $data;
    }
}
