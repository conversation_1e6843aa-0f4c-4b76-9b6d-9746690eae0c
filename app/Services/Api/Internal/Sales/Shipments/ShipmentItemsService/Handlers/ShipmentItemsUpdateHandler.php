<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateShipmentItemJob;
use App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO\ShipmentItemDTO;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use http\Exception\InvalidArgumentException;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Queue;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ShipmentItemsUpdateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;

    public function __construct(
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private readonly ShipmentsRepositoryContract $shipmentsRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof ShipmentItemDTO) {
            throw new InvalidArgumentException();
        }
        $item = $this->shipmentItemsRepository->show($dto->resourceId);

        if (!$item) {
            throw new ResourceNotFoundException();
        }

        $this->shipmentItemsRepository->update(
            $dto->resourceId,
            $dto->toUpdateArray(),
        );

        if ($this->compare($item->total_price, $dto->totalPrice) != 0) {
            $shipment = $this->shipmentsRepository->show($item->shipment_id);
            $priceDifference = $this->subtract($item->total_price, $dto->totalPrice);
            $newTotalPrice = $this->subtract($shipment->total_price, $priceDifference);
            $this->shipmentsRepository
                ->update(
                    $item->shipment_id,
                    ['total_price' => $newTotalPrice],
                );
        }

        Queue::push(new RecalculationAfterUpdateShipmentItemJob($item, $dto));
    }
}
