<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;

class ShipmentItemShowHandler
{
    public function __construct(
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->shipmentItemsRepository->show($resourceId);
    }
}
