<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\Handlers;

use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Jobs\FIFOJobs\HandleFifoJob;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

class ShipmentItemDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly ShipmentItemsRepositoryContract $shipmentItemsRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(string $resourceId): void
    {
        // Если нету такой позиции, ничего не делаем
        $item = $this->shipmentItemsRepository->show($resourceId);
        if (!$item) {
            throw new ResourceNotFoundException('Resource not found');
        }
        DB::table('shipments')
            ->where('id', $item->shipment_id)
            ->update([
                'total_price' => DB::raw("CAST(COALESCE(total_price, '0') AS NUMERIC) - " . $item->price)
            ]);

        Queue::push(new HandleFifoJob($resourceId, true));
    }
}
