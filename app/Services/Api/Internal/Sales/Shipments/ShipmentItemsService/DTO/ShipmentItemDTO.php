<?php

namespace App\Services\Api\Internal\Sales\Shipments\ShipmentItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;
use App\Traits\SummaryPriceCalculator;

class ShipmentItemDTO implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;
    use SummaryPriceCalculator;

    public string $totalPrice;
    public string $cost;
    public string $totalCost;
    public string $profit;
    public string $totalVatSum;

    public function __construct(
        public ?string $cabinetId,
        public ?string $shipmentId,
        public ?string $productId,
        public ?string $vat_rate_id,
        public string $price = '0',
        public string $discount = '0',
        public string $quantity = '1',
        public ?string $resourceId = null,
        public string $cost = '0',
        public string $totalCost = '0',
        public string $profit = '0'
    ) {
        $this->calculateTotals();
    }

    /**
     * Рассчитывает все итоговые суммы
     */
    private function calculateTotals(): void
    {
        // Рассчитываем общую стоимость позиции
        $this->totalPrice = $this->calculateSumPrice([
            'price' => $this->price,
            'quantity' => $this->quantity,
            'discount' => $this->discount,
        ]);

        // Рассчитываем общую себестоимость
        $this->totalCost = $this->multiply($this->cost, $this->quantity);

        // Рассчитываем прибыль
        $this->profit = $this->subtract($this->totalPrice, $this->totalCost);

        // Рассчитываем НДС (пока простой расчет, можно усложнить позже)
        $this->totalVatSum = '0'; // TODO: добавить расчет НДС когда будет нужно
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'shipment_id' => $this->shipmentId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'cost' => $this->cost,
            'total_cost' => $this->totalCost,
            'total_price' => $this->totalPrice,
            'profit' => $this->profit,
            'total_vat_sum' => $this->totalVatSum
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'profit' => $this->profit,
            'cost' => $this->cost,
            'total_cost' => $this->totalCost,
            'total_price' => $this->totalPrice,
            'total_vat_sum' => $this->totalVatSum
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            shipmentId: $data['shipment_id'] ?? null,
            productId: $data['product_id'] ?? null,
            vat_rate_id: $data['vat_rate_id'] ?? null,
            price: (string)($data['price'] ?? '0'),
            discount: (string)($data['discount'] ?? '0'),
            quantity: (string)($data['quantity'] ?? '1'),
            resourceId: $data['id'] ?? null,
            cost: (string)($data['cost'] ?? '0'),
            totalCost: (string)($data['total_cost'] ?? '0'),
            profit: (string)($data['profit'] ?? '0')
        );
    }
}
