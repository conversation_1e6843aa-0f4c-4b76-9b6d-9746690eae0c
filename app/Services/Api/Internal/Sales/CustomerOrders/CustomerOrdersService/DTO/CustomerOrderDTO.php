<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrdersService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasDeliveryArrayContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;

class CustomerOrderDTO implements DtoContract, HasInsertArrayDtoContract, HasDeliveryArrayContract, HasUpdateArrayDtoContract
{
    use HasOrderedUuid;

    public function __construct(
        public string $cabinetId,
        public string $legal_entity_id,
        public int $userId,
        public string $contractor_id,
        public string $employeeId,
        public string $departmentId,
        public ?string $number = null,
        public ?string $date_from = null,
        public ?string $status_id = null,
        public ?bool $held = false,
        public ?bool $reserve = false,
        public ?string $plan_date = null,
        public ?string $sales_channel_id = null,
        public ?string $warehouse_id = null,
        public array $files = [],
        public ?array $deliveryInfo = null,
        public ?string $resourceId = null,
        public ?string $comment = null,
        public bool $hasVat = true,
        public bool $priceIncludesVat = true,
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinetId,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'number' => $this->number,
            'date_from' => $this->date_from,
            'status_id' => $this->status_id,
            'held' => $this->held,
            'reserve' => $this->reserve,
            'legal_entity_id' => $this->legal_entity_id,
            'contractor_id' => $this->contractor_id,
            'plan_date' => $this->plan_date,
            'sales_channel_id' => $this->sales_channel_id,
            'warehouse_id' => $this->warehouse_id,
            'comment' => $this->comment,
            'has_vat' => $this->hasVat,
            'price_includes_vat' => $this->priceIncludesVat,
        ];
    }

    public function toDeliveryArray(string $id): array
    {
        return [
            'id' => $this->generateUuid(),
            'order_id' => $id,
            'comment' => $this->deliveryInfo['comment'] ?? null,
            'post_code' => $this->deliveryInfo['post_code'] ?? null,
            'country' => $this->deliveryInfo['country'] ?? null,
            'region' => $this->deliveryInfo['region'] ?? null,
            'city' => $this->deliveryInfo['city'] ?? null,
            'street' => $this->deliveryInfo['street'] ?? null,
            'house' => $this->deliveryInfo['house'] ?? null,
            'office' => $this->deliveryInfo['office'] ?? null,
            'other' => $this->deliveryInfo['other'] ?? null
        ];
    }

    public function toUpdateArray(): array
    {
        return [
            'date_from' => $this->date_from,
            'status_id' => $this->status_id,
            'held' => $this->held,
            'reserve' => $this->reserve,
            'legal_entity_id' => $this->legal_entity_id,
            'contractor_id' => $this->contractor_id,
            'plan_date' => $this->plan_date,
            'sales_channel_id' => $this->sales_channel_id,
            'warehouse_id' => $this->warehouse_id,
            'employee_id' => $this->employeeId,
            'department_id' => $this->departmentId,
            'comment' => $this->comment,
            'has_vat' => $this->hasVat,
            'price_includes_vat' => $this->priceIncludesVat,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'] ?? null,
            legal_entity_id: $data['legal_entity_id'],
            userId: auth()->user()->id,
            contractor_id: $data['contractor_id'],
            employeeId: $data['employee_id'],
            departmentId: $data['department_id'],
            number: $data['number'] ?? null,
            date_from: isset($data['date_from']) ? Carbon::parse($data['date_from']) : null,
            status_id: $data['status_id'] ?? null,
            held: $data['held'] ?? true,
            reserve: $data['reserve'] ?? false,
            plan_date: isset($data['plan_date']) ? Carbon::parse($data['plan_date']) : null,
            sales_channel_id: $data['sales_channel_id'] ?? null,
            warehouse_id: $data['warehouse_id'] ?? null,
            files: $data['files'] ?? [],
            deliveryInfo: $data['delivery_info'] ?? [],
            resourceId: $data['id'] ?? null,
            comment: $data['comment'] ?? null,
            hasVat: $data['has_vat'] ?? true,
            priceIncludesVat: $data['price_includes_vat'] ?? true,
        );
    }
}
