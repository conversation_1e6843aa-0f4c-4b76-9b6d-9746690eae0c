<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\Traits;

use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Traits\PrecisionCalculator;
use Illuminate\Support\Facades\DB;

/**
 * Трейт для расчета общей суммы заказа покупателя с учетом НДС
 * Использует BcMath для точных расчетов с varchar полями
 */
trait CustomerOrderTotalCalculator
{
    use PrecisionCalculator;

    /**
     * Пересчитывает общую сумму заказа покупателя на основе позиций с учетом НДС
     *
     * @param string $orderId ID заказа
     * @return string Общая сумма заказа
     */
    public function recalculateCustomerOrderTotal(string $orderId): string
    {
        $order = DB::table('customer_orders')->where('id', $orderId)->first();
        
        if (!$order) {
            return '0';
        }

        $items = DB::table('customer_order_items as coi')
            ->leftJoin('vat_rates as vr', 'coi.vat_rate_id', '=', 'vr.id')
            ->where('coi.order_id', $orderId)
            ->select(['coi.price_in_currency', 'coi.quantity', 'coi.discount', 'vr.rate as vat_rate'])
            ->get();

        $totalSum = '0';

        foreach ($items as $item) {
            $itemTotal = $this->calculateCustomerOrderItemTotal(
                (string)($item->price_in_currency ?? '0'),
                (string)($item->quantity ?? '1'),
                (string)($item->discount ?? '0'),
                (string)($item->vat_rate ?? '0'),
                $order->price_includes_vat,
                $order->has_vat
            );

            $totalSum = $this->add($totalSum, $itemTotal);
        }

        return $totalSum;
    }

    /**
     * Рассчитывает итоговую сумму позиции заказа покупателя с учетом НДС
     *
     * @param string $priceInCurrency Цена в валюте
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string $vatRate Ставка НДС
     * @param bool $priceIncludesVat Цена включает НДС
     * @param bool $hasVat Документ с НДС
     * @return string Итоговая сумма позиции
     */
    public function calculateCustomerOrderItemTotal(
        string $priceInCurrency,
        string $quantity,
        string $discount,
        string $vatRate,
        bool $priceIncludesVat,
        bool $hasVat
    ): string {
        // Рассчитываем сумму без скидки
        $subtotal = $this->multiply($priceInCurrency, $quantity);

        // Применяем скидку
        if ($this->compare($discount, '0') > 0) {
            $discountPercent = $this->divide($discount, '100');
            $discountAmount = $this->multiply($subtotal, $discountPercent);
            $subtotal = $this->subtract($subtotal, $discountAmount);
        }

        // Если документ без НДС, возвращаем сумму без НДС
        if (!$hasVat) {
            return $subtotal;
        }

        // Если ставка НДС равна 0, возвращаем сумму без НДС
        if ($this->compare($vatRate, '0') === 0) {
            return $subtotal;
        }

        $vatDecimal = $this->divide($vatRate, '100');

        if ($priceIncludesVat) {
            // Цена включает НДС - возвращаем сумму как есть
            return $subtotal;
        } else {
            // Цена без НДС - добавляем НДС к цене
            $vatAmount = $this->multiply($subtotal, $vatDecimal);
            return $this->add($subtotal, $vatAmount);
        }
    }

    /**
     * Рассчитывает итоговую сумму позиции с учетом скидки (старый метод для совместимости)
     *
     * @param string $amountInBase Сумма в базовой валюте
     * @param string $discount Скидка в процентах
     * @return string Итоговая сумма позиции
     */
    public function calculateItemTotal(string $amountInBase, string $discount): string
    {
        if ($this->compare($discount, '0') === 0) {
            return $amountInBase;
        }

        $discountPercent = $this->divide($discount, '100');
        $discountAmount = $this->multiply($amountInBase, $discountPercent);

        return $this->subtract($amountInBase, $discountAmount);
    }

    /**
     * Обновляет общую сумму заказа в базе данных
     *
     * @param string $orderId ID заказа
     * @return void
     */
    public function updateCustomerOrderTotal(string $orderId): void
    {
        $totalPrice = $this->recalculateCustomerOrderTotal($orderId);

        DB::table('customer_orders')
            ->where('id', $orderId)
            ->update([
                'total_price' => $totalPrice,
                'updated_at' => now()
            ]);
    }

    /**
     * Получает информацию о НДС для заказа покупателя
     *
     * @param string $orderId ID заказа
     * @param string|null $vatRateId ID ставки НДС
     * @return array
     */
    public function getVatInfo(string $orderId, ?string $vatRateId = null): array
    {
        $order = DB::table('customer_orders')->where('id', $orderId)->first();
        
        if (!$order) {
            return [
                'has_vat' => true,
                'price_includes_vat' => true,
                'vat_rate' => '0'
            ];
        }

        $vatRate = '0';
        if ($vatRateId) {
            $vatRateRecord = DB::table('vat_rates')->where('id', $vatRateId)->first();
            if ($vatRateRecord) {
                $vatRate = (string)$vatRateRecord->rate;
            }
        }

        return [
            'has_vat' => (bool)$order->has_vat,
            'price_includes_vat' => (bool)$order->price_includes_vat,
            'vat_rate' => $vatRate
        ];
    }
}
