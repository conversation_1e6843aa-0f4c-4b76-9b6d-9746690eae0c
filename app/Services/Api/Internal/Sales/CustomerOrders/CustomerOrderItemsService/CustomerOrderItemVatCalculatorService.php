<?php

namespace App\Services\Api\Internal\Sales\CustomerOrders\CustomerOrderItemsService;

use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Traits\PrecisionCalculator;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для расчета НДС в позициях заказа покупателя
 */
class CustomerOrderItemVatCalculatorService
{
    use PrecisionCalculator;

    public function __construct(
        private readonly VatRatesRepositoryContract $vatRatesRepository
    ) {}

    /**
     * Рассчитывает детали НДС для позиции заказа покупателя
     *
     * @param string $orderId ID заказа покупателя
     * @param string $priceInCurrency Цена в валюте
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string|null $vatRateId ID ставки НДС
     * @param string $cabinetId ID кабинета
     * @return array
     */
    public function calculateItemVatDetails(
        string $orderId,
        string $priceInCurrency,
        string $quantity,
        string $discount,
        ?string $vatRateId,
        string $cabinetId
    ): array {
        // Получаем настройки НДС заказа
        $order = DB::table('customer_orders')->where('id', $orderId)->first();
        if (!$order) {
            throw new \InvalidArgumentException('Customer order not found');
        }

        // Рассчитываем сумму без скидки
        $subtotal = $this->multiply($priceInCurrency, $quantity);

        // Применяем скидку
        if ($this->compare($discount, '0') > 0) {
            $discountPercent = $this->divide($discount, '100');
            $discountAmount = $this->multiply($subtotal, $discountPercent);
            $subtotal = $this->subtract($subtotal, $discountAmount);
        }

        // Если заказ без НДС
        if (!$order->has_vat) {
            return [
                'subtotal' => $subtotal,
                'vat_amount' => '0',
                'total' => $subtotal,
                'vat_rate' => '0',
                'has_vat' => false,
                'price_includes_vat' => $order->price_includes_vat
            ];
        }

        // Получаем ставку НДС
        $vatRate = '0';
        if ($vatRateId) {
            $vatRateRecord = $this->vatRatesRepository->show($vatRateId);
            if ($vatRateRecord) {
                $vatRate = (string)$vatRateRecord->rate;
            }
        }

        // Если ставка НДС равна 0
        if ($this->compare($vatRate, '0') === 0) {
            return [
                'subtotal' => $subtotal,
                'vat_amount' => '0',
                'total' => $subtotal,
                'vat_rate' => $vatRate,
                'has_vat' => true,
                'price_includes_vat' => $order->price_includes_vat
            ];
        }

        $vatDecimal = $this->divide($vatRate, '100');

        if ($order->price_includes_vat) {
            // Цена включает НДС - извлекаем НДС из цены
            $vatDivisor = $this->add('1', $vatDecimal);
            $priceWithoutVat = $this->divide($subtotal, $vatDivisor);
            $vatAmount = $this->subtract($subtotal, $priceWithoutVat);
            
            return [
                'subtotal' => $priceWithoutVat,
                'vat_amount' => $vatAmount,
                'total' => $subtotal,
                'vat_rate' => $vatRate,
                'has_vat' => true,
                'price_includes_vat' => true
            ];
        } else {
            // Цена без НДС - добавляем НДС к цене
            $vatAmount = $this->multiply($subtotal, $vatDecimal);
            $total = $this->add($subtotal, $vatAmount);
            
            return [
                'subtotal' => $subtotal,
                'vat_amount' => $vatAmount,
                'total' => $total,
                'vat_rate' => $vatRate,
                'has_vat' => true,
                'price_includes_vat' => false
            ];
        }
    }

    /**
     * Рассчитывает итоговую сумму позиции заказа покупателя
     *
     * @param string $orderId ID заказа покупателя
     * @param string $priceInCurrency Цена в валюте
     * @param string $quantity Количество
     * @param string $discount Скидка в процентах
     * @param string|null $vatRateId ID ставки НДС
     * @param string $cabinetId ID кабинета
     * @return string
     */
    public function calculateItemTotal(
        string $orderId,
        string $priceInCurrency,
        string $quantity,
        string $discount,
        ?string $vatRateId,
        string $cabinetId
    ): string {
        $vatDetails = $this->calculateItemVatDetails(
            $orderId,
            $priceInCurrency,
            $quantity,
            $discount,
            $vatRateId,
            $cabinetId
        );

        return $vatDetails['total'];
    }

    /**
     * Получает ID ставки НДС "Без НДС" для кабинета
     *
     * @param string $cabinetId ID кабинета
     * @return string|null
     */
    public function getAutoVatRateId(string $cabinetId): ?string
    {
        $vatRate = DB::table('vat_rates')
            ->where('cabinet_id', $cabinetId)
            ->where('rate', 0)
            ->first();

        return $vatRate?->id;
    }
} 