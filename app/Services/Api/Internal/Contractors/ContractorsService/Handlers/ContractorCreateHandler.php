<?php

namespace App\Services\Api\Internal\Contractors\ContractorsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\ContractorAccountsRepositoryContract;
use App\Contracts\Repositories\ContractorAdressesRepositoryContract;
use App\Contracts\Repositories\ContractorContactsRepositoryContract;
use App\Contracts\Repositories\ContractorContractorGroupRepositoryContract;
use App\Contracts\Repositories\ContractorDetailAddressRepositoryContract;
use App\Contracts\Repositories\ContractorDetailsRepositoryContract;
use App\Contracts\Repositories\ContractorsRepositoryContract;
use App\Services\Api\Internal\Contractors\ContractorsService\DTO\ContractorDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

readonly class ContractorCreateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public string $resourceId;

    public function __construct(
        private ContractorsRepositoryContract $repository,
        private ContractorAdressesRepositoryContract $contractorAdressesRepository,
        private ContractorDetailsRepositoryContract $contractorDetailsRepository,
        private ContractorContractorGroupRepositoryContract $contractorContractorGroupRepository,
        private ContractorAccountsRepositoryContract $contractorAccountsRepository,
        private ContractorContactsRepositoryContract $contractorContactsRepository,
        private ContractorDetailAddressRepositoryContract $contractorDetailAddressRepository,
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof ContractorDTO) {
            throw new InvalidArgumentException();
        }

        $this->repository->insert($dto->toInsertArray($this->resourceId));
        $this->contractorAdressesRepository->insert($dto->toInsertAdressArray($this->resourceId));

        $detailId = $this->generateUuid();
        $this->contractorDetailsRepository->insert($dto->toInsertDetailArray($detailId, $this->resourceId));
        $this->contractorDetailAddressRepository->insert($dto->toInsertDetailAddressArray($detailId));

        if ($dto->accounts) {
            $accounts = collect($dto->accounts);

            $accounts = $accounts->map(function ($item) {

                $item['id'] = $this->generateUuid();

                $item['contractor_id'] = $this->resourceId;
                $item['created_at'] = now();
                $item['updated_at'] = now();

                return $item;
            });

            $this->contractorAccountsRepository->insert($accounts->toArray());

        }

        if ($dto->contacts) {
            $contacts = collect($dto->contacts);

            $contacts = $contacts->map(function ($item) {

                $item['id'] = $this->generateUuid();

                $item['contractor_id'] = $this->resourceId;
                $item['created_at'] = now();
                $item['updated_at'] = now();

                return $item;
            });

            $this->contractorContactsRepository->insert($contacts->toArray());
        }

        if ($dto->contractorGroups) {
            $contractorGroups = collect($dto->contractorGroups);

            $contractorGroups = $contractorGroups->map(function ($item) {

                $item['id'] = $this->generateUuid();

                $item['contractor_id'] = $this->resourceId;

                return $item;
            });

            $this->contractorContractorGroupRepository->insert($contractorGroups->toArray());

        }

        $this->setRelationToFiles($this->resourceId, $dto->files, 'contractors');

        return $this->resourceId;
    }
}
