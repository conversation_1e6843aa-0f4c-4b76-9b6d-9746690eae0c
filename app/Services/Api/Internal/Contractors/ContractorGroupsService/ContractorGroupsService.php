<?php

namespace App\Services\Api\Internal\Contractors\ContractorGroupsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Contractors\ContractorGroupsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers\ContractorGroupsBulkDeleteHandler;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers\ContractorGroupsCreateHandler;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers\ContractorGroupsDeleteHandler;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers\ContractorGroupsGetHandler;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers\ContractorGroupsShowHandler;
use App\Services\Api\Internal\Contractors\ContractorGroupsService\Handlers\ContractorGroupsUpdateHandler;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

readonly class ContractorGroupsService implements ContractorGroupsServiceContract
{
    public function __construct(
        private ContractorGroupsCreateHandler $createHandler,
        private ContractorGroupsGetHandler $getHandler,
        private ContractorGroupsDeleteHandler $deleteHandler,
        private ContractorGroupsShowHandler $showHandler,
        private ContractorGroupsUpdateHandler $updateHandler,
        private ContractorGroupsBulkDeleteHandler $bulkDeleteHandler
    ) {
    }
    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    /**
     * @throws BindingResolutionException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }
}
