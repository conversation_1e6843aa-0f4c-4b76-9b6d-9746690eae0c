<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Traits;

use Illuminate\Support\Facades\DB;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

trait CheckSystemClone
{
    public function checkSystemClone(string $groupId, string $cabinetId): void
    {
        //Проверка, что каким-то образом нам не прилетел системный айдишник при имеющемся системном клоне
        $clone = DB::table('cabinet_measurement_system_groups')
            ->where('system_group_id', $groupId)
            ->where('cabinet_id', $cabinetId)
            ->exists();

        //Если клон есть, то выбрасываем 404, т.к. юзер уже ничего не должен знать о системных записях
        if ($clone) {
            throw new ResourceNotFoundException('Please, do not use system groups');
        }
    }
}
