<?php

namespace App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Directories\MeasurementUnitGroupServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers\MeasurementUnitGroupCloneSystemHandler;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers\MeasurementUnitGroupCreateHandler;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers\MeasurementUnitGroupDeleteHandler;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers\MeasurementUnitGroupGetHandler;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers\MeasurementUnitGroupShowHandler;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\Handlers\MeasurementUnitGroupUpdateHandler;
use Illuminate\Support\Collection;

readonly class MeasurementUnitGroupService implements MeasurementUnitGroupServiceContract
{
    public function __construct(
        private MeasurementUnitGroupCreateHandler $createHandler,
        private MeasurementUnitGroupUpdateHandler $updateHandler,
        private MeasurementUnitGroupDeleteHandler $deleteHandler,
        private MeasurementUnitGroupShowHandler $showHandler,
        private MeasurementUnitGroupGetHandler $getHandler,
        private MeasurementUnitGroupCloneSystemHandler $cloneSystemHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);

    }
    public function cloneSystemGroup(object $group, string $cabinetId, string $resourceId = null): array
    {
        return $this->cloneSystemHandler->run($group, $cabinetId, $resourceId);
    }
}
