<?php

namespace App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;

readonly class CabinetCurrencyShowHandler
{
    public function __construct(
        private CabinetCurrenciesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
