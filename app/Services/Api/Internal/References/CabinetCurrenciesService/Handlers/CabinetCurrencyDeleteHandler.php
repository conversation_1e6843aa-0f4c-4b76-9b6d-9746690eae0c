<?php

namespace App\Services\Api\Internal\References\CabinetCurrenciesService\Handlers;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Traits\HasOrderedUuid;

class CabinetCurrencyDeleteHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly CabinetCurrenciesRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
