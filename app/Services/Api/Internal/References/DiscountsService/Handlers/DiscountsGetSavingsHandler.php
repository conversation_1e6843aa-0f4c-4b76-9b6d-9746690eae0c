<?php

namespace App\Services\Api\Internal\References\DiscountsService\Handlers;

use App\Contracts\Repositories\DiscountsRepositoryContract;
use App\Traits\HasOrderedUuid;

class DiscountsGetSavingsHandler
{
    use HasOrderedUuid;
    public function __construct(
        private readonly DiscountsRepositoryContract $repository
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return$this->repository->getAllSavingsForDiscountSavingsId($resourceId);
    }
}
