<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;

class AcceptanceItemDto implements DtoContract, HasInsertArrayDtoContract, HasUpdateArrayDtoContract
{
    use AcceptanceTotalCalculator;

    public string $totalPrice;
    public string $totalVatSum;

    public function __construct(
        public string $quantity,
        public ?string $acceptanceId = null,
        public ?string $productId = null,
        public string $price = '0',
        public string $discount = '0',
        public ?string $vat_rate_id = null,
        public ?string $countryId = null,
        public ?string $gtdNumber = null,
        public ?string $resourceId = null
    ) {
        $this->calculateTotals();
    }

    /**
     * Рассчитывает total_price и total_vat_sum с учетом настроек НДС в приемке
     */
    private function calculateTotals(): void
    {
        if (!$this->acceptanceId) {
            // Если нет ID приемки, используем простой расчет
            $this->totalPrice = $this->multiply($this->price, $this->quantity);
            $this->totalVatSum = '0';
            return;
        }

        $vatInfo = $this->getVatInfo($this->acceptanceId, $this->vat_rate_id);

        // Базовая сумма = цена × количество
        $baseAmount = $this->multiply($this->price, $this->quantity);

        // Применяем скидку
        $amountAfterDiscount = $this->applyDiscount($baseAmount, $this->discount);

        // Рассчитываем НДС в зависимости от настроек
        if (!$vatInfo['has_vat'] || $this->compare($vatInfo['vat_rate'], '0') == 0) {
            // Без НДС
            $this->totalPrice = $amountAfterDiscount;
            $this->totalVatSum = '0';
        } elseif ($vatInfo['price_includes_vat']) {
            // НДС включен в цену
            $vatCalculation = $this->extractVat($amountAfterDiscount, $vatInfo['vat_rate']);
            $this->totalPrice = $amountAfterDiscount;
            $this->totalVatSum = $vatCalculation['vat_amount'];
        } else {
            // НДС сверх цены
            $vatCalculation = $this->calculateVat($amountAfterDiscount, $vatInfo['vat_rate']);
            $this->totalPrice = $vatCalculation['total_amount'];
            $this->totalVatSum = $vatCalculation['vat_amount'];
        }
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'acceptance_id' => $this->acceptanceId,
            'product_id' => $this->productId,
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'total_price' => $this->totalPrice,
            'country_id' => $this->countryId,
            'gtd_number' => $this->gtdNumber,
            ];
    }

    public function toUpdateArray(): array
    {
        return [
            'quantity' => $this->quantity,
            'price' => $this->price,
            'discount' => $this->discount,
            'vat_rate_id' => $this->vat_rate_id,
            'total_price' => $this->totalPrice,
            'country_id' => $this->countryId,
            'gtd_number' => $this->gtdNumber,
        ];
    }

    public static function fromArray(array $data): self
    {
        $instance = new self(
            quantity: $data['quantity'],
            acceptanceId: $data['acceptance_id'] ?? null,
            productId: $data['product_id'] ?? null,
            price: $data['price'] ?? 0,
            discount: $data['discount'] ?? 0,
            vat_rate_id: $data['vat_rate_id'] ?? null,
            countryId: $data['country_id'] ?? null,
            gtdNumber: $data['gtd_number'] ?? null,
            resourceId: $data['id'] ?? null,
        );

        // Пересчитываем totalPrice после создания экземпляра
        $instance->totalPrice = $instance->calculateTotalPrice();

        return $instance;
    }
}
