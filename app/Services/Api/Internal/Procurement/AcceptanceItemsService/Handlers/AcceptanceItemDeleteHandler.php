<?php

namespace App\Services\Api\Internal\Procurement\AcceptanceItemsService\Handlers;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;

use App\Jobs\FIFOJobs\RecalculationAfterDeleteAcceptanceItemJob;
use App\Services\Api\Internal\Procurement\Acceptances\Traits\AcceptanceTotalCalculator;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;

class AcceptanceItemDeleteHandler
{
    use HasOrderedUuid;
    use AcceptanceTotalCalculator;

    public function __construct(
        private readonly AcceptanceItemsRepositoryContract $acceptanceItemsRepository,
        private readonly AcceptanceRepositoryContract $acceptanceRepository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(string $resourceId): void
    {
        $item = $this->acceptanceItemsRepository->show($resourceId);

        // Удаляем позицию
        $this->acceptanceItemsRepository->delete($resourceId);

        // Пересчитываем общую сумму приемки с учетом НДС
        $this->updateAcceptanceTotal($item->acceptance_id);

        RecalculationAfterDeleteAcceptanceItemJob::dispatch($resourceId);
    }
}
