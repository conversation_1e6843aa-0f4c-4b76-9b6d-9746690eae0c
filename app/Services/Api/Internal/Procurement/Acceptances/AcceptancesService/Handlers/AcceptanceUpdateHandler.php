<?php

namespace App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\AcceptanceRepositoryContract;
use App\Jobs\FIFOJobs\RecalculationAfterUpdateAcceptanceJob;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\DTO\AcceptanceDto;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\Queue;
use InvalidArgumentException;
use RuntimeException;

class AcceptanceUpdateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    private HasUpdateArrayDtoContract $dto;
    private string $resourceId;

    public function __construct(
        private readonly AcceptanceRepositoryContract $acceptanceRepository,
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof AcceptanceDto) {
            $this->dto = $dto;
            $this->resourceId = $this->dto->resourceId;

            $resource = $this->acceptanceRepository->getFirst($this->resourceId);

            if (!$resource) {
                throw new RuntimeException('Acceptance not found in service');
            }

            $dto->cabinetId = $resource->cabinet_id;
            $update = $this->dto->toUpdateArray();

            if ($resource->legal_entity_id !== $this->dto->legalEntityId) {
                $documentNumberGenerator = new DocumentNumberGenerator(
                    'acceptances',
                    $dto->cabinetId,
                    $dto->number,
                    $dto->legalEntityId
                );
                $update['number'] = $documentNumberGenerator->generateNumber();
            }

            $this->acceptanceRepository->update(
                $this->dto->resourceId,
                $update,
            );

            $this->setRelationToFiles($this->resourceId, $dto->files, 'acceptances', true);

            Queue::push(new RecalculationAfterUpdateAcceptanceJob($resource, $this->dto));

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');
    }
}
