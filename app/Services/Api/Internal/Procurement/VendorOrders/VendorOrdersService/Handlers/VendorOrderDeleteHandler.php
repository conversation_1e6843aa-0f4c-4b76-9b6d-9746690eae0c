<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers;

use App\Contracts\Repositories\VendorOrdersRepositoryContract;

readonly class VendorOrderDeleteHandler
{
    public function __construct(
        private VendorOrdersRepositoryContract $vendorOrdersRepository,
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->vendorOrdersRepository->delete($resourceId);
    }
}
