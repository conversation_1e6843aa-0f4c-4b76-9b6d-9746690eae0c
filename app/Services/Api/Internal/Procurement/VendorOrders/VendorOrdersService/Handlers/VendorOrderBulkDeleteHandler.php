<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers;

use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\VendorOrdersRepositoryContract;
use App\Traits\HasOrderedUuid;

class VendorOrderBulkDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly VendorOrdersRepositoryContract $vendorOrdersRepository,
        private readonly DocumentsRepositoryContract $documentsRepository
    ) {
    }

    public function run(array $ids): void
    {
        $this->vendorOrdersRepository->deleteWhereIn($ids);
        $this->documentsRepository->deleteWhereDocumentableIdIn($ids);
    }
}
