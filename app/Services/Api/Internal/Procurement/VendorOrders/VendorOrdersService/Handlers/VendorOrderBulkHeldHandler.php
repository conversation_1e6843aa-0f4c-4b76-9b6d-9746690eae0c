<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrdersService\Handlers;

use App\Contracts\Repositories\VendorOrdersRepositoryContract;

readonly class VendorOrderBulkHeldHandler
{
    public function __construct(
        private VendorOrdersRepositoryContract $vendorOrdersRepository
    ) {
    }

    public function run(array $ids): void
    {
        $this->vendorOrdersRepository->updateWhereIn(
            $ids,
            ['held' => true]
        );
    }
}
