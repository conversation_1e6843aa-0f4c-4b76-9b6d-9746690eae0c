<?php

namespace App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\VendorOrderItemsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Repositories\VendorOrdersRepositoryContract;
use App\Services\Api\Internal\Procurement\VendorOrders\Traits\VendorOrderTotalCalculator;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\DTO\VendorOrderItemDTO;
use App\Services\Api\Internal\Procurement\VendorOrders\VendorOrderItemsService\VendorOrderItemVatCalculatorService;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use InvalidArgumentException;

class VendorOrderItemCreateHandler
{
    use HasOrderedUuid;
    use PrecisionCalculator;
    use VendorOrderTotalCalculator;

    protected string $resourceId;
    public function __construct(
        private readonly VendorOrderItemsRepositoryContract $vendorOrderItemsRepository,
        private readonly VendorOrdersRepositoryContract $vendorOrdersRepository,
        private readonly VendorOrderItemVatCalculatorService $vatCalculatorService
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof VendorOrderItemDTO) {
            throw new InvalidArgumentException('Invalid DTO');
        }

        // Получаем заказ для проверки настроек НДС
        $order = $this->vendorOrdersRepository->show($dto->orderId);
        if (!$order) {
            throw new InvalidArgumentException('Vendor order not found');
        }

        // Устанавливаем значения по умолчанию
        if (!$dto->currencyId) {
            // Получаем базовую валюту кабинета
            $baseCurrency = \DB::table('cabinet_currencies')
                ->where('cabinet_id', $order->cabinet_id)
                ->where('is_default', true)
                ->first();
            $dto->currencyId = $baseCurrency?->id;
        }
        
        if (!$dto->priceInCurrency) {
            $dto->priceInCurrency = '0';
        }
        
        if (!$dto->currencyRateToBase) {
            $dto->currencyRateToBase = '1';
        }
        
        if (!$dto->discount) {
            $dto->discount = '0';
        }

        // Автоматически назначаем ставку НДС "Без НДС" если has_vat = false и ставка не указана
        if (!$order->has_vat && !$dto->vatRateId) {
            $dto->vatRateId = $this->vatCalculatorService->getAutoVatRateId($order->cabinet_id);
        }

        $dto->priceInBase = $this->multiply($dto->priceInCurrency, $dto->currencyRateToBase);
        $dto->amountInBase = $this->multiply($dto->priceInBase, $dto->quantity);

        // Используем новый расчет с НДС
        $dto->totalPrice = $this->vatCalculatorService->calculateItemTotal(
            $dto->orderId,
            $dto->priceInCurrency,
            (string)$dto->quantity,
            $dto->discount,
            $dto->vatRateId,
            $order->cabinet_id
        );

        $this->vendorOrderItemsRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        // Используем новый пересчет общей суммы с НДС
        $this->updateVendorOrderTotal($dto->orderId);

        return $this->resourceId;
    }
}
