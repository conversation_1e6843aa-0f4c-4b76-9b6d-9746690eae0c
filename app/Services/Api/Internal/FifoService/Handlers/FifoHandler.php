<?php

namespace App\Services\Api\Internal\FifoService\Handlers;

use App\Contracts\Repositories\DocumentsRepositoryContract;
use App\Contracts\Repositories\ShipmentItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\ShipmentWarehouseItemRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use RuntimeException;
use stdClass;

readonly class FifoHandler
{
    public function __construct(
        private ShipmentItemsRepositoryContract $shipmentItemsRepository,
        private WarehouseItemsRepositoryContract $warehouseItemsRepository,
        private ShipmentWarehouseItemRepositoryContract $shipmentWarehouseItemRepository,
        private ShipmentsRepositoryContract $shipmentsRepository,
        private DocumentsRepositoryContract $documentsRepository
    ) {
    }

    public function run(string $resourceId, bool $delete = false): void
    {
        // Получаем дату и ID склада
        $shipmentDetails = $this->shipmentItemsRepository->getShipmentDetails(
            $resourceId,
            ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id']
        );

        if (!$shipmentDetails) {
            throw new RuntimeException('Shipment not found');
        }

        $shipmentDate = $shipmentDetails->date_from;
        $warehouseId = $shipmentDetails->warehouse_id;
        $productId = $shipmentDetails->product_id;


        // Получаем все новейшие позиции отгрузки для данного продукта и склада
        $newestShipmentItems = $this->shipmentItemsRepository
            ->getNewestShipmentItems($productId, $shipmentDate, $warehouseId);


        if (!$newestShipmentItems) {
            return;
        }

        $shipmentIds = $newestShipmentItems
            ->pluck('id')->toArray();

        // Возвращаем все партии обратно на склад
        $this->warehouseItemsRepository
            ->returnWarehouseItemsFromShipmentItemIds($shipmentIds);


        // Удаляем все записи по этим отгрузкам
        $this->shipmentWarehouseItemRepository
            ->deleteWhereInIds($shipmentIds);

        // Удаляем позицию, если пришло условие для удаление
        // (нужно для возможности повторного использования метода при delete shipment item запросе)
        if ($delete) {
            $this->shipmentItemsRepository->delete($resourceId);
            $this->documentsRepository->deleteWhereDocumentableId($resourceId);

            $newestShipmentItems = $newestShipmentItems->reject(function ($item) use ($resourceId) {
                return $item->id === $resourceId;
            });
        }

        foreach ($newestShipmentItems as $newestShipmentItem) {
            $this->handleFIFO($newestShipmentItem, $warehouseId);
        }
    }

    public function handleBulk(array $shipmentItemIds, bool $delete = false): void
    {
        // Получаем все необходимые данные одним запросом
        $shipmentDetails = $this->shipmentItemsRepository->getBulkShipmentDetails(
            $shipmentItemIds,
            ['shipments.date_from', 'shipments.warehouse_id', 'shipment_items.product_id']
        );

        if ($shipmentDetails->isEmpty()) {
            throw new RuntimeException('Shipments not found');
        }

        // Группируем элементы по warehouse_id и date_from для оптимизации
        $groupedShipments = $shipmentDetails->groupBy(function ($item) {
            return $item->warehouse_id . '_' . $item->date_from;
        });

        foreach ($groupedShipments as $shipments) {
            $warehouseId = $shipments->first()->warehouse_id;
            $shipmentDate = $shipments->first()->date_from;
            $productIds = $shipments->pluck('product_id')->unique();

            // Получаем все новейшие позиции отгрузки для данных продуктов и склада
            $newestShipmentItems = $this->shipmentItemsRepository
                ->getBulkNewestShipmentItems($productIds->toArray(), $shipmentDate, $warehouseId);

            if ($newestShipmentItems->isEmpty()) {
                continue;
            }

            $allShipmentIds = $newestShipmentItems->pluck('id')->toArray();

            // Возвращаем все партии обратно на склад одним запросом
            $this->warehouseItemsRepository
                ->returnWarehouseItemsFromShipmentItemIds($allShipmentIds);

            // Удаляем все записи по этим отгрузкам одним запросом
            $this->shipmentWarehouseItemRepository
                ->deleteWhereInIds($allShipmentIds);

            if ($delete) {
                $deleteIds = array_intersect($allShipmentIds, $shipmentItemIds);
                if (!empty($deleteIds)) {
                    $this->shipmentItemsRepository->bulkDelete($deleteIds);
                    $this->documentsRepository->deleteWhereDocumentableIdIn($deleteIds);
                }

                $newestShipmentItems = $newestShipmentItems->reject(function ($item) use ($shipmentItemIds) {
                    return in_array($item->id, $shipmentItemIds, true);
                });
            }

            // Обрабатываем каждую группу отгрузок
            $this->handleBulkFIFO($newestShipmentItems, $warehouseId);
        }
    }

    private function handleFIFO(stdClass $shipmentItem, string $warehouseId): void
    {
        $shipmentWarehouseData = [];
        $totalShipmentCost = 0;
        $batchUpdateWarehouseItems = [];
        $time = Carbon::now();

        $warehouseItems = $this->warehouseItemsRepository
            ->get($shipmentItem->product_id, $warehouseId, $shipmentItem->shipment_date);

        $remaintQuantity = $shipmentItem->quantity;
        $totalCost = 0;
        $recidual = $warehouseItems !== null ? $warehouseItems->sum('quantity') : 0;

        foreach ($warehouseItems as $warehouseItem) {

            if ($remaintQuantity <= 0) {
                break;
            }

            if ($warehouseItem->quantity === 0) {
                continue;
            }

            if ($warehouseItem->quantity >= $remaintQuantity) {
                $warehouseItem->quantity -= $remaintQuantity;
                $used = $remaintQuantity;
                $remaintQuantity = 0;
            } else {
                $remaintQuantity -= $warehouseItem->quantity;
                $used = $warehouseItem->quantity;
                $warehouseItem->quantity = 0;
            }

            $batchUpdateWarehouseItems[] = [
                'id' => $warehouseItem->id,
                'created_at' => $warehouseItem->created_at,
                'updated_at' => $time,
                'warehouse_id' => $warehouseId,
                'cell_id' => $warehouseItem->cell_id,
                'product_id' => $warehouseItem->product_id,
                'acceptance_id' => $warehouseItem->acceptance_id,
                'batch_number' => $warehouseItem->batch_number,
                'quantity' => $warehouseItem->quantity,
                'unit_price' => $warehouseItem->unit_price,
                'total_price' => $warehouseItem->total_price,
                'received_at' => $warehouseItem->received_at,
                'status' => $warehouseItem->quantity > 0 ? 'in_stock' : 'out_of_stock',
            ];

            $shipmentWarehouseData[] = [
                'shipment_item_id' => $shipmentItem->id,
                'warehouse_item_id' => $warehouseItem->id,
                'quantity' => $used,
                'shipment_date' => $shipmentItem->shipment_date,
            ];

            $totalCost += $warehouseItem->unit_price * $used;
        }

        $totalShipmentCost += $totalCost;

        $this->warehouseItemsRepository->upsert(
            $batchUpdateWarehouseItems,
            ['quantity', 'status', 'updated_at']
        );

        $this->shipmentItemsRepository
            ->update(
                $shipmentItem->id,
                [
                    'updated_at' => $time,
                    'total_cost' => $totalCost,
                    'cost' => $totalCost / $shipmentItem->quantity,
                    'profit' => $shipmentItem->total_price - $totalCost,
                ],
            );

        $this->shipmentWarehouseItemRepository
            ->insert($shipmentWarehouseData);

        $this->shipmentsRepository
            ->update(
                $shipmentItem->shipment_id,
                [
                    'updated_at' => $time,
                    'total_cost' => $totalShipmentCost,
                    'profit' => $shipmentItem->total_price - $totalShipmentCost,
                ],
            );
    }

    private function handleBulkFIFO(Collection $shipmentItems, string $warehouseId): void
    {
        $time = Carbon::now();
        $batchUpdateWarehouseItems = [];
        $shipmentWarehouseData = [];
        $shipmentUpdates = [];
        $shipmentTotals = []; // Для хранения общих сумм по каждой отгрузке

        // Группируем отгрузки по product_id для оптимизации
        $groupedItems = $shipmentItems->groupBy('product_id');

        foreach ($groupedItems as $productId => $items) {
            // Получаем все warehouse items для данного продукта одним запросом
            $warehouseItems = $this->warehouseItemsRepository
                ->get($productId, $warehouseId, $items->first()->shipment_date);

            $recidual = $warehouseItems !== null ? $warehouseItems->sum('quantity') : 0;

            foreach ($items as $shipmentItem) {
                $remaintQuantity = $shipmentItem->quantity;
                $totalCost = 0;

                foreach ($warehouseItems as $warehouseItem) {
                    if ($remaintQuantity <= 0 || $warehouseItem->quantity === 0) {
                        continue;
                    }

                    $used = min($warehouseItem->quantity, $remaintQuantity);
                    $warehouseItem->quantity -= $used;
                    $remaintQuantity -= $used;

                    $batchUpdateWarehouseItems[] = [
                        'id' => $warehouseItem->id,
                        'created_at' => $warehouseItem->created_at,
                        'updated_at' => $time,
                        'warehouse_id' => $warehouseId,
                        'cell_id' => $warehouseItem->cell_id,
                        'product_id' => $warehouseItem->product_id,
                        'acceptance_id' => $warehouseItem->acceptance_id,
                        'batch_number' => $warehouseItem->batch_number,
                        'quantity' => $warehouseItem->quantity,
                        'unit_price' => $warehouseItem->unit_price,
                        'total_price' => $warehouseItem->total_price,
                        'received_at' => $warehouseItem->received_at,
                        'status' => $warehouseItem->quantity > 0 ? 'in_stock' : 'out_of_stock',
                    ];

                    $shipmentWarehouseData[] = [
                        'shipment_item_id' => $shipmentItem->id,
                        'warehouse_item_id' => $warehouseItem->id,
                        'quantity' => $used,
                        'shipment_date' => $shipmentItem->shipment_date,
                    ];

                    $totalCost += $warehouseItem->unit_price * $used;
                }

                // Обновляем общие суммы для отгрузки
                if (!isset($shipmentTotals[$shipmentItem->shipment_id])) {
                    $shipmentTotals[$shipmentItem->shipment_id] = [
                        'total_cost' => 0,
                        'total_price' => 0
                    ];
                }
                $shipmentTotals[$shipmentItem->shipment_id]['total_cost'] += $totalCost;
                $shipmentTotals[$shipmentItem->shipment_id]['total_price'] += $shipmentItem->total_price;

                $shipmentUpdates[] = [
                    'id' => $shipmentItem->id,
                    'updated_at' => $time,
                    'total_cost' => $totalCost,
                    'cost' => $totalCost / $shipmentItem->quantity,
                    'profit' => $shipmentItem->total_price - $totalCost,
                    'shipment_id' => $shipmentItem->shipment_id,
                    'product_id' => $shipmentItem->product_id,
                    'quantity' => $shipmentItem->quantity
                ];
            }
        }

        // Выполняем все обновления одним запросом
        if (!empty($batchUpdateWarehouseItems)) {
            $this->warehouseItemsRepository->upsert(
                $batchUpdateWarehouseItems,
                ['quantity', 'status', 'updated_at']
            );
        }

        if (!empty($shipmentWarehouseData)) {
            $this->shipmentWarehouseItemRepository->insert($shipmentWarehouseData);
        }

        if (!empty($shipmentUpdates)) {
            $this->shipmentItemsRepository->bulkUpdate($shipmentUpdates);
        }

        // Обновляем данные отгрузок
        foreach ($shipmentTotals as $shipmentId => $totals) {
            $this->shipmentsRepository->update($shipmentId, [
                'updated_at' => $time,
                'total_cost' => $totals['total_cost'],
                'profit' => $totals['total_price'] - $totals['total_cost']
            ]);
        }
    }
}
