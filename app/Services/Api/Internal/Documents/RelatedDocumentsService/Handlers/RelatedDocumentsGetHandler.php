<?php

namespace App\Services\Api\Internal\Documents\RelatedDocumentsService\Handlers;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

readonly class RelatedDocumentsGetHandler
{
    /**
     * Преобразует плоский список узлов в древовидную структуру
     */
    private function buildTree(Collection $nodes): array
    {
        $tree = [];
        $indexed = [];

        // Индексируем узлы по documentable_id для быстрого доступа
        foreach ($nodes as $node) {
            $node->children = [];
            $indexed[$node->documentable_id] = $node;
        }

        // Строим дерево
        foreach ($nodes as $node) {
            if ($node->parent_id && isset($indexed[$node->parent_id])) {
                $indexed[$node->parent_id]->children[] = $node;
            } else {
                $tree[] = $node;
            }
        }

        return $tree;
    }

    public function run(string $documentId): Collection
    {
        // Сначала получаем текущий узел для определения его границ и tree_id
        $currentNode = DB::table('documents')
            ->select(['lft', 'rgt', 'tree_id'])
            ->where('documentable_id', $documentId)
            ->first();

        if (!$currentNode) {
            return collect([]);
        }

        // Получаем все узлы дерева одним запросом
        $nodes = DB::table('documents')
            ->select([
                'documentable_id',
                'documentable_type',
                'tree_id',
                'lft',
                'rgt',
                'parent_id'
            ])
            ->where('tree_id', $currentNode->tree_id)
            ->orderBy('lft')
            ->get();

        // Преобразуем в древовидную структуру
        return collect($this->buildTree($nodes));
    }
}
