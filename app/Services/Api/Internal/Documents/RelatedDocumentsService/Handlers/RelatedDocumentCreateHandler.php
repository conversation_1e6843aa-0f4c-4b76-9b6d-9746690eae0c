<?php

namespace App\Services\Api\Internal\Documents\RelatedDocumentsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\RelatedDocumentsRepositoryContract;
use App\Repositories\Documents\DocumentsRepository;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\DTO\RelatedDocumentDTO;
use Exception;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

readonly class RelatedDocumentCreateHandler
{
    public function __construct(
        private RelatedDocumentsRepositoryContract $repository,
        private DocumentsRepository $documentsRepository
    ) {
    }

    /**
     * @throws Exception|Throwable
     */
    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof RelatedDocumentDTO) {
            throw new RuntimeException('Invalid DTO type');
        }

        $documentType = $this->documentsRepository->getType($dto->documentId);
        $bindedDocumentType = $this->documentsRepository->getType($dto->bindedDocumentId);

        try {
            DB::beginTransaction();

            // Определяем, какой документ должен быть родителем
            [$parentType, $childType] = $this->resolveParentChild($documentType, $bindedDocumentType);

            // Определяем ID документов на основе их типов
            $parentId = $parentType === $documentType ? $dto->documentId : $dto->bindedDocumentId;
            $childId = $parentType === $documentType ? $dto->bindedDocumentId : $dto->documentId;

            // Проверяем существование связи
            $existingRelation = DB::table('documents')
                ->where('documentable_id', $childId)
                ->where('parent_id', $parentId)
                ->exists();

            if ($existingRelation) {
                DB::commit();
                throw new RuntimeException('Documents already related');
            }

            $this->repository->addChild($parentId, $childId);

            DB::commit();
            return 'success';
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Определяет, какой документ должен быть родителем на основе типов документов
     */
    private function resolveParentChild(string $type1, string $type2): array
    {
        $relations = [
            'shipments' => [
                'customer_orders' => false, // CustomerOrder родитель для Shipment
            ],
            'acceptances' => [
                'vendor_orders' => false,   // VendorOrder родитель для Acceptance
                'customer_orders' => true,   // Acceptance родитель для CustomerOrder
            ],
            'vendor_orders' => [
                'customer_orders' => false,   // CustomerOrder родитель для VendorOrder
            ],
            'incoming_payments' => [
                'shipments' => false,   // Acceptance родитель для IncomingPayment
                'customer_orders' => false,   // CustomerOrder родитель для IncomingPayment
            ],
            'outgoing_payments' => [
                'customer_orders' => false,   // CustomerOrder родитель для OutgoingPayment
            ]
        ];

        // Проверяем прямую связь
        if (isset($relations[$type1][$type2])) {
            return $relations[$type1][$type2]
                ? [$type1, $type2]    // type1 родитель
                : [$type2, $type1];   // type2 родитель
        }

        // Проверяем обратную связь
        if (isset($relations[$type2][$type1])) {
            return $relations[$type2][$type1]
                ? [$type2, $type1]    // type2 родитель
                : [$type1, $type2];   // type1 родитель
        }

        throw new RuntimeException("Can relate document {$type1} with {$type2}");
    }
}
