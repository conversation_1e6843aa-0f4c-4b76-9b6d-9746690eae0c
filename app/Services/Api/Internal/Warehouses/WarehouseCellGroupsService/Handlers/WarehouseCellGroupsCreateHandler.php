<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;
use App\Traits\HasOrderedUuid;

class WarehouseCellGroupsCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly WarehouseCellGroupsRepositoryContract $repository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        $this->repository->insert(
            $dto->toInsertArray($this->resourceId)
        );

        return $this->resourceId;
    }
}
