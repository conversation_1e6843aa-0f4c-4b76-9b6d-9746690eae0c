<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers;

use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehouseCellGroupsGetHandler
{
    public function __construct(
        private WarehouseCellGroupsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
