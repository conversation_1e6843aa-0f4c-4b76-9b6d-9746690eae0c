<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellGroupsService\Handlers;

use App\Contracts\Repositories\WarehouseCellGroupsRepositoryContract;

readonly class WarehouseCellGroupsShowHandler
{
    public function __construct(
        private WarehouseCellGroupsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): ?object
    {
        return $this->repository->show($resourceId);
    }
}
