<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseCellsService\Handlers;

use App\Contracts\Repositories\WarehouseCellsRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehouseCellsGetHandler
{
    public function __construct(
        private WarehouseCellsRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
