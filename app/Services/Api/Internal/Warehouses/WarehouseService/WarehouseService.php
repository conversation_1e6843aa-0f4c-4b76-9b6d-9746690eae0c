<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Warehouses\WarehouseService\Handlers\WarehousesCreateHandler;
use App\Services\Api\Internal\Warehouses\WarehouseService\Handlers\WarehousesDeleteHandler;
use App\Services\Api\Internal\Warehouses\WarehouseService\Handlers\WarehousesGetHandler;
use App\Services\Api\Internal\Warehouses\WarehouseService\Handlers\WarehousesShowHandler;
use App\Services\Api\Internal\Warehouses\WarehouseService\Handlers\WarehousesUpdateHandler;
use Illuminate\Support\Collection;

readonly class WarehouseService implements WarehouseServiceContract
{
    public function __construct(
        private WarehousesCreateHandler $createHandler,
        private WarehousesUpdateHandler $updateHandler,
        private WarehousesGetHandler $getHandler,
        private WarehousesDeleteHandler $deleteHandler,
        private WarehousesShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
