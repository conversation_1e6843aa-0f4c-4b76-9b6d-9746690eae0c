<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseService\Handlers;

use App\Contracts\Repositories\WarehousesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WarehousesGetHandler
{
    public function __construct(
        private WarehousesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
