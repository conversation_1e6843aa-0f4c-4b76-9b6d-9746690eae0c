<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehouseOrderSchemesRepositoryContract;
use App\Contracts\Repositories\WarehousesRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehouseService\DTO\WarehouseDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class WarehousesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly WarehousesRepositoryContract $repository,
        private readonly WarehouseOrderSchemesRepositoryContract $schemeRepository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehouseDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        if (isset($dto->order_scheme['control_operational_balances'])) {
            $this->schemeRepository->updateByWarehouseId(
                $dto->resourceId,
                ['control_operational_balances' => $dto->order_scheme['control_operational_balances']]
            );
        }

        $warehouse = $this->repository->show($dto->resourceId);

        if ($warehouse) {
            if ($dto->isDefault && !$warehouse->is_default) {
                $this->repository->updateWhereCabinetId($warehouse->cabinet_id, ['is_default' => false]);
            }

            $this->repository->update(
                $dto->resourceId,
                $dto->toUpdateArray()
            );
        }


    }
}
