<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\Handlers;

use App\Contracts\Repositories\WorkSchedulesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class WorkSchedulesGetHandler
{
    public function __construct(
        private WorkSchedulesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): ?Collection
    {
        return $this->repository->get(
            $dto->id,
            $dto->filters,
            $dto->fields,
            $dto->sortField,
            $dto->sortDirection,
            $dto->page,
            $dto->perPage
        );
    }
}
