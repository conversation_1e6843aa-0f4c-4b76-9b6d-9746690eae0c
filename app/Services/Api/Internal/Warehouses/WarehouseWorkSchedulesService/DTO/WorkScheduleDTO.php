<?php

namespace App\Services\Api\Internal\Warehouses\WarehouseWorkSchedulesService\DTO;

use App\Contracts\DtoContract;
use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use Carbon\Carbon;

class WorkScheduleDTO implements HasInsertArrayDtoContract, DtoContract, HasUpdateArrayDtoContract
{
    public function __construct(
        public ?string $cabinet_id,
        public string $name,
        public string $date_from,
        public string $filling_type,
        public array $filling_template,
        public ?string $description,
        public string $date_to,
        public ?int $cycle_day_lenght,
        public ?string $cycle_day_from,
        public ?string $resourceId,
        public bool $keep_holidays = false,
        public array $holiday_schedule = [],
    ) {
    }

    public function toInsertArray(string $id, ?string $employeeId = null): array
    {
        return [
            'id' => $id,
            'cabinet_id' => $this->cabinet_id,
            'name' => $this->name,
            'date_from' => $this->date_from,
            'filling_type' => $this->filling_type,
            'description' => $this->description,
            'date_to' => $this->date_to,
            'cycle_day_lenght' => $this->cycle_day_lenght,
            'cycle_day_from' => $this->cycle_day_from,
            'keep_holidays' => $this->keep_holidays
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinet_id: $data['cabinet_id'] ?? null,
            name: $data['name'],
            date_from: isset($data['date_from']) ? Carbon::parse($data['date_from']) : null,
            filling_type: $data['filling_type'],
            filling_template: isset($data['filling_template']) ? (array)json_decode($data['filling_template'], true) : [],
            description: $data['description'] ?? null,
            date_to: isset($data['date_to']) ? Carbon::parse($data['date_to'])->toDateString() : Carbon::now()->endOfYear()->toDateString(),
            cycle_day_lenght: $data['cycle_day_lenght'] ?? null,
            cycle_day_from: $data['cycle_day_from'] ?? null,
            resourceId: $data['id'] ?? null,
            keep_holidays: $data['keep_holidays'] ?? false,
            holiday_schedule: isset($data['holiday_schedule']) ? (array)json_decode($data['holiday_schedule'], true) : [],
        );
    }

    public function toUpdateArray(): array
    {
        return [
            'name' => $this->name,
            'date_from' => $this->date_from,
            'filling_type' => $this->filling_type,
            'description' => $this->description,
            'date_to' => $this->date_to,
            'cycle_day_lenght' => $this->cycle_day_lenght,
            'cycle_day_from' => $this->cycle_day_from,
            'keep_holidays' => $this->keep_holidays
        ];
    }
}
