<?php

namespace App\Services\Api\Internal\Warehouses\WarehousePhonesService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\WarehousePhonesRepositoryContract;
use App\Services\Api\Internal\Warehouses\WarehousePhonesService\DTO\WarehousePhoneDTO;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;

class WarehousePhonesUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly WarehousePhonesRepositoryContract $repository,
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if (!$dto instanceof WarehousePhoneDTO) {
            throw new InvalidArgumentException('Invalid dto');
        }

        $this->repository->update(
            $dto->resourceId,
            $dto->toUpdateArray()
        );
    }
}
