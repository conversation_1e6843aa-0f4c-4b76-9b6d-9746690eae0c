<?php

namespace App\Services\Api\Internal\GoodsTransferService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Products\GoodsTransferServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\GoodsTransferService\Handlers\GoodsTransferCreateHandler;
use App\Services\Api\Internal\GoodsTransferService\Handlers\GoodsTransferDeleteHandler;
use App\Services\Api\Internal\GoodsTransferService\Handlers\GoodsTransferGetHandler;
use App\Services\Api\Internal\GoodsTransferService\Handlers\GoodsTransferShowHandler;
use App\Services\Api\Internal\GoodsTransferService\Handlers\GoodsTransferUpdateHandler;
use Illuminate\Support\Collection;

readonly class GoodsTransferService implements GoodsTransferServiceContract
{
    public function __construct(
        private GoodsTransferCreateHandler $createHandler,
        private GoodsTransferGetHandler $getHandler,
        private GoodsTransferShowHandler $showHandler,
        private GoodsTransferUpdateHandler $updateHandler,
        private GoodsTransferDeleteHandler $deleteHandler,
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
