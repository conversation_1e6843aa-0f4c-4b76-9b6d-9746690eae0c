<?php

namespace App\Services\Api\Internal\Workspace\StatusTypesService;

use App\Contracts\Services\Internal\StatusTypesServiceContract;
use App\Services\Api\Internal\Workspace\StatusTypesService\Handlers\StatusTypeGetHandler;
use Illuminate\Support\Collection;

readonly class StatusTypeService implements StatusTypesServiceContract
{
    public function __construct(
        private StatusTypeGetHandler $handler
    ) {
    }

    public function get(): Collection
    {
        return $this->handler->run();
    }
}
