<?php

namespace App\Services\Api\Internal\Workspace\EmployeesService;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Cabinet\EmployeeServiceContract;
use App\DTO\IndexRequestDTO;
use App\Models\User;
use App\Services\Api\Internal\Workspace\EmployeesService\Handlers\EmployeeBulkDeleteHandler;
use App\Services\Api\Internal\Workspace\EmployeesService\Handlers\EmployeesDeleteHandler;
use App\Services\Api\Internal\Workspace\EmployeesService\Handlers\EmployeesGetHandler;
use App\Services\Api\Internal\Workspace\EmployeesService\Handlers\EmployeesGetProfileHandler;
use App\Services\Api\Internal\Workspace\EmployeesService\Handlers\EmployeesShowHandler;
use App\Services\Api\Internal\Workspace\EmployeesService\Handlers\EmployeesUpdateHandler;
use Illuminate\Support\Collection;

readonly class EmployeesService implements EmployeeServiceContract
{
    public function __construct(
        private EmployeesGetHandler $getHandler,
        private EmployeesShowHandler $showHandler,
        private EmployeesUpdateHandler $updateHandler,
        private EmployeesDeleteHandler $deleteHandler,
        private EmployeesGetProfileHandler $getProfileHandler,
        private EmployeeBulkDeleteHandler $bulkDeleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function getProfile(User $user, string $cabinetId): ?object
    {
        return $this->getProfileHandler->run($user, $cabinetId);
    }
    public function bulkDelete(array $ids): void
    {
        $this->bulkDeleteHandler->run($ids);
    }
}
