<?php

namespace App\Services\Api\Internal\Workspace\DepartmentsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\DepartmentsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Workspace\DepartmentsService\Handlers\DepartmentsCreateHandler;
use App\Services\Api\Internal\Workspace\DepartmentsService\Handlers\DepartmentsDeleteHandler;
use App\Services\Api\Internal\Workspace\DepartmentsService\Handlers\DepartmentsGetHandler;
use App\Services\Api\Internal\Workspace\DepartmentsService\Handlers\DepartmentsShowHandler;
use App\Services\Api\Internal\Workspace\DepartmentsService\Handlers\DepartmentsUpdateHandler;
use Illuminate\Support\Collection;

readonly class DepartmentsService implements DepartmentsServiceContract
{
    public function __construct(
        private DepartmentsGetHandler $getHandler,
        private DepartmentsCreateHandler $createHandler,
        private DepartmentsUpdateHandler $updateHandler,
        private DepartmentsDeleteHandler $deleteHandler,
        private DepartmentsShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }
}
