<?php

namespace App\Services\Api\Internal\Workspace\CabinetsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Cabinet\CabinetsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Workspace\CabinetsService\Handlers\CabinetCreateHandler;
use App\Services\Api\Internal\Workspace\CabinetsService\Handlers\CabinetDeleteHandler;
use App\Services\Api\Internal\Workspace\CabinetsService\Handlers\CabinetGetHandler;
use App\Services\Api\Internal\Workspace\CabinetsService\Handlers\CabinetShowHandler;
use App\Services\Api\Internal\Workspace\CabinetsService\Handlers\CabinetUpdateHandler;
use Illuminate\Support\Collection;

readonly class CabinetsService implements CabinetsServiceContract
{
    public function __construct(
        private CabinetCreateHandler $createHandler,
        private CabinetGetHandler $getHandler,
        private CabinetShowHandler $showHandler,
        private CabinetUpdateHandler $updateHandler,
        private CabinetDeleteHandler $deleteHandler
    ) {
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data['user_id'], $data['filter']);
    }
}
