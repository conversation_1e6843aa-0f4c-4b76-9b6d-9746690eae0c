<?php

namespace App\Services\Api\Internal\Workspace\CabinetInviteService\Handlers;

use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\UsersRepositoryContract;
use App\DTO\UserDTO;
use App\Enums\Api\Internal\CabinetInviteStatusEnum;
use App\Exceptions\AccessDeniedException;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;

class CabinetInvitesAcceptHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CabinetInvitesRepositoryContract $repository,
        private readonly EmployeeRepositoryContract $employeeRepository,
        private readonly CabinetEmployeeRepositoryContract $cabinetEmployeeRepository,
        private readonly UsersRepositoryContract $usersRepository
    ) {
    }

    /**
     * @throws AccessDeniedException
     * @throws NotFoundException
     */
    public function run(string $id, UserDTO $userDto): void
    {
        $data = $this->repository->find($id);

        if (!$data) {
            throw new NotFoundException('Invite not found');
        }

        if ($data->status !== CabinetInviteStatusEnum::WAITING->value) {
            throw new AccessDeniedException('Only waiting invites can be accepted.');
        }

        $employeeId = $this->generateUuid();

        $this->employeeRepository->insert([
            [
                'id' => $employeeId,
                'user_id' => $userDto->userId,
                'lastname' => $userDto->lastname,
                'firstname' => $userDto->firstname,
                'patronymic' => $userDto->patronymic,
                'email' => $userDto->email,
                'status' => 'active',
                'created_at' => Carbon::now(),
                'department_id' => $data->department_id
            ]
        ]);

        $this->cabinetEmployeeRepository->insert([
            [
                'cabinet_id' => $data->cabinet_id,
                'employee_id' => $employeeId,
                'created_at' => Carbon::now()
            ]
        ]);

        $this->repository->update(
            $data->id,
            [
                'status' => CabinetInviteStatusEnum::ACCEPTED
            ]
        );

        $this->usersRepository->updateByEmail(
            $userDto->email,
            ['current_cabinet_id' => $data->cabinet_id]
        );
    }
}
