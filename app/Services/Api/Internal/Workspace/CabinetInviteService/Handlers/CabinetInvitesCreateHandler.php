<?php

namespace App\Services\Api\Internal\Workspace\CabinetInviteService\Handlers;

use App\Contracts\Repositories\CabinetInvitesRepositoryContract;
use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Workspace\CabinetInviteService\DTO\CabinetInviteDTO;
use App\Services\Api\Internal\Workspace\CabinetInviteService\Jobs\SendCabinetInviteJob;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class CabinetInvitesCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly CabinetInvitesRepositoryContract $repository,
        private readonly EmployeeRepositoryContract $employeeRepository,
        private readonly DepartmentsRepositoryContract $departmentsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    /**
     * @throws NotFoundException
     */
    public function run(CabinetInviteDTO $dto): string
    {
        $dto->token = Str::random(16);

        $employee = $this->employeeRepository->getByUserIdAndCabinet(
            Auth::id(),
            $dto->cabinetId
        );
        if (!$employee) {
            throw new NotFoundException('Employee not found');
        }

        if (!$dto->departmentId) {

            $defaultDepartment = $this->departmentsRepository->findDefault($dto->cabinetId);
            if (!$defaultDepartment) {
                throw new NotFoundException('Default department not found in cabinet');
            }

            $dto->departmentId = $defaultDepartment->id;
        }

        $this->repository->insert(
            $dto->toInsertArray(
                $this->resourceId,
                $employee->id
            )
        );

        dispatch(
            new SendCabinetInviteJob([
                    'email' => $dto->email,
                    'token' => $dto->token
            ])
        );

        return $this->resourceId;
    }
}
