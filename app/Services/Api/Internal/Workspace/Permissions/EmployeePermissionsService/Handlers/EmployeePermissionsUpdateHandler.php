<?php

namespace App\Services\Api\Internal\Workspace\Permissions\EmployeePermissionsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\EmployeePermissionsRepositoryContract;
use App\Services\Api\Internal\Workspace\Permissions\EmployeePermissionsService\DTO\EmployeePermissionDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Container\BindingResolutionException;
use InvalidArgumentException;

readonly class EmployeePermissionsUpdateHandler
{
    use HasOrderedUuid;

    public function __construct(
        private EmployeePermissionsRepositoryContract $repository
    ) {
    }

    /**
     * @throws BindingResolutionException
     */
    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof EmployeePermissionDTO) {

            $this->repository
                ->upsert($dto->toUpdateArray());

            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
