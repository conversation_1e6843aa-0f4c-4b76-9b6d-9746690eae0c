<?php

namespace App\Services\Api\Internal\Workspace\Permissions\RolesService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\RolesServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\NotFoundException;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers\RolesCreateHandler;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers\RolesDeleteHandler;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers\RolesGetHandler;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers\RolesShowHandler;
use App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers\RolesUpdateHandler;
use Illuminate\Support\Collection;

readonly class RolesService implements RolesServiceContract
{
    public function __construct(
        private RolesCreateHandler $createHandler,
        private RolesGetHandler $getHandler,
        private RolesShowHandler $showHandler,
        private RolesUpdateHandler $updateHandler,
        private RolesDeleteHandler $deleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    /**
     * @throws NotFoundException
     */
    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
