<?php

namespace App\Services\Api\Internal\Workspace\Permissions\RolesService\Handlers;

use App\Contracts\Repositories\RolesRepositoryContract;
use App\DTO\IndexRequestDTO;
use Illuminate\Support\Collection;

readonly class RolesGetHandler
{
    public function __construct(
        private RolesRepositoryContract $repository
    ) {
    }

    public function run(IndexRequestDTO $dto): Collection
    {
        return $this->repository->get(
            id: $dto->id,
            page: $dto->page,
            perPage: $dto->perPage,
            sortField: $dto->sortField,
            sortDirection: $dto->sortDirection,
            fields: $dto->fields,
            filters: $dto->filters
        );
    }
}
