<?php

namespace App\Services\Api\Internal\Other\SuggestsService\DTO;

use App\Contracts\DtoContract;

class SuggestBankDTO implements DtoContract
{
    public function __construct(
        public string $query,
        public ?array $status,
        public ?array $type,
        public ?array $locations,
        public ?array $locationsBoost,
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            query: $data['query'],
            status: $data['status'] ?? null,
            type: $data['type'] ?? null,
            locations: $data['locations'] ?? null,
            locationsBoost: $data['locations_boost'] ?? null
        );
    }
}
