<?php

namespace App\Services\Api\Internal\Other\ExcelImportService\Handlers;

use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class DetailedReportHandler
{
    use HasOrderedUuid;

    public function run(Worksheet $sheet, string $cabinetId, string $departmentId, string $employeeId): void
    {

        $dataTitle = [
            'report_agency_order_number' => 'B1',  // Приложение к отчёту №6901034 об исполнении агентского поручения
            'agreement_offer_number' => 'B2',   // к Договору №ИР-33937/21 от 27.04.2021
            'report_for_period'	=> 'B63',        // за период с 01.11.2024 по 30.11.2024
            'agent_name' => 'B6',               // ООО "Интернет Решения"  Агент
            'agent_inn' => 'B7',                // ИНН	7704217370
            'agent_kpp' => 'B8',               // КПП	997750001
            'principal_name' => 'L6',           // Пикин Андрей Сергеевич, ИП   Принципал
            'principal_inn' => 'L7',            // ИНН	************
            'principal_kpp' => 'L8',           // КПП
        ];

        $columns = [
            '№' => 'number_row',
            'Исполнитель' => 'performer',
            'ИНН' => 'inn',
            'КПП' => 'kpp',
            '№ Отправления' => 'shipment_number',
            'Код товара OZON' => 'ozon_product_code',
            'Стоимость товара' => 'product_cost',
            'Тип доставки/отгрузки' => 'delivery_type',
            'Наименование работ, услуг' => 'name_of_services',
            'Сумма с НДС (RUR)' => 'amount_with_vat',
            'Сумма НДС (RUR)' => 'vat_amount',

        ];


        $parsedData = [];
        foreach ($dataTitle as $key => $cell) {
            $parsedData[$key] = $sheet->getCell($cell)->getValue();
        }

        // Получение заголовков колонок из 3 строки
        $headers = [];
        $headerRow = $sheet->getRowIterator(13, 13)->current();

        foreach ($headerRow->getCellIterator() as $cell) {
            $headers[] = trim($cell->getValue());
        }

        $startColumn = 'B';
        $endColumn = 'L';
        $columnsHeaders = [];

        for ($col = $startColumn; $col <= $endColumn; $col++) {
            $columnsHeaders[] = $sheet->getCell($col . '13')->getValue();
        }

        // Сопоставление заголовков с колонками
        $columnsColumnMapping = [];
        foreach ($columns as $columnsHeader => $columnKey) {
            $columnIndex = array_search($columnsHeader, $columnsHeaders);
            if ($columnIndex !== false) {
                $columnsColumnMapping[$columnKey] = Coordinate::stringFromColumnIndex(
                    Coordinate::columnIndexFromString($startColumn) + $columnIndex
                );
            }
        }


        // Чтение данных и присвоение значений
        $data = [];

        $data['dataTitle'] = $parsedData;
        $highestRow = $sheet->getHighestRow();

        for ($row = 16; $row <= $highestRow; $row++) {
            $rowData = [];
            foreach ($columnsColumnMapping as $columnKey => $columnLetter) {
                $rowData[$columnKey] = (string)$sheet->getCell($columnLetter . $row)->getValue();
            }
            if (!empty(array_filter($rowData))) {
                $data['item'][] = $rowData;
            }
        }

        $sold_amount = new Collection($data['item']);

        // Разбиваем коллекцию на чанки по 1000 элементов
        $chunks = $sold_amount->chunk(5);

        $chunks->each(function ($chunk) use ($cabinetId, $departmentId, $employeeId) {
            $records = $chunk->map(function ($item) use ($cabinetId, $departmentId, $employeeId) {

                if (!isset($item['id'])) {
                    $item['id'] = $this->generateUuid();
                }

                $item['cabinet_id'] = $cabinetId;
                $item['department_id'] = $departmentId;
                $item['employee_id'] = $employeeId;
                $item['created_at'] = now();
                $item['updated_at'] = now();

                return $item;

            })->toArray();

            //TODO ещё в разработке
            dd($records);

            // $this->ozonProductsRepositoryContract->upsert($records);
        });

    }

}
