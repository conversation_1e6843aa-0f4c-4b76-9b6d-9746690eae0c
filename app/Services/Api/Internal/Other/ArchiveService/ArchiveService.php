<?php

namespace App\Services\Api\Internal\Other\ArchiveService;

use App\Contracts\HasArchiveRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;

readonly class ArchiveService implements ArchiveServiceContract
{
    public function archive(HasArchiveRepositoryContract $repository, array $ids): void
    {
        $repository->archive($ids);
    }

    public function unarchive(HasArchiveRepositoryContract $repository, array $ids): void
    {
        $repository->unarchive($ids);
    }
}
