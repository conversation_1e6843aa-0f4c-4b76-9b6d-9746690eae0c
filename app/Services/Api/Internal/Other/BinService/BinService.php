<?php

namespace App\Services\Api\Internal\Other\BinService;

use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Services\Internal\BinServiceContract;
use App\DTO\IndexRequestDTO;
use App\Entities\BinItemEntity;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

readonly class BinService implements BinServiceContract
{
    public function __construct(
        private CabinetSettingRepositoryContract $cabinetSettings
    ) {
    }

    public function shouldUseSoftDelete(string $cabinetId): bool
    {
        $settings = $this->cabinetSettings->show($cabinetId);
        return $settings->use_bin ?? false;
    }

    public function index(IndexRequestDTO $dto): Collection
    {
        $entity = new BinItemEntity();
        [$baseFields, $relationFields] = $entity->parseFields($dto->fields);

        $query = $entity;
        $query->select($baseFields)
            ->where('cabinet_id', $dto->id);

        foreach ($relationFields as $relation => $fields) {
            $query->when(method_exists($entity, $relation), function ($query) use ($relation, $fields) {
                $query->with($relation, function ($builder) use ($fields) {
                    $builder->fields($fields);
                });
            });
        }

        return $query
            ->when($dto->sortField, function ($query) use ($dto) {
                return $query->sort($dto->sortField, $dto->sortDirection);
            })
            ->paginate($dto->perPage, $dto->page)
            ->get();
    }

    public function bulkForceDelete(array $ids): void
    {
        $items = DB::table('bin_items')
            ->whereIn('id', $ids)
            ->get();

        DB::transaction(function () use ($items) {
            foreach ($items as $item) {
                DB::table($item->table_name)
                    ->where('id', $item->record_id)
                    ->delete();

                DB::table('bin_items')
                    ->where('id', $item->id)
                    ->delete();
            }
        });
    }

    /**
     * @throws \Throwable
     */
    public function bulkRecover(array $ids): void
    {
        $items = DB::table('bin_items')
            ->whereIn('id', $ids)
            ->get();

        DB::transaction(function () use ($items) {
            foreach ($items as $item) {
                DB::table($item->table_name)
                    ->where('id', $item->record_id)
                    ->update(['deleted_at' => null]);

                DB::table('bin_items')
                    ->where('id', $item->id)
                    ->delete();
            }
        });
    }
}
