<?php

namespace App\Services\Api\Internal\Other\ExportService;

use App\Enums\Api\Internal\TypeProductEnum;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use SimpleXMLElement;

class Export1CService
{
    use HasOrderedUuid;

    protected array $temp;
    protected int $i;

    public function arrayToXml($data, &$xmlData, $elementName = null)
    {
        foreach ($data as $key => $value) {


            if ($key === '@attributes' && is_array($value)) {
                // Добавляем атрибуты в текущий XML элемент
                foreach ($value as $attrName => $attrValue) {
                    $xmlData->addAttribute($attrName, $attrValue);
                }
            } elseif (is_array($value)) {

                // Если массив индексированный с числовыми ключами
                if (array_keys($value) === range(0, count($value) - 1)) {

                    // dd(array_keys($value), $key, $value, $xmlData->getName());
                    foreach ($value as $subValue) {

                        $elementName = $key;
                        // Используем переданное имя элемента или ключ по умолчанию
                        $subnode = $xmlData->addChild($elementName ?? $xmlData->getName());
                        $this->arrayToXml($subValue, $subnode, $elementName);
                    }
                } else {
                    // Создаем тег с именем ключа
                    $subnode = $xmlData->addChild($key);
                    $this->arrayToXml($value, $subnode, $key);
                }

            } else {
                $xmlData->addChild("$key", htmlspecialchars("$value"));
            }
        }
    }


    public function dataXml(string $cabinetId)
    {

        $attr = ["@attributes" =>
                    [
                        "ВерсияФормата" => "1.0",
                        "ДатаВыгрузки" => now(),
                        "ИдПравилКонвертации" => "-",
                        "ИмяКонфигурацииИсточника" => "УправлениеТорговлей",
                        "ИмяКонфигурацииПриемника" => "БухгалтерияПредприятия",
                        "Комментарий" => "Выгрузка данных из log.ru"
                    ]
                ];

        // 1. Загрузите JSON из файла в storage
        $jsonParams =                       Storage::get('1c/params.json');     // ПравилаОбмена
        $jsonparameters1 =                  Storage::get('1c/params1.json');    // Параметры
        $jsonProcessing2 =                  Storage::get('1c/params2.json');    // Обработки
        $jsonRulesForConvertingObjects3 =   Storage::get('1c/params3.json');    // ПравилаКонвертацииОбъектов
        $jsonDataCleaningRules4 =           Storage::get('1c/params4.json');    // ПравилаОчисткиДанных
        $jsonAlgorithms5 =                  Storage::get('1c/params5.json');    // Алгоритмы
        $jsonRequests6 =                    Storage::get('1c/params6.json');    // Запросы

        // 2. Преобразуем JSON в массив
        $params =                       json_decode($jsonParams, true);
        $parameters1 =                  json_decode($jsonparameters1, true);
        $processing2 =                  json_decode($jsonProcessing2, true);
        $rulesForConvertingObjects3 =   json_decode($jsonRulesForConvertingObjects3, true);
        $dataCleaningRules4 =           json_decode($jsonDataCleaningRules4, true);
        $algorithms5 =                  json_decode($jsonAlgorithms5, true);
        $requests6 =                    json_decode($jsonRequests6, true);

        // 3. Создаем новый объект SimpleXMLElement
        $xmlData = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><ФайлОбмена></ФайлОбмена>');

        // 4. Преобразуем массив в XML
        $this->arrayToXml($attr, $xmlData); // Добавляем аттрибуты

        $rules = $xmlData->addChild('ПравилаОбмена');
        $this->arrayToXml($params, $rules);

        $parameters = $rules->addChild('Параметры');
        $this->arrayToXml($parameters1, $parameters);

        $processing = $rules->addChild('Обработки');
        $this->arrayToXml($processing2, $processing);

        $rulesForConvertingObjects = $rules->addChild('ПравилаКонвертацииОбъектов');
        $this->arrayToXml($rulesForConvertingObjects3, $rulesForConvertingObjects, 'Правило');

        $dataCleaningRules = $rules->addChild('ПравилаОчисткиДанных');
        $this->arrayToXml($dataCleaningRules4, $dataCleaningRules, 'Правило');

        $algorithms = $rules->addChild('Алгоритмы');
        $this->arrayToXml($algorithms5, $algorithms, 'Алгоритм');

        $requests = $rules->addChild('Запросы');
        $this->arrayToXml($requests6, $requests, 'Запрос');


        $getLegalEntities = $this->getLegalEntities($cabinetId);
        // dd($getLegalEntities, $this->temp); // Enum Физлица Юрлица legal


        // РеализацияТоваровУслуг

        // ПоступлениеТоваровУслуг
        // СчетФактураПолученный
        // СчетФактураВыданный
        // СчетНаОплатуПокупателю
        // ПоступлениеНаРасчетныйСчет
        // СписаниеСРасчетногоСчета




        $getBankAccounts = $this->getBankAccounts($cabinetId);

        foreach ($getBankAccounts as $object) {

            foreach ($this->temp as $temp) {

                $newTemp = $temp;
                // dump($object['id'], $temp['id']);
                if ($newTemp['id'] == $object['id'] && $newTemp['@attributes']['Имя'] == 'Организации' && $object['@attributes']['ИмяПравила'] == 'БанковскиеСчета') {
                    unset($newTemp['id']);
                    $newTemp['@attributes']['Имя'] = 'Владелец';
                    array_unshift($object['Свойство'], $newTemp);
                    // dd($object['Свойство']);

                }
            }
            unset($object['id']);
            $rules13 = $rules->addChild('Объект');
            $this->arrayToXml($object, $rules13, 'Свойство');
        }

        // Организации
        foreach ($getLegalEntities as $objectOrg) {
            // foreach ($getLegalEntities as $object) {
            foreach ($this->temp as $temp) {

                $newTemp = $temp;

                if ($newTemp['id'] == $objectOrg['id'] && $newTemp['@attributes']['Имя'] == 'ОсновнойБанковскийСчет') {
                    unset($newTemp['id']);
                    array_push($objectOrg['Свойство'], $newTemp);
                }
            }
            unset($objectOrg['id']);

            $rules14 = $rules->addChild('Объект');
            $this->arrayToXml($objectOrg, $rules14, 'Свойство');

            // }

            $getCurrencies = $this->getCurrencies($cabinetId);

            foreach ($getCurrencies as $object) {

                unset($object['id']);
                $rules7 = $rules->addChild('Объект');
                $this->arrayToXml($object, $rules7, 'Свойство');
            }



            $getShipments = $this->getShipments($cabinetId);

            foreach ($getShipments as $k => $object) {

                foreach ($object['Свойство'] as $j => $obj) {

                    foreach ($this->temp as $temp) {

                        $newTemp = $temp;
                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($obj, $newTemp);
                        }

                        if (isset($obj['contractor_id']) && $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты' && $obj['@attributes']['Имя'] == 'Контрагент') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j]['Ссылка'], $obj, $newTemp);
                        }

                        if (isset($obj['contractor_id']) &&  $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Имя'] == $obj['@attributes']['Имя']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        // if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'КратностьВзаиморасчетов' && $newTemp['@attributes']['Имя'] == 'true' && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Валюты') {
                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'КратностьВзаиморасчетов') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($object['Свойство'][$j]['Ссылка']);
                            unset($newTemp['id']);
                            unset($newTemp['Ссылка']);
                            $object['Свойство'][$j]['Значение'] = $newTemp['value'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'КурсВзаиморасчетов') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($object['Свойство'][$j]['Ссылка']);
                            unset($newTemp['id']);
                            unset($newTemp['Ссылка']);
                            $object['Свойство'][$j]['Значение'] = $newTemp['value'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['legal_entity_id']) && $obj['legal_entity_id'] == $temp['id'] && $obj['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['warehouse_id']) && $obj['warehouse_id'] == $temp['id'] && $obj['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                    }

                    if (isset($obj['Значение']) && $obj['Значение'] !== null) {
                        // dd($obj['Значение']);
                        unset($object['Свойство'][$j]['Пусто']);
                    }

                    unset($object['Свойство'][$j]['currency_id']);
                    unset($object['Свойство'][$j]['contractor_id']);
                    unset($object['Свойство'][$j]['legal_entity_id']);
                    unset($object['Свойство'][$j]['warehouse_id']);
                }

                unset($object['id']);

                $rulesShipments = $rules->addChild('Объект');
                $this->arrayToXml($object, $rulesShipments, 'Свойство');
            }





            $getMeasurementProducts = $this->getMeasurementProducts($cabinetId);
            // dd($getMeasurementProducts);
            foreach ($getMeasurementProducts as $object) {
                $rulesMeasurementProducts = $rules->addChild('Объект');
                $this->arrayToXml($object, $rulesMeasurementProducts, 'Свойство');
            }

            $getWarehouses = $this->getWarehouses($cabinetId);
            // dd($getWarehouses);
            foreach ($getWarehouses as $object) {

                $rulesWarehouses = $rules->addChild('Объект');
                $this->arrayToXml($object, $rulesWarehouses, 'Свойство');
            }


            $getAccGtd = $this->getAccGtd($cabinetId);
            // dd($getAccGtd);
            foreach ($getAccGtd as $object) {
                $rulesAccGtd = $rules->addChild('Объект');
                $this->arrayToXml($object, $rulesAccGtd, 'Свойство');
            }


            $getСontractors = $this->getСontractors($cabinetId);

            foreach ($getСontractors as $object) {

                foreach ($this->temp as $temp) {

                    $newTemp = $temp;

                    if ($newTemp['id'] == $object['id'] && $newTemp['Ссылка']['@attributes']['Нпп'] == $object['@attributes']['Нпп']) {

                        $newTemp['@attributes']['Имя'] = 'ГоловнойКонтрагент';
                        unset($newTemp['id']);
                        array_push($object['Свойство'], $newTemp);

                    }

                    $newTemp1 = $temp;

                    if ($newTemp1['id'] == $object['id'] && $newTemp1['@attributes']['Имя'] == 'Контрагенты' && $object['@attributes']['ИмяПравила'] == 'КонтактнаяИнформация') {

                        $newTemp1['@attributes']['Имя'] = 'Объект';
                        unset($newTemp1['id']);
                        array_unshift($object['Свойство'], $newTemp1);

                    }
                }
                unset($object['id']);

                $rules10 = $rules->addChild('Объект');
                $this->arrayToXml($object, $rules10, 'Свойство');
            }

            $getСontracts = $this->getСontracts($cabinetId);
            // dd($getСontracts, $this->temp);


            foreach ($this->temp as $k => $temp) {
                foreach ($this->temp as $NewTemp) {

                    $newTemp = $NewTemp;

                    if (isset($newTemp['id']) && $temp['id'] == $newTemp['id'] && $temp['@attributes']['Имя'] == 'ДоговорКонтрагента' &&  $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты') {
                        $newTemp['@attributes']['Имя'] = 'Владелец';
                        unset($newTemp['id']);
                        array_push($this->temp[$k]['Ссылка']['Свойство'], $newTemp);
                    }

                    if ($temp['@attributes']['Имя'] == 'ДоговорКонтрагента' && $newTemp['@attributes']['Имя'] == 'true' && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Валюты') {
                        $newTemp['@attributes']['Имя'] = 'ВалютаВзаиморасчетов';
                        unset($newTemp['id']);
                        unset($newTemp['value']);
                        array_push($this->temp[$k]['Ссылка']['Свойство'], $newTemp);
                    }

                    if ($temp['@attributes']['Имя'] == 'ДоговорКонтрагента' && $newTemp['@attributes']['Имя'] == 'Организации' && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Организации') {
                        $newTemp['@attributes']['Имя'] = 'Организация';
                        unset($newTemp['id']);
                        array_push($this->temp[$k]['Ссылка']['Свойство'], $newTemp);
                    }
                }
            }


            foreach ($getСontracts as $k =>  $object) {

                foreach ($this->temp as $temp) {

                    $newTemp = $temp;

                    if ($object['id'] == $newTemp['id'] && $temp['@attributes']['Имя'] == 'ДоговорКонтрагента') {
                        unset($newTemp['id']);
                        $object['Ссылка']['Свойство'] = $newTemp['Ссылка']['Свойство'];
                    }

                }
                unset($object['id']);

                $contracts = $rules->addChild('Объект');
                $this->arrayToXml($object, $contracts, 'Свойство');
            }

            $getProducts = $this->getProducts($cabinetId);
            // dd($getProducts);
            foreach ($getProducts as $object) {

                foreach ($object['Свойство'] as $j => $obj) {

                    foreach ($this->temp as $temp) {

                        $newTemp = $temp;

                        if (isset($obj['measurement_unit_id']) && $obj['measurement_unit_id'] == $temp['id'] && $temp['@attributes']['Имя'] == 'БазоваяЕдиницаИзмерения') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];

                        }

                    }

                    unset($object['Свойство'][$j]['measurement_unit_id']);
                }
                // dd($object);
                unset($object['id']);

                $products = $rules->addChild('Объект');
                $this->arrayToXml($object, $products, 'Свойство');
            }


            $getAcceptances = $this->getAcceptances($cabinetId);
            // dd($getAcceptances, $this->temp);
            foreach ($getAcceptances as $k => $object) {

                foreach ($object['Свойство'] as $j => $obj) {

                    foreach ($this->temp as $temp) {

                        $newTemp = $temp;
                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($obj, $newTemp);
                        }

                        if (isset($obj['contractor_id']) && $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты' && $obj['@attributes']['Имя'] == 'Контрагент') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j]['Ссылка'], $obj, $newTemp);
                        }

                        if (isset($obj['contractor_id']) &&  $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Имя'] == $obj['@attributes']['Имя']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        // if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'КратностьВзаиморасчетов' && $newTemp['@attributes']['Имя'] == 'true' && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Валюты') {
                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'КратностьВзаиморасчетов') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($object['Свойство'][$j]['Ссылка']);
                            unset($newTemp['id']);
                            unset($newTemp['Ссылка']);
                            $object['Свойство'][$j]['Значение'] = $newTemp['value'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'КурсВзаиморасчетов') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($object['Свойство'][$j]['Ссылка']);
                            unset($newTemp['id']);
                            unset($newTemp['Ссылка']);
                            $object['Свойство'][$j]['Значение'] = $newTemp['value'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['legal_entity_id']) && $obj['legal_entity_id'] == $temp['id'] && $obj['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['warehouse_id']) && $obj['warehouse_id'] == $temp['id'] && $obj['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                    }

                    if (isset($obj['Значение']) && $obj['Значение'] !== null) {
                        // dd($obj['Значение']);
                        unset($object['Свойство'][$j]['Пусто']);
                    }

                    unset($object['Свойство'][$j]['currency_id']);
                    unset($object['Свойство'][$j]['contractor_id']);
                    unset($object['Свойство'][$j]['legal_entity_id']);
                    unset($object['Свойство'][$j]['warehouse_id']);
                }

                unset($object['id']);
                $acceptances = $rules->addChild('Объект');
                $this->arrayToXml($object, $acceptances, 'Свойство');

            }


            $getIncomingPayments = $this->getIncomingPayments($cabinetId);
            // dd($getIncomingPayments, $this->temp);
            foreach ($getIncomingPayments as $k => $object) {

                foreach ($object['ТабличнаяЧасть']['Запись']['Свойство'] as $ij => $obj) {

                    foreach ($this->temp as $temp) {

                        $newTemp1 = $temp;

                        if (isset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id']) && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id'] == $temp['id'] && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['@attributes']['Имя'] == 'КратностьВзаиморасчетов') {
                            unset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Пусто']);
                            // unset($object['Свойство'][$j]['Ссылка']);
                            unset($newTemp1['id']);
                            // unset($newTemp1['Ссылка']);
                            $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Значение'] = $newTemp1['value'];
                            // dd($object, $obj, $newTemp1);
                        }

                        if (isset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id']) && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id'] == $temp['id'] && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['@attributes']['Имя'] == 'КурсВзаиморасчетов') {
                            unset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Пусто']);
                            // unset($object['Свойство'][$j]['Ссылка']);
                            unset($newTemp1['id']);
                            // unset($newTemp['Ссылка']);
                            $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Значение'] = $newTemp1['value'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                    }

                    unset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id']);

                }

                foreach ($object['Свойство'] as $j => $obj) {
                    // dd($j, $obj);
                    foreach ($this->temp as $temp) {

                        $newTemp = $temp;
                        // if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id']) {
                        //     unset($object['Свойство'][$j]['Пусто']);
                        //     unset($newTemp['id']);
                        //     $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                        //     // dd($obj, $newTemp);
                        // }

                        if (isset($obj['contractor_id']) && $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты' && $obj['@attributes']['Имя'] == 'Контрагент') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j]['Ссылка'], $obj, $newTemp);
                        }

                        if (isset($obj['contractor_id']) &&  $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Имя'] == $obj['@attributes']['Имя']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'ВалютаДокумента') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Значение'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['legal_entity_id']) && $obj['legal_entity_id'] == $temp['id'] && $obj['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                    }

                    if (isset($obj['Значение']) && $obj['Значение'] !== null) {
                        unset($object['Свойство'][$j]['Пусто']);
                    }

                    unset($object['Свойство'][$j]['currency_id']);
                    unset($object['Свойство'][$j]['contractor_id']);
                    unset($object['Свойство'][$j]['legal_entity_id']);
                }

                unset($object['id']);


                $rulesIncomingPayments = $rules->addChild('Объект');
                $this->arrayToXml($object, $rulesIncomingPayments, 'Свойство');
            }


            $getOutgoingPayments = $this->getOutgoingPayments($cabinetId);
            // dd($getOutgoingPayments, $this->temp);
            foreach ($getOutgoingPayments as $k => $object) {

                foreach ($object['ТабличнаяЧасть']['Запись']['Свойство'] as $ij => $obj) {

                    foreach ($this->temp as $temp) {

                        $newTemp1 = $temp;

                        if (isset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id']) && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id'] == $temp['id'] && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['@attributes']['Имя'] == 'КратностьВзаиморасчетов') {
                            unset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Пусто']);
                            unset($newTemp1['id']);
                            $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Значение'] = $newTemp1['value'];
                        }

                        if (isset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id']) && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id'] == $temp['id'] && $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['@attributes']['Имя'] == 'КурсВзаиморасчетов') {
                            unset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Пусто']);
                            unset($newTemp1['id']);
                            $object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['Значение'] = $newTemp1['value'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                    }

                    unset($object['ТабличнаяЧасть']['Запись']['Свойство'][$ij]['currency_id']);

                }

                foreach ($object['Свойство'] as $j => $obj) {

                    foreach ($this->temp as $temp) {

                        $newTemp = $temp;

                        if (isset($obj['contractor_id']) && $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Тип'] == 'СправочникСсылка.Контрагенты' && $obj['@attributes']['Имя'] == 'Контрагент') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j]['Ссылка'], $obj, $newTemp);
                        }

                        if (isset($obj['contractor_id']) &&  $obj['contractor_id'] == $temp['id'] && $newTemp['@attributes']['Имя'] == $obj['@attributes']['Имя']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['currency_id']) && $obj['currency_id'] == $temp['id'] && $obj['@attributes']['Имя'] == 'ВалютаДокумента') {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Значение'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                        if (isset($obj['legal_entity_id']) && $obj['legal_entity_id'] == $temp['id'] && $obj['@attributes']['Тип'] == $temp['@attributes']['Тип']) {
                            unset($object['Свойство'][$j]['Пусто']);
                            unset($newTemp['id']);
                            $object['Свойство'][$j]['Ссылка'] = $newTemp['Ссылка'];
                            // dd($object['Свойство'][$j], $obj, $newTemp);
                        }

                    }

                    if (isset($obj['Значение']) && $obj['Значение'] !== null) {
                        unset($object['Свойство'][$j]['Пусто']);
                    }

                    unset($object['Свойство'][$j]['currency_id']);
                    unset($object['Свойство'][$j]['contractor_id']);
                    unset($object['Свойство'][$j]['legal_entity_id']);
                }

                unset($object['id']);

                $rulesOutgoingPayments = $rules->addChild('Объект');
                $this->arrayToXml($object, $rulesOutgoingPayments, 'Свойство');
            }




        }

        // $getCountries = $this->getCountries();
        // // dd($getCountries);
        // foreach ($getCountries as $object) {
        //     $rules8 = $rules->addChild('Объект');
        //     $this->arrayToXml($object, $rules8, 'Свойство');
        // }



        // $getMeasurements = $this->getMeasurements();
        // // dd($getMeasurements);
        // foreach ($getMeasurements as $object) {
        //     $rules9 = $rules->addChild('Объект');
        //     $this->arrayToXml($object, $rules9, 'Свойство');
        // }

        $xmlContent = $xmlData->asXML();
        Storage::put('1c/newyourfile.xml', $xmlContent);

    }



    // Организации Нпп="0"
    public function getLegalEntities(string $cabinetId)
    {

        $data = DB::table('legal_entities as le')
        ->select('le.id', 'le.short_name', 'ld.inn', 'ld.kpp', 'ld.ogrn', 'ld.okpo', 'ld.full_name', 'ld.type', 'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code')
        ->leftJoin('legal_accounts as la', function ($join) {
            $join->on('le.id', '=', 'la.legal_entity_id')
                ->where('la.is_main', '=', true);
        })
        ->leftjoin('legal_details as ld', 'le.id', '=', 'ld.legal_entity_id')
        ->leftJoin('cabinet_currencies as cc', function ($join) {
            $join->on('le.cabinet_id', '=', 'cc.cabinet_id')
                ->where('cc.is_active', '=', true);
        })
        ->leftjoin('global_currencies as gc', 'cc.currency_id', '=', 'gc.id')
        ->groupBy(['le.id', 'le.short_name', 'ld.inn', 'ld.kpp', 'ld.ogrn', 'ld.okpo', 'ld.full_name', 'ld.type', 'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code'])
        ->where('le.cabinet_id', $cabinetId)->get();
        // dd($data);
        $result = [];

        $this->i = 0;
        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'Организации',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Организации'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Наименование',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->short_name
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'ИНН',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->inn
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'КПП',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->kpp
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'КодПоОКПО',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->okpo
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'НаименованиеПолное',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->full_name
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'ЭтоГруппа',
                                'Тип' => 'Булево'
                            ],
                            'Значение' => 'false??'
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'ЮрФизЛицо',
                            'Тип' => 'ПеречислениеСсылка.ЮрФизЛицо'
                        ],
                        'Значение' => $item->type
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ОГРН',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->ogrn
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false????'
                    ]
                ],
            ];

            $this->temp[] = [
                    'id' => $item->id,
                    '@attributes' => [
                        'Имя' => 'Организации',
                        'Тип' => 'СправочникСсылка.Организации'
                    ],
                    'Ссылка' => [
                        '@attributes' => [
                            'Нпп' => $i
                        ],
                        'Свойство' => [
                            [
                                '@attributes' => [
                                    'Имя' => 'Наименование',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->short_name
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'ИНН',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->inn
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'КПП',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->kpp
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'КодПоОКПО',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->okpo
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'НаименованиеПолное',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->full_name
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'ЭтоГруппа',
                                    'Тип' => 'Булево'
                                ],
                                'Значение' => 'false??'
                            ]
                        ]
                    ]

            ];

            $i++;
        }

        $this->i = $i;

        return $result;
    }


    public function getBankAccounts(string $cabinetId)
    {

        $data = DB::table('legal_entities as le')
        ->select('le.id', 'le.short_name', 'la.is_main', 'ld.inn', 'ld.kpp', 'ld.okpo', 'ld.full_name', 'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code')
        ->leftjoin('legal_accounts as la', 'le.id', '=', 'la.legal_entity_id')
        ->leftjoin('legal_details as ld', 'le.id', '=', 'ld.legal_entity_id')
        ->leftJoin('cabinet_currencies as cc', function ($join) {
            $join->on('le.cabinet_id', '=', 'cc.cabinet_id')
                ->where('cc.is_active', '=', true);
        })
        ->leftjoin('global_currencies as gc', 'cc.currency_id', '=', 'gc.id')
        ->groupBy(['le.id', 'le.short_name','la.is_main', 'ld.inn', 'ld.kpp', 'ld.okpo', 'ld.full_name',  'la.bik', 'la.bank', 'la.correspondent_account', 'la.payment_account', 'la.address', 'gc.num_code'])
        ->where('le.cabinet_id', $cabinetId)->get();
        // dd($data);
        $result = [];

        $i = $this->i;
        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'Банки',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Банки'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->bik
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'Наименование',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->bank
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'КоррСчет',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->correspondent_account
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Адрес',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->address
                    ]
                ],
            ];

            $this->temp[] = [
                'id' => $item->id,
                '@attributes' => [
                    'Имя' => 'Банки',
                    'Тип' => 'СправочникСсылка.Банки'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->bik
                        ]
                    ]
                ]

            ];

            $i++;

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'БанковскиеСчета',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.БанковскиеСчета'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'НомерСчета',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->payment_account
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'Наименование',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->bank
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Банк',
                            'Тип' => 'СправочникСсылка.Банки'
                        ],
                        'Ссылка' => [
                            '@attributes' => [
                                'Нпп' => $i
                            ],
                            'Свойство' => [
                                [
                                    '@attributes' => [
                                        'Имя' => 'Код',
                                        'Тип' => 'Строка'
                                    ],
                                    'Значение' => $item->bik
                                ]
                            ]
                        ],
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВидСчета',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => 'Расчетный????'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВалютаДенежныхСредств',
                            'Тип' => 'СправочникСсылка.Валюты'
                        ],
                        'Ссылка' => [
                            '@attributes' => [
                                'Нпп' => $i
                            ],
                            'Свойство' => [
                                [
                                    '@attributes' => [
                                        'Имя' => 'Код',
                                        'Тип' => 'Строка'
                                    ],
                                    'Значение' => $item->num_code
                                ]
                            ]
                        ],
                    ]
                ],
            ];


            $this->temp[] = [
                'id' => $item->id,
                '@attributes' => [
                    'Имя' => (!$item->is_main) ? 'БанковскиеСчета' : 'ОсновнойБанковскийСчет',
                    'Тип' => 'СправочникСсылка.БанковскиеСчета'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'НомерСчета',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->payment_account
                        ]
                    ]
                ]

            ];

            $i++;
        }

        $this->i = $i;
        // dd($result);
        return $result;
    }


    // Валюты Нпп="1"
    public function getCurrencies(string $cabinetId)
    {

        // $data = DB::table('acceptances as ac')
        // ->select('*')
        // ->where('ac.cabinet_id', $cabinetId)->get();

        // dd($data);

        $data = DB::table('cabinet_currencies as ccu')
        ->select('ccu.id', 'ccu.currency_id', 'gc.num_code', 'gc.char_code', 'ccu.is_active', 'gc.external_id', 'gc.value')
        ->leftjoin('global_currencies as gc', 'gc.id', '=', 'ccu.currency_id')
        ->groupBy(['ccu.id', 'gc.num_code', 'gc.char_code', 'gc.external_id', 'gc.value'])
        ->where('ccu.cabinet_id', $cabinetId)->get();
        // dd($data);

        // Преобразование данных в нужный формат
        $result = [];

        $i = $this->i;

        foreach ($data as $item) {
            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'Валюты',
                    'Нпп' =>  $i,
                    'Тип' => 'СправочникСсылка.Валюты'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        '@attributes' => [
                            'Имя' => 'Код',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->num_code
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => ($item->is_active) ? 'false' : 'true'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Наименование',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->char_code
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'НаименованиеПолное',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->char_code
                    ]
                ]
            ];


            $this->temp[] = [
                'id' => $item->currency_id,
                'value' => $item->value,
                '@attributes' => [
                    'Имя' => ($item->is_active) ? 'false' : 'true',
                    'Тип' => 'СправочникСсылка.Валюты'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        '@attributes' => [
                            'Имя' => 'Код',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->num_code
                    ]
                ]
            ];

            $i++;
        }

        $this->i = $i;

        return $result;

    }

    // ПриходныйКассовыйОрдер Нпп="38" 00001

    // РасходныйКассовыйОрдер Нпп="41" 00001

    // НомерГТД Нпп="18" Нпп="19" второй продукт?

    // Контрагенты Нпп="6"
    public function getСontractors(string $cabinetId)
    {
        $data = DB::table('contractors as c')
            ->select('c.id', 'c.title', 'c.tel', 'c.fax', 'c.email', 'cd.inn', 'cd.kpp', 'cd.ogrn', 'cd.full_name', 'cd.type', 'c.deleted_at')
            ->leftjoin('contractors_details as cd', 'c.id', '=', 'cd.contractor_id')
            ->groupBy(['c.id', 'cd.inn', 'cd.kpp', 'cd.ogrn', 'cd.full_name', 'cd.type', 'c.deleted_at'])
            ->where('cabinet_id', $cabinetId)->get();

        $result = [];
        $temp = [];
        $i = $this->i;

        foreach ($data as $k => $item) {
            $contractor = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'Контрагенты',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Контрагенты'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => $this->getContractorProperties($item)
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'ЮрФизЛицо',
                            'Тип' => 'ПеречислениеСсылка.ЮрФизЛицо'
                        ],
                        'Значение' => $item->type
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false',
                    ]
                ]
            ];

            $result[] = $contractor;
            $tempcontractor = $contractor;
            $tempcontractor['@attributes']['Имя'] = 'Контрагенты';
            unset($tempcontractor['@attributes']['ИмяПравила']);
            $this->temp[] = $tempcontractor;

            // dd($tempcontractor);

            $i++;

            $contactInfoTypes = [
                ['Тип' => 'Телефон', 'Вид' => 'Телефон', 'Поле' => 'Поле3', 'Значение' => $item->tel],
                ['Тип' => 'Телефон', 'Вид' => 'Факс', 'Поле' => 'Поле3', 'Значение' => $item->fax],
                ['Тип' => 'АдресЭлектроннойПочты', 'Вид' => 'Email', 'Поле' => 'Поле1', 'Значение' => $item->email]
            ];

            foreach ($contactInfoTypes as $contactInfoType) {
                $contactInfo = [
                    'id' => $item->id,
                    '@attributes' => [
                        'ИмяПравила' => 'КонтактнаяИнформация',
                        'Нпп' => $i,
                        'Тип' => 'РегистрСведенийЗапись.КонтактнаяИнформация'
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Тип',
                                'Тип' => 'ПеречислениеСсылка.ТипыКонтактнойИнформации'
                            ],
                            'Значение' => $contactInfoType['Тип']
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Вид',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $contactInfoType['Вид']
                        ],
                        [
                            '@attributes' => [
                                'Имя' => $contactInfoType['Поле'],
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $contactInfoType['Значение']
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Представление',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $contactInfoType['Значение']
                        ]
                    ]
                ];

                $result[] = $contactInfo;
                $i++;
            }

        }

        $this->i = $i;


        return $result;
    }

    private function getContractorProperties($item)
    {
        return [
            [
                '@attributes' => [
                    'Имя' => 'Наименование',
                    'Тип' => 'Строка'
                ],
                'Значение' => $item->title
            ],
            [
                '@attributes' => [
                    'Имя' => 'ИНН',
                    'Тип' => 'Строка'
                ],
                'Значение' => $item->inn
            ],
            [
                '@attributes' => [
                    'Имя' => 'КПП',
                    'Тип' => 'Строка'
                ],
                'Значение' => $item->kpp
            ],
            [
                '@attributes' => [
                    'Имя' => 'КодПоОКПО',
                    'Тип' => 'Строка'
                ],
                'Значение' => $item->ogrn
            ],
            [
                '@attributes' => [
                    'Имя' => 'НаименованиеПолное',
                    'Тип' => 'Строка'
                ],
                'Значение' => $item->full_name
            ],
            [
                '@attributes' => [
                    'Имя' => 'ЭтоГруппа',
                    'Тип' => 'Булево'
                ],
                'Значение' => 'false'
            ]
        ];
    }

    // ДоговорыКонтрагентов Нпп="5"
    public function getСontracts(string $cabinetId)
    {

        $data = DB::table('contractors as c')
        ->select('c.id', 'cf.title', 'c.deleted_at')
        ->leftjoin('contractors_files as cf', 'c.id', '=', 'cf.contractor_id')
        ->groupBy(['c.id'], 'cf.title')
        ->where('cabinet_id', $cabinetId)->get();
        // dd($data);

        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            if ($item->title !== null) {

                $result[] = [
                    'id' => $item->id,
                    '@attributes' => [
                        'ИмяПравила' => 'ДоговорыКонтрагентов',
                        'Нпп' =>  $i,
                        'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                    ],
                    'Ссылка' => [
                        '@attributes' => [
                            'Нпп' =>  $i
                        ],
                        'Свойство' => [
                            [
                                '@attributes' => [
                                    'Имя' => 'Наименование',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->title
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'ЭтоГруппа',
                                    'Тип' => 'Булево'
                                ],
                                'Значение' => 'false??'
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'ВидДоговора',
                                    'Тип' => 'ПеречислениеСсылка.ВидыДоговоровКонтрагентов'
                                ],
                                'Значение' => 'СПокупателем'
                            ]
                        ]
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'ПометкаУдаления',
                                'Тип' => 'Булево'
                            ],
                            'Значение' => ($item->deleted_at) ? 'false' : 'true'
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Комментарий',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => 'Комментарий ???'
                        ]
                    ]
                ];


                $this->temp[] = [
                    'id' => $item->id,
                    '@attributes' => [
                        'Имя' => 'ДоговорКонтрагента',
                        'Нпп' =>  $i,
                        'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                    ],
                    'Ссылка' => [
                        '@attributes' => [
                            'Нпп' =>  $i
                        ],
                        'Свойство' => [
                            [
                                '@attributes' => [
                                    'Имя' => 'Наименование',
                                    'Тип' => 'Строка'
                                ],
                                'Значение' => $item->title
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'ЭтоГруппа',
                                    'Тип' => 'Булево'
                                ],
                                'Значение' => 'false??'
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'ВидДоговора',
                                    'Тип' => 'ПеречислениеСсылка.ВидыДоговоровКонтрагентов'
                                ],
                                'Значение' => 'СПокупателем'
                            ]
                        ]
                    ]
                ];

                $i++;

            }
        }

        $this->i = $i;

        return $result;

    }

    public function getAcceptances(string $cabinetId)
    {

        $data = DB::table('acceptances as ac')
        ->select('*')
        ->where('ac.cabinet_id', $cabinetId)->get();
        // dd($data);

        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'ПоступлениеТоваровУслуг',
                    'Нпп' =>  $i,
                    'Тип' => 'ДокументСсылка.ПоступлениеТоваровУслуг'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Номер',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->number
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Дата',
                                'Тип' => 'Дата'
                            ],
                            'Значение' => $item->date_from
                        ]
                    ]
                ],
                'ТабличнаяЧасть' => $this->getAccItem($item->id),
                'Свойство' => [
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'ВалютаДокумента',
                            'Тип' => 'СправочникСсылка.Валюты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комментарий',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->comment,
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'НомерВходящегоДокумента',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->incoming_number,
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ДатаВходящегоДокумента',
                            'Тип' => 'Дата'
                        ],
                        'Значение' => $item->incoming_date,
                        'Пусто' => ''
                    ],
                    [
                        'contractor_id' => $item->contractor_id,
                        '@attributes' => [
                            'Имя' => 'Контрагент',
                            'Тип' => 'СправочникСсылка.Контрагенты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'contractor_id' => $item->contractor_id,
                        '@attributes' => [
                            'Имя' => 'ДоговорКонтрагента',
                            'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'КратностьВзаиморасчетов',
                            'Тип' => 'Число'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'КурсВзаиморасчетов',
                            'Тип' => 'Число'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'НДСВключенВСтоимость',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => ($item->price_includes_vat) ? 'true' : 'false'
                    ],
                    [
                        'legal_entity_id' => $item->legal_entity_id,
                        '@attributes' => [
                            'Имя' => 'Организация',
                            'Тип' => 'СправочникСсылка.Организации'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'РегистрироватьЦеныПоставщика',
                            'Тип' => 'Булево'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'warehouse_id' => $item->warehouse_id,
                        '@attributes' => [
                            'Имя' => 'Склад',
                            'Тип' => 'СправочникСсылка.Склады'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СуммаВключаетНДС',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => ($item->price_includes_vat) ? 'true' : 'false'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СуммаДокумента',
                            'Тип' => 'Число'
                        ],
                        'Значение' => round((int)$item->total_price / 100, 2),
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'УчитыватьНДС',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => ($item->price_includes_vat) ? 'true Перепроверить!' : 'false'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВидОперации',
                            'Тип' => 'ПеречислениеСсылка.ВидыОперацийПоступлениеТоваровУслуг'
                        ],
                        'Значение' => 'ПокупкаКомиссия'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' =>  'Булево'
                        ],
                        'Значение' => 'false Доделать ' // ($item->deleted_at) ? 'false' : 'true'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СпособЗачетаАвансов',
                            'Тип' => 'ПеречислениеСсылка.СпособыЗачетаАвансов'
                        ],
                        'Значение' => 'НеЗачитывать'
                    ],
                ]
            ];

            $i++;

        }

        $this->i = $i;
        // dd($result);
        return $result;

    }

    public function getAccItem($accItemId)
    {

        $data = DB::table('acceptance_items as aci')
        ->select('aci.*', 'vr.rate', 'vr.description as vr_desc', 'c.name as country_name') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('vat_rates as vr', 'vr.id', '=', 'aci.vat_rate_id')
        ->leftjoin('countries as c', 'c.id', '=', 'aci.country_id')
        ->groupBy('aci.id', 'vr.id', 'c.id') //, 'vr.rate', 'vr.description'
        ->where('aci.acceptance_id', $accItemId)->get();
        // dd($data);

        $resItem = [];

        foreach ($data as $item) {

            if ($item->rate != 0) {
                $vat_rate = 'НДС'.$item->rate;
                $sum_rate =  [
                    '@attributes' => [
                        'Имя' => 'СуммаНДС',
                        'Тип' => 'Число'
                    ],
                    'Значение' => round(((int)$item->total_price * $item->rate / (100 + $item->rate)) / 100, 2)
                ];
            } else {
                $vat_rate = 'БезНДС';
                $sum_rate = [];
            }

            $resItem[] = [
                'product_id' => $item->product_id,
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'Количество',
                            'Тип' => 'Число'
                        ],
                        'Значение' => number_format((int)$item->quantity, 1, '.', '') // $item->quantity
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СтавкаНДС',
                            'Тип' => 'ПеречислениеСсылка.СтавкиНДС'
                        ],
                        'Значение' => $vat_rate
                    ],
                    $sum_rate,
                    [
                        '@attributes' => [
                            'Имя' => 'Сумма',
                            'Тип' => 'Число'
                        ],
                        'Значение' => round((int)$item->total_price / 100, 2)
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Цена',
                            'Тип' => 'Число'
                        ],
                        'Значение' => round((int)$item->price / 100, 2)
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комиссия',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false???'
                    ]
                ]
            ];

        }

        foreach ($resItem as $k => $object) {

            foreach ($this->temp as $temp) {

                $newTemp = $temp;
                if ($newTemp['id'] == $object['product_id']) {
                    unset($newTemp['id']);
                    // array_unshift($resItem[$k]['Свойство'], $newTemp);

                    $resItem[$k]['type'] = $newTemp['type'];
                    unset($newTemp['type']);
                    // Вставим новый массив в нужное место
                    $this->array_insert_after_key($resItem[$k]['Свойство'], 0, [$newTemp]);
                }
            }
            unset($resItem[$k]['product_id']);
        }

        $resItems = [
            [
                '@attributes' => [
                    'Имя' => 'Товары'
                ]
            ],
            [
                '@attributes' => [
                    'Имя' => 'Услуги'
                ]
            ]
        ];

        foreach ($resItem as $k => $object) {

            if ($object['type'] == TypeProductEnum::PRODUCT->value) {
                unset($object['type']);
                $resItems[0]['Запись'][] = $object;
            } elseif ($object['type'] == TypeProductEnum::SERVICE->value) {
                unset($object['type']);
                $resItems[1]['Запись'][] = $object;
            }
        }

        return $resItems;
    }


    public function getAccGtd(string $cabinetId)
    {

        $data = DB::table('acceptances as ac')
        ->select('aci.id', 'aci.gtd_number')
        ->leftjoin('acceptance_items as aci', 'ac.id', '=', 'aci.acceptance_id')
        ->groupBy('aci.id', 'aci.gtd_number')
        ->where('ac.cabinet_id', $cabinetId)->get();
        // dd($data);

        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                '@attributes' => [
                    'ИмяПравила' => 'НомераГТД',
                    'Тип' => 'СправочникСсылка.НомераГТД'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->gtd_number
                        ]
                    ]
                ]
            ];

            $this->temp[] = [
                'id' => $item->id,
                '@attributes' => [
                    'Имя' => 'НомераГТД',
                    'Тип' => 'СправочникСсылка.НомераГТД'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->gtd_number
                        ]
                    ]
                ]
            ];

            $i++;

        }

        $this->i = $i;

        return $result;
    }


    /**
     * Inserts an array after a specified key in another array.
     *
     * This function searches for the specified key in the provided array. If the key is found,
     * it inserts the given array immediately after that key. If the key is not found, the given
     * array is appended to the end of the original array.
     *
     * @param array   $array         The original array to modify.
     * @param mixed   $key           The key after which the new array should be inserted.
     * @param array   $insert_array  The array to insert into the original array.
     *
     * @return void
     */
    public function array_insert_after_key(& $array, $key, $insert_array)
    {

        $index = array_search($key, array_keys($array));

        // key is not found, add to the end of the array
        if ($index === false) {
            $array = array_merge($array, $insert_array);
        }
        // split the array into two parts and insert a new element between them
        else {
            $array = array_merge(
                array_slice($array, 0, $index + 1, true),
                $insert_array,
                array_slice($array, $index + 1, null, true)
            );
        }
    }

    // РеализацияТоваровУслуг
    public function getShipments(string $cabinetId)
    {

        $data = DB::table('shipments as s')
        ->select('*')
        ->where('s.cabinet_id', $cabinetId)->get();
        // dd($data);


        // id :                 идентификатор
        // created_at :         создан_ат
        // updated_at :         обновлен_ат
        // cabinet_id :         кабинета_и_дателя
        // employee_id :        сотрудника
        // number :             номер
        // date_from :          дата_от_наличия
        // status_id :          статуса
        // held :               держал
        // legal_entity_id :    юридический
        // contractor_id :      подрядчика
        // warehouse_id :       хранилища
        // sales_channel_id :   канал продаж
        // currency_id :        валюты
        // consignee_id :       получателя
        // transporter_id :     перевозчика
        // cargo_name :         имя груза
        // shipper_instructions : инструкции отправителя
        // venicle :             транспортное средство
        // venicle_number :      транспортный номер
        // total_seats :         общее количество мест
        // goverment_contract_id : правительства_контракта
        // comment :            комментарий
        // price_includes_vat : цена_включения_vat
        // overhead_cost :      сверхнормативная стоимость
        // total_cost :         общая стоимость
        // profit :             прибыль
        // total_price :        общая цена


        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'РеализацияТоваровУслуг',
                    'Нпп' =>  $i,
                    'Тип' => 'ДокументСсылка.РеализацияТоваровУслуг'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Номер',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->number
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Дата',
                                'Тип' => 'Дата'
                            ],
                            'Значение' => $item->date_from
                        ]
                    ]
                ],
                'ТабличнаяЧасть' => $this->getShipmentItem($item->id),
                'Свойство' => [
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'ВалютаДокумента',
                            'Тип' => 'СправочникСсылка.Валюты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комментарий',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->comment,
                        'Пусто' => ''
                    ],
                    [
                        'contractor_id' => $item->contractor_id,
                        '@attributes' => [
                            'Имя' => 'Контрагент',
                            'Тип' => 'СправочникСсылка.Контрагенты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'contractor_id' => $item->contractor_id,
                        '@attributes' => [
                            'Имя' => 'ДоговорКонтрагента',
                            'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'КратностьВзаиморасчетов',
                            'Тип' => 'Число'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'КурсВзаиморасчетов',
                            'Тип' => 'Число'
                        ],
                        'Пусто' => ''
                    ],
                    // [
                    //     '@attributes' => [
                    //         'Имя' => 'НДСВключенВСтоимость',
                    //         'Тип' => 'Булево'
                    //     ],
                    //     'Значение' => ($item->price_includes_vat) ? 'true' : 'false'
                    // ],
                    [
                        'legal_entity_id' => $item->legal_entity_id,
                        '@attributes' => [
                            'Имя' => 'Организация',
                            'Тип' => 'СправочникСсылка.Организации'
                        ],
                        'Пусто' => ''
                    ],
                    // [
                    //     '@attributes' => [
                    //         'Имя' => 'РегистрироватьЦеныПоставщика',
                    //         'Тип' => 'Булево'
                    //     ],
                    //     'Пусто' => ''
                    // ],
                    [
                        'warehouse_id' => $item->warehouse_id,
                        '@attributes' => [
                            'Имя' => 'Склад',
                            'Тип' => 'СправочникСсылка.Склады'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СуммаВключаетНДС',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => ($item->price_includes_vat) ? 'true' : 'false'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СуммаДокумента',
                            'Тип' => 'Число'
                        ],
                        'Значение' => round((int)$item->total_price / 100, 2),
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'УчитыватьНДС',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => ($item->price_includes_vat) ? 'true Перепроверить!' : 'false'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВидОперации',
                            'Тип' => 'ПеречислениеСсылка.ВидыОперацийПоступлениеТоваровУслуг'
                        ],
                        'Значение' => 'ПокупкаКомиссия'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' =>  'Булево'
                        ],
                        'Значение' => 'false Доделать ' // ($item->deleted_at) ? 'false' : 'true'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СпособЗачетаАвансов',
                            'Тип' => 'ПеречислениеСсылка.СпособыЗачетаАвансов'
                        ],
                        'Значение' => 'НеЗачитывать'
                    ],
                ]
            ];

            $i++;

        }

        $this->i = $i;
        // dd($result);
        return $result;

    }


    public function getShipmentItem($shipmentItemId)
    {

        $data = DB::table('shipment_items as shi')
        ->select('shi.*', 'vr.rate', 'vr.description as vr_desc') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('vat_rates as vr', 'vr.id', '=', 'shi.vat_rate_id')
        ->groupBy('shi.id', 'vr.id') //, 'vr.rate', 'vr.description'
        ->where('shi.shipment_id', $shipmentItemId)->get();
        // dd($data);

        // id :            идентификатор :
        // created_at :    создан_ат :
        // updated_at :    обновлен_ат :
        // shipment_id :   идентификатор_отправки :
        // product_id :    идентификатор_продукта :
        // quantity :      количество :
        // price :         цена :
        // cost :          стоимость :
        // total_cost :    общая стоимость :
        // total_price :   общая цена :
        // profit :        прибыль :
        // vat_rate_id :   ставка НДС :
        // discount :      скидка :
        // recidual :      остаточный :
        // rate :          ставка :
        // vr_desc :       виртуальный адрес :


        $resItem = [];

        foreach ($data as $item) {

            if ($item->rate != 0) {
                $vat_rate = 'НДС'.$item->rate;

                $sum_rate =  [
                    '@attributes' => [
                        'Имя' => 'СуммаНДС',
                        'Тип' => 'Число'
                    ],
                    'Значение' => round(((int)$item->total_price * $item->rate / (100 + $item->rate)) / 100, 2)
                ];

            } else {
                $vat_rate = 'БезНДС';

                $sum_rate = [];
            }

            $resItem[] = [
                'product_id' => $item->product_id,
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'Количество',
                            'Тип' => 'Число'
                        ],
                        'Значение' => number_format((int)$item->quantity, 1, '.', '') // $item->quantity
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'НомерГТД',
                            'Тип' => 'СправочникСсылка.НомераГТД'
                        ],
                        'Значение' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СтавкаНДС',
                            'Тип' => 'ПеречислениеСсылка.СтавкиНДС'
                        ],
                        'Значение' => $vat_rate
                    ],
                    $sum_rate,
                    [
                        '@attributes' => [
                            'Имя' => 'Сумма',
                            'Тип' => 'Число'
                        ],
                        'Значение' => round((int)$item->total_price / 100, 2)
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Цена',
                            'Тип' => 'Число'
                        ],
                        'Значение' => round((int)$item->price / 100, 2)
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комиссия',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false???'
                    ]
                ]
            ];

        }

        foreach ($resItem as $k => $object) {

            foreach ($this->temp as $temp) {

                $newTemp = $temp;
                if ($newTemp['id'] == $object['product_id']) {
                    unset($newTemp['id']);
                    // array_unshift($resItem[$k]['Свойство'], $newTemp);

                    $resItem[$k]['type'] = $newTemp['type'];
                    unset($newTemp['type']);
                    // Вставим новый массив в нужное место
                    $this->array_insert_after_key($resItem[$k]['Свойство'], 0, [$newTemp]);
                }
            }
            unset($resItem[$k]['product_id']);
        }

        $resItems = [
            [
                '@attributes' => [
                    'Имя' => 'Товары'
                ]
            ],
            [
                '@attributes' => [
                    'Имя' => 'Услуги'
                ]
            ]
        ];

        // dd($resItem);
        foreach ($resItem as $k => $object) {

            if ($object['type'] == TypeProductEnum::PRODUCT->value) {
                unset($object['type']);
                $resItems[0]['Запись'][] = $object;
            } elseif ($object['type'] == TypeProductEnum::SERVICE->value) {
                unset($object['type']);
                $resItems[1]['Запись'][] = $object;
            }
        }

        return $resItems;
    }


    public function getIncomingPayments(string $cabinetId)
    {

        $data = DB::table('incoming_payments as ip')
        ->select('*')
        ->where('ip.cabinet_id', $cabinetId)->get();
        // dd($data);

        // id: идентификатор:
        // created_at: создан_ат:
        // updated_at: обновлен_ат:
        // cabinet_id: идентификатор кабинета_ат:
        // employee_id: идентификатор сотрудника_ат:
        // number: номер:
        // date_from: данные из:
        // held: держал:
        // legal_entity_id: юридический идентификатор:
        // contractor_id: идентификатор подрядчика:
        // sales_channel_id: идентификатор канала продаж:
        // sum: сумма:
        // included_vat: включенный платеж:
        // comment: комментарий:
        // incoming_number: входящий номер:
        // incoming_date: входящие данные:
        // currency_id: идентификатор валюты:
        // bounded_sum: ограниченная сумма:
        // not_bounded_sum: сумма без ограничений:
        // is_imported: импортированным:

        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'ПриходныйКассовыйОрдер',
                    'Нпп' =>  $i,
                    'Тип' => 'ДокументСсылка.ПриходныйКассовыйОрдер'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Номер',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->number
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Дата',
                                'Тип' => 'Дата'
                            ],
                            'Значение' => $item->date_from
                        ]
                    ]
                ],
                'ТабличнаяЧасть' => [
                    '@attributes' => [
                        'Имя' =>  'РасшифровкаПлатежа'
                    ],
                    'Запись' => [
                        'Свойство' => [
                            [
                                '@attributes' => [
                                    'Имя' => 'ДоговорКонтрагента',
                                    'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                                ]
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СуммаНДС',
                                    'Тип' => 'Число'
                                ],
                                'Пусто' => ''
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СтавкаНДС',
                                    'Тип' => 'ПеречислениеСсылка.СтавкиНДС'
                                ],
                                'Пусто' => ''
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СуммаВзаиморасчетов',
                                    'Тип' => 'Число'
                                ],
                                'Значение' => $item->sum
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СуммаПлатежа',
                                    'Тип' => 'Число'
                                ],
                                'Значение' => $item->sum
                            ],
                            [
                                'currency_id' => $item->currency_id,
                                '@attributes' => [
                                    'Имя' => 'КратностьВзаиморасчетов',
                                    'Тип' => 'Число'
                                ],
                                'Пусто' => ''
                            ],
                            [
                                'currency_id' => $item->currency_id,
                                '@attributes' => [
                                    'Имя' => 'КурсВзаиморасчетов',
                                    'Тип' => 'Число'
                                ],
                                'Пусто' => ''
                            ],
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        'contractor_id' => $item->contractor_id,
                        '@attributes' => [
                            'Имя' => 'Контрагент',
                            'Тип' => 'СправочникСсылка.Контрагенты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'legal_entity_id' => $item->legal_entity_id,
                        '@attributes' => [
                            'Имя' => 'Организация',
                            'Тип' => 'СправочникСсылка.Организации'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ДоговорКонтрагента',
                            'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                        ]
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СуммаДокумента',
                            'Тип' => 'Число'
                        ],
                        'Значение' => $item->sum
                    ],
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'ВалютаДокумента',
                            'Тип' => 'СправочникСсылка.Валюты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВидОперации',
                            'Тип' => 'ПеречислениеСсылка.ВидыОперацийПКО'
                        ],
                        'Значение' => 'ПокупкаКомиссия'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => '???'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Основание',
                            'Тип' => 'Строка'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комментарий',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->comment,
                        'Пусто' => ''
                    ],
                ]
            ];

            $i++;

        }

        $this->i = $i;
        // dd($result);
        return $result;

    }


    public function getOutgoingPayments(string $cabinetId)
    {

        $data = DB::table('outgoing_payments as op')
        ->select('*')
        ->where('op.cabinet_id', $cabinetId)->get();
        // dd($data);

        // id: id:
        // created_at: created_at:
        // updated_at: updated_at:
        // cabinet_id: cabinet_id:
        // employee_id: employee_id:
        // number: номер:
        // date_from: дата получения:
        // held: держал:
        // without_closing_documents: без_закрытия_документов:
        // legal_entity_id: идентификатор юридического лица:
        // contractor_id: идентификатор подрядчика:
        // sales_channel_id: идентификатор канала продаж:
        // sum: сумма:
        // included_vat: включенный_ват:
        // comment: комментарий:
        // currency_id: идентификатор валюты:
        // bounded_sum: ограниченная сумма:
        // not_bounded_sum: не ограниченная сумма:
        // is_imported: is_imported:

        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                'id' => $item->id,
                '@attributes' => [
                    'ИмяПравила' => 'РасходныйКассовыйОрдер',
                    'Нпп' =>  $i,
                    'Тип' => 'ДокументСсылка.РасходныйКассовыйОрдер'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' =>  $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Номер',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->number
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Дата',
                                'Тип' => 'Дата'
                            ],
                            'Значение' => $item->date_from
                        ]
                    ]
                ],
                'ТабличнаяЧасть' => [
                    '@attributes' => [
                        'Имя' =>  'РасшифровкаПлатежа'
                    ],
                    'Запись' => [
                        'Свойство' => [
                            [
                                '@attributes' => [
                                    'Имя' => 'ДоговорКонтрагента',
                                    'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                                ]
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СуммаНДС',
                                    'Тип' => 'Число'
                                ],
                                'Пусто' => ''
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СтавкаНДС',
                                    'Тип' => 'ПеречислениеСсылка.СтавкиНДС'
                                ],
                                'Пусто' => ''
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СуммаВзаиморасчетов',
                                    'Тип' => 'Число'
                                ],
                                'Значение' => $item->sum
                            ],
                            [
                                '@attributes' => [
                                    'Имя' => 'СуммаПлатежа',
                                    'Тип' => 'Число'
                                ],
                                'Значение' => $item->sum
                            ],
                            [
                                'currency_id' => $item->currency_id,
                                '@attributes' => [
                                    'Имя' => 'КратностьВзаиморасчетов',
                                    'Тип' => 'Число'
                                ],
                                'Пусто' => ''
                            ],
                            [
                                'currency_id' => $item->currency_id,
                                '@attributes' => [
                                    'Имя' => 'КурсВзаиморасчетов',
                                    'Тип' => 'Число'
                                ],
                                'Пусто' => ''
                            ],
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        'contractor_id' => $item->contractor_id,
                        '@attributes' => [
                            'Имя' => 'Контрагент',
                            'Тип' => 'СправочникСсылка.Контрагенты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        'legal_entity_id' => $item->legal_entity_id,
                        '@attributes' => [
                            'Имя' => 'Организация',
                            'Тип' => 'СправочникСсылка.Организации'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ДоговорКонтрагента',
                            'Тип' => 'СправочникСсылка.ДоговорыКонтрагентов'
                        ]
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СуммаДокумента',
                            'Тип' => 'Число'
                        ],
                        'Значение' => $item->sum
                    ],
                    [
                        'currency_id' => $item->currency_id,
                        '@attributes' => [
                            'Имя' => 'ВалютаДокумента',
                            'Тип' => 'СправочникСсылка.Валюты'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВидОперации',
                            'Тип' => 'ПеречислениеСсылка.ВидыОперацийПКО'
                        ],
                        'Значение' => 'ПокупкаКомиссия'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => '???'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Основание',
                            'Тип' => 'Строка'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комментарий',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->comment,
                        'Пусто' => ''
                    ],
                ]
            ];

            $i++;

        }

        $this->i = $i;
        // dd($result);
        return $result;

    }



    // public function getCountries()
    // {
    //     $countries = DB::table('countries')->get();

    //     $result = [];

    //     foreach ($countries as $item) {
    //         $result[] = [
    //             '@attributes' => [
    //                 'ИмяПравила' => 'КлассификаторСтранМира',
    //                 'Нпп' => '',
    //                 'Тип' => 'СправочникСсылка.КлассификаторСтранМира'
    //             ],
    //             'Ссылка' => [
    //                 '@attributes' => [
    //                     'Нпп' => ''
    //                 ],
    //                 'Свойство' => [
    //                     '@attributes' => [
    //                         'Имя' => 'Код',
    //                         'Тип' => 'Строка'
    //                     ],
    //                     'Значение' => $item->code
    //                 ]
    //             ],
    //             'Свойство' => [
    //                 [
    //                     '@attributes' => [
    //                         'Имя' => 'ПометкаУдаления',
    //                         'Тип' => 'Булево'
    //                     ],
    //                     'Значение' => 'false',
    //                 ],
    //                 [
    //                     '@attributes' => [
    //                         'Имя' => 'Наименование',
    //                         'Тип' => 'Строка'
    //                     ],
    //                     'Значение' => $item->name
    //                 ],
    //                 [
    //                     '@attributes' => [
    //                         'Имя' => 'НаименованиеПолное',
    //                         'Тип' => 'Строка'
    //                     ],
    //                     'Значение' => $item->full_name
    //                 ]
    //             ]
    //         ];
    //     }

    //     return $result;
    // }

    // КлассификаторЕдиницИзмерения Нпп="14"
    public function getMeasurementProducts(string $cabinetId)
    {

        $shipments = DB::table('shipments as s')
        ->select('s.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('shipment_items as shi', 's.id', '=', 'shi.shipment_id')
        ->leftjoin('products as p', 'p.id', '=', 'shi.product_id')
        ->leftjoin('measurement_units as mu', 'mu.id', '=', 'p.measurement_unit_id')
        ->groupBy('s.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') //, 'vr.rate', 'vr.description'
        ->where('s.cabinet_id', $cabinetId)
        ->distinct('measurement_unit_id')->get();

        $acceptances = DB::table('acceptances as ac')
        ->select('ac.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') // , 'vr.rate', 'vr.description as vr_desc'
        ->leftjoin('acceptance_items as aci', 'ac.id', '=', 'aci.acceptance_id')
        ->leftjoin('products as p', 'p.id', '=', 'aci.product_id')
        ->leftjoin('measurement_units as mu', 'mu.id', '=', 'p.measurement_unit_id')
        ->groupBy('ac.id', 'p.measurement_unit_id', 'mu.name', 'mu.code', 'mu.short_name') //, 'vr.rate', 'vr.description'
        ->where('ac.cabinet_id', $cabinetId)
        ->distinct('measurement_unit_id')->get();

        $mergeArray = [];
        $uniqueArray = [];
        $uniqueIds = [];

        foreach (array_merge($shipments->toArray(), $acceptances->toArray()) as $object) {
            $mergeArray[] = (array) $object;
        }

        foreach ($mergeArray as $item) {
            if (!in_array($item['measurement_unit_id'], $uniqueIds)) {
                $uniqueArray[] = $item;
                $uniqueIds[] = $item['measurement_unit_id'];
            }
        }

        $result = [];

        $i = $this->i;

        foreach ($uniqueArray as $item) {

            $result[] = [
                '@attributes' => [
                    'ИмяПравила' => 'КлассификаторЕдиницИзмерения',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.КлассификаторЕдиницИзмерения'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => '{УникальныйИдентификатор}',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item['measurement_unit_id']
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item['code']
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false Вставить Softdelete',
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Наименование',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item['short_name']
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'НаименованиеПолное',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item['name']
                    ]
                ]
            ];

            $this->temp[] = [
                'id' => $item['measurement_unit_id'],
                '@attributes' => [
                    'Имя' => 'БазоваяЕдиницаИзмерения',
                    'Тип' => 'СправочникСсылка.КлассификаторЕдиницИзмерения'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => '{УникальныйИдентификатор}',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item['measurement_unit_id']
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item['code']
                        ]
                    ]
                ]
            ];

            $i++;

        }

        $this->i = $i;
        // dd($result);
        return $result;
    }


    // public function getMeasurements()
    // {
    //     $data = DB::table('measurement_units')->get();

    //     $result = [];

    //     foreach ($data as $item) {
    //         $result[] = [
    //             '@attributes' => [
    //                 'ИмяПравила' => 'КлассификаторЕдиницИзмерения',
    //                 'Нпп' => '',
    //                 'Тип' => 'СправочникСсылка.КлассификаторЕдиницИзмерения'
    //             ],
    //             'Ссылка' => [
    //                 '@attributes' => [
    //                     'Нпп' => ''
    //                 ],
    //                 'Свойство' => [
    //                     '@attributes' => [
    //                         'Имя' => 'Код',
    //                         'Тип' => 'Строка'
    //                     ],
    //                     'Значение' => $item->code
    //                 ]
    //             ],
    //             'Свойство' => [
    //                 [
    //                     '@attributes' => [
    //                         'Имя' => 'ПометкаУдаления',
    //                         'Тип' => 'Булево'
    //                     ],
    //                     'Значение' => 'false',
    //                 ],
    //                 [
    //                     '@attributes' => [
    //                         'Имя' => 'Наименование',
    //                         'Тип' => 'Строка'
    //                     ],
    //                     'Значение' => $item->short_name
    //                 ],
    //                 [
    //                     '@attributes' => [
    //                         'Имя' => 'НаименованиеПолное',
    //                         'Тип' => 'Строка'
    //                     ],
    //                     'Значение' => $item->name
    //                 ]
    //             ]
    //         ];
    //     }

    //     return $result;
    // }

    public function getProducts(string $cabinetId)
    {

        $data = DB::table('products as p')
        ->select('p.id', 'p.title', 'p.code', 'p.type', 'p.article', 'p.tax', 'p.description', 'p.measurement_unit_id', 'mu.name', 'mu.code as code_m', 'p.deleted_at')
        ->leftjoin('measurement_units as mu', 'mu.id', '=', 'p.measurement_unit_id')
        ->groupBy(['p.id', 'p.title', 'p.code', 'p.article', 'p.tax', 'p.description', 'mu.name', 'mu.code', 'p.deleted_at'])
        ->where('p.cabinet_id', $cabinetId)->get();
        // dd($data);
        $result = [];

        $i = $this->i;

        foreach ($data as $item) {

            $result[] = [
                '@attributes' => [
                    'ИмяПравила' => 'Номенклатура',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Номенклатура'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Наименование',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->title
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'ЭтоГруппа',
                                'Тип' => 'Булево'
                            ],
                            'Значение' => 'false'
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->code
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'Код',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->code
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Артикул',
                            'Тип' => 'Строка'
                        ],
                        'Значение' =>  $item->article
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' =>  $item->deleted_at
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ЭтоГруппа',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'НаименованиеПолное',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->title
                    ],
                    [
                        'measurement_unit_id' => $item->measurement_unit_id,
                        '@attributes' => [
                            'Имя' => 'БазоваяЕдиницаИзмерения',
                            'Тип' => 'СправочникСсылка.КлассификаторЕдиницИзмерения'
                        ],
                        'Пусто' => ''
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'СтавкаНДС',
                            'Тип' => 'ПеречислениеСсылка.СтавкиНДС'
                        ],
                        'Значение' => $item->tax
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комментарий',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->description
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Услуга',
                            'Тип' => 'Булево'
                        ],
                        'Значение' => 'false'
                    ],

                ],

            ];


            $this->temp[] = [
                'id' => $item->id,
                'type' => $item->type,
                '@attributes' => [
                    'Имя' => 'Номенклатура',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Номенклатура'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Наименование',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->title
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'ЭтоГруппа',
                                'Тип' => 'Булево'
                            ],
                            'Значение' => 'false'
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'Код',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->code
                        ]
                    ]
                ]
            ];


            $i++;

        }

        $this->i = $i;

        return $result;

    }

    public function getWarehouses(string $cabinetId)
    {

        // нужна ли группа
        // добавить софтделит

        $data = DB::table('warehouses as w')
        ->select('w.id', 'w.name', 'wa.comment')
        ->leftjoin('warehouse_addresses as wa', 'wa.id', '=', 'w.address_id')
        ->groupBy(['w.id', 'w.name', 'wa.comment'])
        ->where('w.cabinet_id', $cabinetId)->get();
        // dd($data);
        $result = [];

        $i = $this->i;
        foreach ($data as $item) {

            $result[] = [
                '@attributes' => [
                    'ИмяПравила' => 'Склады',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Склады'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Наименование',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->name
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'ЭтоГруппа',
                                'Тип' => 'Булево'
                            ],
                            'Значение' => 'false Нужна ли группа?'
                        ]
                    ]
                ],
                'Свойство' => [
                    [
                        '@attributes' => [
                            'Имя' => 'ПометкаУдаления',
                            'Тип' => 'Булево'
                        ],
                        'Значение' =>  'false добавить софтделит'
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'Комментарий',
                            'Тип' => 'Строка'
                        ],
                        'Значение' => $item->comment
                    ],
                    [
                        '@attributes' => [
                            'Имя' => 'ВидСклада',
                            'Тип' => 'ПеречислениеСсылка.ВидыСкладов'
                        ],
                        'Значение' => 'Будет ли у нас ВидСклада???'
                    ]
                ],
            ];

            $this->temp[] = [
                'id' => $item->id,
                '@attributes' => [
                    'Имя' => 'Склад',
                    'Нпп' => $i,
                    'Тип' => 'СправочникСсылка.Склады'
                ],
                'Ссылка' => [
                    '@attributes' => [
                        'Нпп' => $i
                    ],
                    'Свойство' => [
                        [
                            '@attributes' => [
                                'Имя' => 'Наименование',
                                'Тип' => 'Строка'
                            ],
                            'Значение' => $item->name
                        ],
                        [
                            '@attributes' => [
                                'Имя' => 'ЭтоГруппа',
                                'Тип' => 'Булево'
                            ],
                            'Значение' => 'false Нужна ли группа?'
                        ]
                    ]
                ]
            ];

            $i++;
        }

        $this->i = $i;

        return $result;
    }

}
