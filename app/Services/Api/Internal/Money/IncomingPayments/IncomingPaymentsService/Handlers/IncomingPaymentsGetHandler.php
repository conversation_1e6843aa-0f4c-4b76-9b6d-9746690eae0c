<?php

namespace App\Services\Api\Internal\Money\IncomingPayments\IncomingPaymentsService\Handlers;

use App\Contracts\Repositories\IncomingPaymentsRepositoryContract;
use Illuminate\Support\Collection;

readonly class IncomingPaymentsGetHandler
{
    public function __construct(
        private IncomingPaymentsRepositoryContract $repository
    ) {
    }

    public function run(array $data): Collection
    {
        $cabinetId = $data['cabinet_id'] ?? null;
        return $this->repository->get($cabinetId);
    }
}
