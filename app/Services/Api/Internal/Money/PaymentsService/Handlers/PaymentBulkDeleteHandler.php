<?php

namespace App\Services\Api\Internal\Money\PaymentsService\Handlers;

use App\Contracts\Repositories\DocumentsRepositoryContract;
use Illuminate\Support\Facades\DB;

readonly class PaymentBulkDeleteHandler
{
    public function __construct(
        private DocumentsRepositoryContract $documentsRepository
    ) {
    }

    public function run(array $ids): void
    {
        DB::table('incoming_payments')
            ->whereIn('id', $ids)
            ->delete();

        DB::table('outgoing_payments')
            ->whereIn('id', $ids)
            ->delete();

        $this->documentsRepository->deleteWhereDocumentableIdIn($ids);
    }
}
