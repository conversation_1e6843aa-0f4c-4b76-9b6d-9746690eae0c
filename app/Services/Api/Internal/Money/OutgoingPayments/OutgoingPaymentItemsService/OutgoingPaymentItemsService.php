<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\Finances\OutgoingPaymentItemsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\Handlers\OutgoingPaymentItemsCreateHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\Handlers\OutgoingPaymentItemsDeleteHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\Handlers\OutgoingPaymentItemsGetHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\Handlers\OutgoingPaymentItemsShowHandler;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\Handlers\OutgoingPaymentItemsUpdateHandler;
use Illuminate\Support\Collection;

readonly class OutgoingPaymentItemsService implements OutgoingPaymentItemsServiceContract
{
    public function __construct(
        private OutgoingPaymentItemsCreateHandler $createHandler,
        private OutgoingPaymentItemsGetHandler $getHandler,
        private OutgoingPaymentItemsShowHandler $showHandler,
        private OutgoingPaymentItemsUpdateHandler $updateHandler,
        private OutgoingPaymentItemsDeleteHandler $deleteHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection
    {
        return $this->getHandler->run($data);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }
}
