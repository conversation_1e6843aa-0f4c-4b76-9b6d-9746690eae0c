<?php

namespace App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\Handlers;

use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Repositories\OutgoingPaymentsRepositoryContract;
use App\Services\Api\Internal\Documents\DocumentNumberGenerator\DocumentNumberGenerator;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentsService\DTO\OutgoingPaymentDTO;
use App\Traits\HasFiles;
use App\Traits\HasOrderedUuid;
use InvalidArgumentException;
use Symfony\Component\Routing\Exception\ResourceNotFoundException;

readonly class OutgoingPaymentsUpdateHandler
{
    use HasOrderedUuid;
    use HasFiles;

    public function __construct(
        private OutgoingPaymentsRepositoryContract $repository
    ) {
    }

    public function run(HasUpdateArrayDtoContract $dto): void
    {
        if ($dto instanceof OutgoingPaymentDTO) {

            $resource = $this->repository->show($dto->id);

            if (!$resource) {
                throw new ResourceNotFoundException();
            }
            $update = $dto->toUpdateArray();
            if ($resource->legal_entity_id !== $dto->legal_entity_id) {
                $documentNumberGenerator = new DocumentNumberGenerator(
                    'outgoing_payments',
                    $resource->cabinet_id,
                    $dto->number,
                    $dto->legal_entity_id
                );
                $update['number'] = $documentNumberGenerator->generateNumber();
            }

            $this->repository->update(
                $dto->id,
                $update
            );

            $this->setRelationToFiles($dto->id, $dto->files, 'outgoing_payments', true);
            return;
        }

        throw new InvalidArgumentException('Unsupported DTO type');

    }
}
