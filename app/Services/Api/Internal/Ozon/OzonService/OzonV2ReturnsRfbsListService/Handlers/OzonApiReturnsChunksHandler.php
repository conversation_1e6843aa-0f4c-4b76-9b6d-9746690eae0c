<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV2ReturnsRfbsListService\Handlers;

use App\Contracts\Repositories\OzonV2ReturnsRfbsListRepositoryContract;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class OzonApiReturnsChunksHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        private readonly OzonV2ReturnsRfbsListRepositoryContract $repository,
    ) {
    }

    public function run(Collection $returns, int $limit, string $cabinetId, string $departmentId, string $employeeId, string $ozonCompanyId): void
    {
        $orderNumberChunks = $returns->chunk($limit);

        $orderNumberChunks->each(function ($chunk) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId) {
            $orders = [];

            $existingReturns = $this->repository->getReturnsByReturnIds($chunk->pluck('return_id')->toArray())->toArray();

            $chunk->each(function ($item) use ($cabinetId, $departmentId, $employeeId, $ozonCompanyId, &$orders, &$existingReturns) {
                $orderId = $existingReturns[$item['return_id']] ?? $this->generateUuid();

                $orders[] = $this->prepareOrderData($item, $orderId, $cabinetId, $departmentId, $employeeId, $ozonCompanyId);
            });

            if (!empty($orders)) {
                $this->repository->upsert($orders);
            }
        });
    }


    private function prepareOrderData($item, string $orderId, string $cabinetId, string $departmentId, string $employeeId, string $ozonCompanyId): array
    {
        return [

            'id' => $orderId,
            'cabinet_id' => $cabinetId,
            'department_id' => $departmentId,
            'employee_id' => $employeeId,
            'ozon_company_id' => $ozonCompanyId,
            'client_name' => $item['client_name'],
            'rfbs_created_at' => $this->formatDate($item['created_at'] ?? null),
            'order_number' => $item['order_number'],
            'posting_number' => $item['posting_number'],
            'product_currency_code' => $item['product']['currency_code'],
            'product_name' => $item['product']['name'],
            'product_offer_id' => $item['product']['offer_id'],
            'product_price' => $this->convertToKopecks($item['product']['price'] ?? null),
            'product_sku' => $item['product']['sku'],
            'return_id' => $item['return_id'],
            'return_number' => $item['return_number'],
            'state_state' => $item['state']['state'],
            'state_state_name' => $item['state']['state_name'],
            'state_group_state' => $item['state']['group_state'],
            'state_money_return_state_name' => $item['state']['money_return_state_name'],

            'created_at' => now(),
            'updated_at' => now(),

        ];
    }

    private function formatDate(?string $date): ?string
    {
        return $date ? Carbon::parse($date)->format('Y-m-d H:i:s') : null;
    }

    private function convertToKopecks(?int $price): ?int
    {
        return isset($price) ? $this->rublesInKopeck($price) : null;
    }

}
