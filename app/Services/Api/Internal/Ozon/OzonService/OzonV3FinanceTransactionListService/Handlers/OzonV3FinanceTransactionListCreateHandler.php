<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\Handlers;

use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsShowHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3FinanceTransactionListService\DTO\OzonV3FinanceTransactionListDTO;
use App\Traits\ConverterForKopecks;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Collection;

class OzonV3FinanceTransactionListCreateHandler
{
    use HasOrderedUuid;
    use ConverterForKopecks;

    public function __construct(
        protected OzonApiRequestHandler $ozonApiRequestHandler,
        protected OzonApiTransactionNumberChunksHandler $ozonApiTransactionNumberChunksHandler,
        protected OzonCredentialsShowHandler $ozonCredentialsShowHandler,
    ) {
    }

    public function run(OzonV3FinanceTransactionListDTO $dto): void
    {

        $page = 1;

        $pageSize = 1000;

        $ozonCredential = $this->ozonCredentialsShowHandler->run($dto->ozonCredentialId);

        $response = $this->ozonApiRequestHandler->run($dto, $ozonCredential->client_id, $ozonCredential->api_key, $page, $pageSize);

        for ($page; $page <= $response['result']['page_count']; $page++) {

            $response = $this->ozonApiRequestHandler->run($dto, $ozonCredential->client_id, $ozonCredential->api_key, $page, $pageSize);

            $soldAmount = new Collection($response['result']['operations']);

            if ($soldAmount->isNotEmpty()) {

                $this->ozonApiTransactionNumberChunksHandler->run($soldAmount, $pageSize, $dto->cabinetId, $dto->departmentId, $dto->employeeId, $ozonCredential->client_id);

            }

        }

    }
}
