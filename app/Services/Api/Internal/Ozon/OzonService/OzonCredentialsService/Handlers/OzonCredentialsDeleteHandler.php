<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers;

use App\Contracts\Repositories\OzonCredentialsRepositoryContract;
use App\Traits\HasOrderedUuid;

class OzonCredentialsDeleteHandler
{
    use HasOrderedUuid;

    public function __construct(
        private readonly OzonCredentialsRepositoryContract $repository,
    ) {
    }

    public function run(string $resourceId): void
    {
        $this->repository->delete($resourceId);
    }
}
