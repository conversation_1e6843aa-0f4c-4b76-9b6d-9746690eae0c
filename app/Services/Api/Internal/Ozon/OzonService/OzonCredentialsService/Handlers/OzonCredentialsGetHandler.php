<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers;

use App\Contracts\Repositories\OzonCredentialsRepositoryContract;
use Illuminate\Support\Collection;

class OzonCredentialsGetHandler
{
    public function __construct(
        private readonly OzonCredentialsRepositoryContract $repository,
    ) {
    }

    public function run(string $cabinetId): Collection
    {

        $ozonCredentials = $this->repository->get($cabinetId);

        return $ozonCredentials->map(function ($item) {
            return [
                'id' => $item->id,
                'cabinet_id' => $item->cabinet_id,
                'employee_id' => $item->employee_id,
                'department_id' => $item->department_id,
                'name' => $item->name,
                'client_id' => decrypt($item->client_id),
                'api_key' => decrypt($item->api_key),
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at,
            ];
        });

    }
}
