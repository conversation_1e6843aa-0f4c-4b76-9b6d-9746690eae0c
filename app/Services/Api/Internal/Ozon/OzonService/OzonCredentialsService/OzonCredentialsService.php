<?php

namespace App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\HasUpdateArrayDtoContract;
use App\Contracts\Services\Internal\OzonCredentialsServiceContract;
use App\DTO\IndexRequestDTO;
use App\Exceptions\InvalidUuidException;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsCreateHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsDeleteHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsGetHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsShowHandler;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\Handlers\OzonCredentialsUpdateHandler;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

readonly class OzonCredentialsService implements OzonCredentialsServiceContract
{
    public function __construct(
        private OzonCredentialsGetHandler $getHandler,
        private OzonCredentialsCreateHandler $createHandler,
        private OzonCredentialsUpdateHandler $updateHandler,
        private OzonCredentialsDeleteHandler $deleteHandler,
        private OzonCredentialsShowHandler $showHandler
    ) {
    }

    public function index(array|IndexRequestDTO $data): Collection|LengthAwarePaginator
    {
        return $this->getHandler->run($data['cabinet_id']);
    }

    public function show(string $id): ?object
    {
        return $this->showHandler->run($id);
    }

    /**
     * @throws InvalidUuidException
     */
    public function delete(string $id): void
    {
        $this->deleteHandler->run($id);
    }

    public function create(HasInsertArrayDtoContract $dto): string
    {
        return $this->createHandler->run($dto);
    }

    public function update(HasUpdateArrayDtoContract $dto): void
    {
        $this->updateHandler->run($dto);
    }

}
