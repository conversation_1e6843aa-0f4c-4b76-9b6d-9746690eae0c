<?php

namespace App\Services\Api\Internal\WarehouseReservationService;

use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Repositories\WarehouseReservationsRepositoryContract;
use App\Contracts\Services\Internal\WarehouseReservationServiceContract;
use App\Services\Api\Internal\WarehouseReservationService\Handlers\ReserveItemsHandler;
use App\Services\Api\Internal\WarehouseReservationService\Handlers\CancelReservationHandler;
use App\Services\Api\Internal\WarehouseReservationService\Handlers\CheckAvailabilityHandler;

readonly class WarehouseReservationService implements WarehouseReservationServiceContract
{
    public function __construct(
        private ReserveItemsHandler $reserveHandler,
        private CancelReservationHandler $cancelHandler,
        private CheckAvailabilityHandler $availabilityHandler
    ) {
    }

    public function reserveForOrderItem(string $orderItemId, string $productId, string $warehouseId, int $quantity, string $dateFrom): bool
    {
        return $this->reserveHandler->run($orderItemId, $productId, $warehouseId, $quantity, $dateFrom);
    }

    public function cancelReservationForOrderItem(string $orderItemId): bool
    {
        return $this->cancelHandler->run($orderItemId);
    }

    public function checkAvailability(string $productId, string $warehouseId, int $quantity, string $dateFrom): bool
    {
        return $this->availabilityHandler->run($productId, $warehouseId, $quantity, $dateFrom);
    }

    public function getAvailableQuantity(string $productId, string $warehouseId, string $dateFrom): int
    {
        return $this->availabilityHandler->getAvailableQuantity($productId, $warehouseId, $dateFrom);
    }
}
