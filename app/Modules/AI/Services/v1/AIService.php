<?php

namespace App\Modules\AI\Services\v1;

use Illuminate\Support\Collection;
use App\Modules\AI\Contracts\v1\Services\AIServiceContract;
use App\Modules\AI\Contracts\v1\Repository\EmployeeRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\WarehouseRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\DepartmentRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\LegalEntityRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\SalesChannelsRepositoryContract;
use App\Modules\AI\Contracts\v1\Repository\CabinetCurrencyRepositoryContract;

readonly class AIService implements AIServiceContract
{
    public function __construct(
        private WarehouseRepositoryContract $warehouseRepository,
        private LegalEntityRepositoryContract $legalEntityRepository,
        private EmployeeRepositoryContract $employeeRepository,
        private DepartmentRepositoryContract $departmentRepository,
        private SalesChannelsRepositoryContract $salesChannelsRepository,
        private CabinetCurrencyRepositoryContract $cabinetCurrencyRepository,
    ) {
    }

    public function getWarehouseData(string $cabinetId): Collection
    {
        return $this->warehouseRepository->get($cabinetId);
    }

    public function getLegalsData(string $cabinetId): Collection
    {
        return $this->legalEntityRepository->get($cabinetId);
    }

    public function getEmployeesData(string $cabinetId): Collection
    {
        return $this->employeeRepository->get($cabinetId);
    }

    public function getDepartmentsData(string $cabinetId): Collection
    {
        return $this->departmentRepository->get($cabinetId);
    }

    public function getSalesChannelsData(string $cabinetId): Collection
    {
        return $this->salesChannelsRepository->get($cabinetId);
    }

    public function getCabinetCurrencyData(string $cabinetId): Collection
    {
        return $this->cabinetCurrencyRepository->get($cabinetId);
    }
}
