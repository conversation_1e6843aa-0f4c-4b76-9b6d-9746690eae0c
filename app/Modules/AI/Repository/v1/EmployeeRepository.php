<?php

namespace App\Modules\AI\Repository\v1;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Modules\AI\Contracts\v1\Repository\EmployeeRepositoryContract;

class EmployeeRepository implements EmployeeRepositoryContract
{
    public function get(string $cabinetId): Collection
    {
        return DB::table('cabinet_employee')
            ->join('employees', 'employees.id', '=', 'cabinet_employee.employee_id')
            ->where('cabinet_employee.cabinet_id', $cabinetId)
            ->select([
                'employees.id',
                'employees.firstname',
                DB::raw("NULLIF(
                    COALESCE(employees.lastname, '') || ' ' ||
                    COALESCE(employees.firstname, '') || ' ' ||
                    COALESCE(employees.patronymic, ''), ''
                ) AS fullname"),

                'employees.telephone',
                'employees.email',
                DB::raw("
                CASE 
                    WHEN employees.driver_license_series IS NOT NULL 
                    OR employees.driver_license_number IS NOT NULL
                    OR employees.driver_license_issue_date IS NOT NULL
                    OR employees.driver_license_expiration_date IS NOT NULL
                    OR employees.driver_license_category IS NOT NULL
                    THEN true 
                    ELSE false
                END AS driver_license
                ")
            ])
            ->get();
    }
}
