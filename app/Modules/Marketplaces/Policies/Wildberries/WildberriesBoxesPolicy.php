<?php

namespace App\Modules\Marketplaces\Policies\Wildberries;

use App\Modules\Marketplaces\Contracts\ModuleAuthInterface;

class WildberriesBoxesPolicy
{
    public function __construct(
        protected ModuleAuthInterface $moduleAuth
    ) {
    }

    public function checkPermissionsToSupply(string $id): void
    {
        $this->moduleAuth->validateRelationAccess(
            'wildberries_supplies',
            $id
        );
    }

    public function checkPermissionsToBox(array|string $ids): ?object
    {
        if (is_array($ids)) {
            $this->moduleAuth->validateResourcesAccess(
                'wildberries_boxes',
                null,
                $ids
            );
            return null;
        }
        return $this->moduleAuth->validateRelationAccess(
            'wildberries_boxes',
            $ids
        );
    }

    public function addOrders(string $supplyId, string $boxId, array $orderIds): void
    {
        $this->checkPermissionsToSupply($supplyId);

        $box = $this->checkPermissionsToBox($boxId);

        $this->moduleAuth->validateResourcesAccess(
            'wildberries_fbs_orders',
            $box->cabinet_id,
            $orderIds
        );
    }

    public function deleteOrderFromBox(string $supplyId, string $boxId, string $orderId): void
    {
        $this->checkPermissionsToSupply($supplyId);

        $this->checkPermissionsToBox($boxId);

        $this->moduleAuth->validateRelationAccess(
            'wildberries_fbs_orders',
            $orderId
        );
    }
}
