<?php

namespace App\Modules\Marketplaces\Policies\Ozon;

use App\Modules\Marketplaces\Contracts\ModuleAuthInterface;
use App\Modules\Marketplaces\DTO\CostAccounting\CostAccountingDto;
use App\Modules\Marketplaces\Services\Ozon\Data\OzonMarketData;

class OzonPolicy
{
    public function __construct(
        protected ModuleAuthInterface $moduleAuth
    ) {
    }

    public function index(string $cabinetId): void
    {
        $this->moduleAuth->hasAccessToCabinet($cabinetId);
    }
    /**
     * Проверка прав доступа к интеграции
     */
    public function checkPermissionsToIntegration(string $id): void
    {
        $this->moduleAuth->validateRelationAccess(
            'ozon_integrations',
            $id
        );
    }

    /**
     * Проверка прав доступа к складу
     */
    public function checkPermissionsToWarehouse(string $warehouseId): void
    {
        $this->moduleAuth->validateRelationAccess(
            'ozon_fbs_warehouses',
            $warehouseId
        );
    }

    public function create(OzonMarketData $dto): void
    {
        $this->moduleAuth->hasAccessToCabinet($dto->cabinetId);

        $this->moduleAuth->validateRelationAccess(
            'legal_entities',
            $dto->legalEntityId,
            $dto->cabinetId
        );

        $this->moduleAuth->validateRelationAccess(
            'contractors',
            $dto->contractorId,
            $dto->cabinetId
        );

        $this->moduleAuth->validateRelationAccess(
            'departments',
            $dto->departmentId,
            $dto->cabinetId
        );

        $this->moduleAuth->validateRelationAccess(
            'contracts',
            $dto->comissionContractId,
            $dto->cabinetId
        );
    }

    public function update(OzonMarketData $dto, string $id): void
    {
        $integration = $this->moduleAuth->validateRelationAccess(
            'ozon_integrations',
            $id
        );

        if ($dto->legalEntityId != $integration->legal_entity_id) {
            $this->moduleAuth->validateRelationAccess(
                'legal_entities',
                $dto->legalEntityId,
                $integration->cabinet_id
            );
        }

        if ($dto->comissionContractId != $integration->commission_contract_id) {
            $this->moduleAuth->validateRelationAccess(
                'contracts',
                $dto->comissionContractId,
                $integration->cabinet_id
            );
        }

        if ($dto->contractorId != $integration->contractor_id) {
            $this->moduleAuth->validateRelationAccess(
                'contractors',
                $dto->contractorId,
                $integration->cabinet_id
            );
        }

        if ($dto->departmentId != $integration->department_id) {
            $this->moduleAuth->validateRelationAccess(
                'departments',
                $dto->departmentId,
                $integration->cabinet_id
            );
        }

        if (isset($dto->priceSync['prediscount_price_id'])) {
            $this->moduleAuth->validateRelationAccess(
                'cabinet_prices',
                $dto->priceSync['prediscount_price_id'],
                $integration->cabinet_id
            );
        }

        if (isset($dto->priceSync['min_price_id'])) {
            $this->moduleAuth->validateRelationAccess(
                'cabinet_prices',
                $dto->priceSync['min_price_id'],
                $integration->cabinet_id
            );
        }
    }

    public function updateFbs(string $id, string $warehouseId): void
    {
        // TODO: Implement updateFbs() method.
    }

    public function updateAccountingCostSettings(CostAccountingDto $dto, string $id): void
    {
        // TODO: Implement updateAccountingCostSettings() method.
    }

    public function checkPermissionsToOrder(string $orderId): void
    {
        $this->moduleAuth->validateRelationAccess(
            'ozon_fbs_orders',
            $orderId
        );
    }
}
