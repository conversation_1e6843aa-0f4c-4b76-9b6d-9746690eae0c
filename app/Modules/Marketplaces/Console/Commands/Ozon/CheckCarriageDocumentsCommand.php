<?php

namespace App\Modules\Marketplaces\Console\Commands\Ozon;

use App\Modules\Marketplaces\Services\Ozon\Actions\Carriages\CheckDocumentsStatusAction;
use Illuminate\Console\Command;

class CheckCarriageDocumentsCommand extends Command
{
    protected $signature = 'ozon:check-carriage-documents 
                           {--max-attempts=5 : Maximum check attempts per carriage}
                           {--batch-size=50 : Number of carriages to process in one batch}';

    protected $description = 'Check Ozon carriage documents status';

    public function handle(): int
    {
        $maxAttempts = (int) $this->option('max-attempts');
        $batchSize = (int) $this->option('batch-size');

        $this->info("Starting carriage documents check (max attempts: {$maxAttempts}, batch size: {$batchSize})");

        $stats = (new CheckDocumentsStatusAction())->run($maxAttempts, $batchSize);

        $this->table(
            ['Metric', 'Count'],
            [
                ['Processed', $stats['processed']],
                ['Ready', $stats['ready']],
                ['In Process', $stats['in_process']],
                ['Errors', $stats['errors']],
                ['Failed', $stats['failed']],
            ]
        );

        $this->info('Carriage documents check completed');

        return self::SUCCESS;
    }
}
