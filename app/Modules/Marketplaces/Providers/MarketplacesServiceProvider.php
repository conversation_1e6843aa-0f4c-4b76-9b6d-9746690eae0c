<?php

namespace App\Modules\Marketplaces\Providers;

use App\Modules\Marketplaces\Adapters\AuthServiceAdapter;
use App\Modules\Marketplaces\Contracts\MarketplaceDtoFactoryInterface;
use App\Modules\Marketplaces\Contracts\MarketplaceRequestFactoryInterface;
use App\Modules\Marketplaces\Contracts\ModuleAuthInterface;
use App\Modules\Marketplaces\Factories\MarketplaceDtoFactory;
use App\Modules\Marketplaces\Factories\MarketplaceRequestFactory;
use App\Modules\Marketplaces\Services\Ozon\OzonServiceProvider;
use App\Modules\Marketplaces\Services\Wildberries\WildberriesServiceProvider;
use Illuminate\Support\ServiceProvider;

class MarketplacesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->register(OzonServiceProvider::class);
        $this->app->register(WildberriesServiceProvider::class);
        $this->app->register(WildberriesActionsServiceProvider::class);
        $this->app->singleton(ModuleAuthInterface::class, AuthServiceAdapter::class);

        // Регистрация фабрик
        $this->app->bind(MarketplaceDtoFactoryInterface::class, MarketplaceDtoFactory::class);
        $this->app->bind(MarketplaceRequestFactoryInterface::class, MarketplaceRequestFactory::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
    }
}
