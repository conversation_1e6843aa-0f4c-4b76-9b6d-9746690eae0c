<?php

namespace App\Modules\Marketplaces\Factories;

use App\Modules\Marketplaces\Policies\BaseMarketplacePolicy;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesPolicy;
use InvalidArgumentException;

readonly class MarketplacePolicyFactory
{
    public function __construct(
        private OzonPolicy $ozonPolicy,
        private WildberriesPolicy $wildberriesPolicy
    ) {
    }

    public function create(string $type): BaseMarketplacePolicy
    {
        return match ($type) {
            'ozon' => $this->ozonPolicy,
            'wildberries' => $this->wildberriesPolicy,
            default => throw new InvalidArgumentException('Unknown policy')
        };
    }
}
