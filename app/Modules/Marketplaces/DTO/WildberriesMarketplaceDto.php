<?php

namespace App\Modules\Marketplaces\DTO;

class WildberriesMarketplaceDto implements MarketplaceDto
{
    public array $priceSync;
    public array $residuesSync;
    public array $orderSync;
    public array $reportSync;

    public function __construct(
        public readonly string $cabinetId,
        public readonly string $shopName,
        public readonly string $token,
        public readonly string $legalEntityId,
        public readonly string $contractorId,
        public readonly string $comissionContractId,
        public readonly string $departmentId,
        array $priceSync = [],
        array $residuesSync = [],
        array $orderSync = [],
        array $reportSync = []
    ) {
        $this->priceSync = $priceSync;
        $this->residuesSync = $residuesSync;
        $this->orderSync = $orderSync;
        $this->reportSync = $reportSync;
    }

    public static function fromArray(array $data): self
    {
        return new self(
            cabinetId: $data['cabinet_id'],
            shopName: $data['name'],
            token: $data['token'],
            legalEntityId: $data['legal_entity_id'],
            contractorId: $data['contractor_id'],
            comissionContractId: $data['comission_contract_id'],
            departmentId: $data['department_id'],
            // Дополнительные настройки синхронизации сохраняем в приватных свойствах
            priceSync: $data['price_sync'] ?? [],
            residuesSync: $data['residues_sync'] ?? [],
            orderSync: $data['order_sync'] ?? [],
            reportSync: $data['report_sync'] ?? [],
        );
    }
}
