<?php

namespace App\Modules\Marketplaces\Enums;

enum ProductMatchingTypeEnum: string
{
    case SKU = 'SKU';
    case NAME = 'NAME';
    case ARTICLE = 'ARTICLE';
    case CODE = 'CODE';
    case BARCODE = 'BARCODE';
    case MARKET_ARTICLE_BARCODE = 'MARKET_ARTICLE_BARCODE';

    public function label(): string
    {
        return match ($this) {
            self::SKU => 'По SKU',
            self::NAME => 'По наименованию',
            self::ARTICLE => 'По артикулу',
            self::CODE => 'По коду',
            self::BARCODE => 'По штрихкоду',
            self::MARKET_ARTICLE_BARCODE => 'Артикул маркета - штрихкод',
        };
    }
}
