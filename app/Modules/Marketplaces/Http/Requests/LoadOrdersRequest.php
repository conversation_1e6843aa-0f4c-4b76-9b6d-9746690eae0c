<?php

namespace App\Modules\Marketplaces\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoadOrdersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'date_from' => 'sometimes|date_format:Y-m-d|before_or_equal:date_to',
            'date_to' => 'required_if:date_from,!=,null|date_format:Y-m-d|after_or_equal:date_from',
        ];
    }
}
