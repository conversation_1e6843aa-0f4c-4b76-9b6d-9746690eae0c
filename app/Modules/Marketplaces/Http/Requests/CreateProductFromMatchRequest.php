<?php

namespace App\Modules\Marketplaces\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateProductFromMatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'product_to_match_id' => 'required|string|uuid',
            'product_data' => 'required|array',
            'product_data.department_id' => 'required|string|uuid',
            'product_data.type' => 'required|integer',
            'product_data.indication_subject_calculation' => 'required|string',
            'product_data.tax_system' => 'required|string',
            'product_data.title' => 'required|string|max:255',
            'product_data.article' => 'nullable|string|max:100',
            'product_data.code' => 'nullable|string|max:100',
            'product_data.barcode' => 'nullable|string|max:100',
            'product_data.prices' => 'nullable|array',
            'product_data.prices.*.cabinet_price_id' => 'required|string|uuid',
            'product_data.prices.*.amount' => 'required|numeric|min:0',
            'product_data.prices.*.currency_id' => 'required|string|uuid',
            'product_data.prices.*.sort' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Получить ID товара к сопоставлению из запроса
     *
     * @return string
     */
    public function getProductToMatchId(): string
    {
        return $this->validated('product_to_match_id');
    }

    /**
     * Получить данные товара из запроса
     *
     * @return array
     */
    public function getProductData(): array
    {
        return $this->validated('product_data');
    }
}
