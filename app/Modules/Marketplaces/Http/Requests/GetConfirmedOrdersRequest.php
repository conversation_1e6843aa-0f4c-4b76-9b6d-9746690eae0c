<?php

namespace App\Modules\Marketplaces\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetConfirmedOrdersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'status' => 'nullable|string|in:new,confirm,complete,cancel,receive,reject',
            'search' => 'nullable|string',
            'date_from' => 'nullable|date_format:Y-m-d H:i',
            'date_to' => 'nullable|date_format:Y-m-d H:i|after_or_equal:date_from',
            'sort_field' => 'nullable|string',
            'sort_direction' => 'nullable|string|in:asc,desc',
        ];
    }
}
