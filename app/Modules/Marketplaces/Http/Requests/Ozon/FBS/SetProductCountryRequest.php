<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBS;

use Illuminate\Foundation\Http\FormRequest;

class SetProductCountryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'product_id' => ['required', 'integer', 'min:1'],
            'country_iso_code' => ['required', 'string', 'size:2', 'regex:/^[A-Z]{2}$/'],
        ];
    }
}
