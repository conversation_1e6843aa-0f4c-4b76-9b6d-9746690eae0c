<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBS;

use App\Modules\Marketplaces\Services\Ozon\Enums\OrderStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetFBSOrdersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['integer', 'min:1'],
            'per_page' => ['integer', 'min:1', 'max:100'],
            'date_from' => ['date'],
            'date_to' => ['date', 'after_or_equal:date_from'],
            'search' => ['string', 'max:255'],
            'module_status' => ['string', 'in:new,confirmed'],
            'ozon_status' => ['string', Rule::in(OrderStatusEnum::cases())],
            'sort_field' => ['string', 'in:created_at,updated_at,number,total_price'],
            'sort_direction' => ['string', 'in:asc,desc'],
        ];
    }
}
