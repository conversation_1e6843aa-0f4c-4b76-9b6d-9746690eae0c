<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBS;

use Illuminate\Foundation\Http\FormRequest;

class CancelFBSOrderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cancel_reason_id' => ['required', 'integer'],
            'cancel_reason_message' => ['required_if:cancel_reason_id,402', 'string'],
        ];
    }
}
