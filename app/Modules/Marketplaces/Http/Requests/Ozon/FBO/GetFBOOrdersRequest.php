<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\FBO;

use App\Modules\Marketplaces\Services\Ozon\Enums\FBOOrderStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetFBOOrdersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['integer', 'min:1'],
            'per_page' => ['integer', 'min:1', 'max:100'],
            'date_from' => ['date'],
            'date_to' => ['date', 'after_or_equal:date_from'],
            'search' => ['string', 'max:255'],
            'ozon_status' => ['string', Rule::in(FBOOrderStatusEnum::cases())],
            'sort_field' => ['string', 'in:created_at,updated_at,number,total_price'],
            'sort_direction' => ['string', 'in:asc,desc'],
        ];
    }
}
