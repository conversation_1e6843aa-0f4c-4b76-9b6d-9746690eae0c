<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Carriages;

use Illuminate\Foundation\Http\FormRequest;

class GetCarriagesRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'integration_id' => ['required', 'uuid'],
            'status' => ['nullable', 'string', 'in:new,approved,confirmed'],
            'delivery_method_id' => ['nullable', 'uuid'],
            'departure_date_from' => ['nullable', 'date'],
            'departure_date_to' => ['nullable', 'date', 'after_or_equal:departure_date_from'],
            'page' => ['nullable', 'integer', 'min:1'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
        ];
    }
}
