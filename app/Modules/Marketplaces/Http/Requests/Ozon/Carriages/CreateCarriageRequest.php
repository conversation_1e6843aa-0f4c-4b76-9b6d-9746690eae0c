<?php

namespace App\Modules\Marketplaces\Http\Requests\Ozon\Carriages;

use Illuminate\Foundation\Http\FormRequest;

class CreateCarriageRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'delivery_method_id' => ['required', 'uuid'],
            'departure_date' => ['required', 'date', 'after_or_equal:today'],
        ];
    }
}
