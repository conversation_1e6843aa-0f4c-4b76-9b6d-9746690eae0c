<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries\Products;

use Illuminate\Foundation\Http\FormRequest;

class ManualMatchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_to_match_id' => [
                'required',
                'string',
                'uuid',
                'exists:wildberries_products_to_match,id',
            ],
            'product_id' => [
                'required',
                'string',
                'uuid',
                'exists:products,id',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'product_to_match_id.required' => 'ID товара к сопоставлению обязателен для заполнения.',
            'product_to_match_id.uuid' => 'ID товара к сопоставлению должен быть валидным UUID.',
            'product_to_match_id.exists' => 'Товар к сопоставлению не найден.',
            'product_id.required' => 'ID товара обязателен для заполнения.',
            'product_id.uuid' => 'ID товара должен быть валидным UUID.',
            'product_id.exists' => 'Товар не найден.',
        ];
    }
}
