<?php

namespace App\Modules\Marketplaces\Http\Requests\Wildberries\FBS\Orders;

use Illuminate\Foundation\Http\FormRequest;

class GetFBSOrdersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => ['integer', 'min:1'],
            'per_page' => ['integer', 'min:1', 'max:100'],
            'date_from' => ['date'],
            'date_to' => ['date', 'after_or_equal:date_from'],
            'search' => ['string', 'max:255'],
            'module_status' => ['string', 'in:new,confirmed,packed,canceled,delivered,returned'],
            'wb_status' => ['string', 'in:new,confirmed,packed,canceled,delivered,returned'],
            'delivery_type' => ['string', 'in:fbs,wbgo'],
            'sort_field' => ['string', 'in:created_at,updated_at,wb_number,total_price'],
            'sort_direction' => ['string', 'in:asc,desc'],
        ];
    }
}
