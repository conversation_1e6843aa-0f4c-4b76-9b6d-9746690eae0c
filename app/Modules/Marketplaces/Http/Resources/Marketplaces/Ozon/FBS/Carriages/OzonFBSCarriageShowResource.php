<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Carriages;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSCarriageShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор отгрузки */
            'id' => $this->id,
            /** @var string|null $ozon_carriage_id Идентификатор отгрузки в системе Ozon */
            'ozon_carriage_id' => $this->ozon_carriage_id,
            /** @var string $departure_date Дата отправления */
            'departure_date' => $this->departure_date,
            /** @var string $status Статус отгрузки */
            'status' => $this->status,
            /** @var string|null $ozon_status Статус отгрузки в системе Ozon */
            'ozon_status' => $this->ozon_status,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,

            /** @var array{is_synced: bool, sync_attempts: int, last_sync_attempt: string|null} $sync_info Информация о синхронизации */
            'sync_info' => [
                'is_synced' => (bool) $this->sync_info->is_synced,
                'sync_attempts' => (int) $this->sync_info->sync_attempts,
                'last_sync_attempt' => $this->sync_info->last_sync_attempt,
            ],

            /** @var array{documents_checked: bool, documents_status: string|null, documents_check_attempts: int, last_documents_check: string|null} $documents_info Информация о проверке документов */
            'documents_info' => [
                'documents_checked' => (bool) $this->documents_info->documents_checked,
                'documents_status' => $this->documents_info->documents_status,
                'documents_check_attempts' => (int) $this->documents_info->documents_check_attempts,
                'last_documents_check' => $this->documents_info->last_documents_check,
            ],

            /** @var array{waybill_checked: bool, waybill_status: string|null, waybill_check_attempts: int, last_waybill_check: string|null} $waybill_info Информация о накладной */
            'waybill_info' => [
                'waybill_checked' => (bool) $this->waybill_info->waybill_checked,
                'waybill_status' => $this->waybill_info->waybill_status,
                'waybill_check_attempts' => (int) $this->waybill_info->waybill_check_attempts,
                'last_waybill_check' => $this->waybill_info->last_waybill_check,
            ],

            /** @var array{id: string, name: string, ozon_delivery_method_id: int, status: string, cutoff: string|null, sla_cut_in: string|null, provider_id: int|null} $delivery_method Способ доставки */
            'delivery_method' => [
                'id' => $this->delivery_method->id,
                'name' => $this->delivery_method->name,
                'ozon_delivery_method_id' => (int) $this->delivery_method->ozon_delivery_method_id,
                'status' => $this->delivery_method->status,
                'cutoff' => $this->delivery_method->cutoff,
                'sla_cut_in' => $this->delivery_method->sla_cut_in,
                'provider_id' => $this->delivery_method->provider_id ? (int) $this->delivery_method->provider_id : null,
            ],

            /** @var array{id: string, name: string, ozon_warehouse_id: int} $warehouse Склад */
            'warehouse' => [
                'id' => $this->warehouse->id,
                'name' => $this->warehouse->name,
                'ozon_warehouse_id' => (int) $this->warehouse->ozon_warehouse_id,
            ],

            /** @var array{id: string, name: string} $integration Интеграция */
            'integration' => [
                'id' => $this->integration->id,
                'name' => $this->integration->name,
            ],

            /** @var array{is_partial: bool, partial_num: int|null, containers_count: int|null, ozon_created_at: string|null, ozon_updated_at: string|null}|null $ozon_info Информация из Ozon */
            'ozon_info' => $this->when($this->ozon_info, [
                'is_partial' => (bool) $this->ozon_info?->is_partial,
                'partial_num' => $this->ozon_info?->partial_num ? (int) $this->ozon_info->partial_num : null,
                'containers_count' => $this->ozon_info?->containers_count ? (int) $this->ozon_info->containers_count : null,
                'ozon_created_at' => $this->ozon_info?->ozon_created_at,
                'ozon_updated_at' => $this->ozon_info?->ozon_updated_at,
            ]),

            /** @var array|null $ozon_details Детальная информация из Ozon */
            'ozon_details' => $this->when($this->ozon_details, [
                'act_type' => $this->ozon_details?->act_type,
                'is_waybill_enabled' => (bool) ($this->ozon_details?->is_waybill_enabled ?? false),
                'is_econom' => (bool) ($this->ozon_details?->is_econom ?? false),
                'arrival_pass_ids' => $this->ozon_details?->arrival_pass_ids,
                'available_actions' => $this->ozon_details?->available_actions,
                'cancel_availability' => $this->ozon_details?->cancel_availability,
                'company_id' => $this->ozon_details?->company_id ? (int) $this->ozon_details->company_id : null,
                'first_mile_type' => $this->ozon_details?->first_mile_type,
                'has_postings_for_next_carriage' => (bool) ($this->ozon_details?->has_postings_for_next_carriage ?? false),
                'integration_type' => $this->ozon_details?->integration_type,
                'is_container_label_printed' => (bool) ($this->ozon_details?->is_container_label_printed ?? false),
                'retry_count' => $this->ozon_details?->retry_count ? (int) $this->ozon_details->retry_count : null,
                'tpl_provider_id' => $this->ozon_details?->tpl_provider_id ? (int) $this->ozon_details->tpl_provider_id : null,
                'ozon_warehouse_id' => $this->ozon_details?->ozon_warehouse_id ? (int) $this->ozon_details->ozon_warehouse_id : null,
            ]),

            /** @var array<array{carriage_posting_id: string, posting_number: string, status_at_creation: string, current_status: string, added_to_carriage_at: string, order: array, customer_order: array|null}> $postings Отправления в отгрузке */
            'postings' => collect($this->postings)->map(function ($posting) {
                return [
                    'carriage_posting_id' => $posting->carriage_posting_id,
                    'posting_number' => $posting->posting_number,
                    'status_at_creation' => $posting->status_at_creation,
                    'current_status' => $posting->current_status,
                    'added_to_carriage_at' => $posting->added_to_carriage_at,
                    'order' => [
                        'id' => $posting->order->id,
                        'total_price' => (string) $posting->order->total_price,
                        'created_at' => $posting->order->created_at,
                    ],
                    'customer_order' => $posting->customer_order ? [
                        'number' => $posting->customer_order->number,
                        'total_price' => (string) $posting->customer_order->total_price,
                    ] : null,
                ];
            })->toArray(),

            /** @var int $postings_count Количество отправлений */
            'postings_count' => (int) $this->postings_count,
        ];
    }
}
