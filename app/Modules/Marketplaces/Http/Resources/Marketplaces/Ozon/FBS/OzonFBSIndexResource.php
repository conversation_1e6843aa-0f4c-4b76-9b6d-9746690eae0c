<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS;

use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\OzonOrderItemResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->integration_id,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->warehouse_id,
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->currency_id,
            /** @var string $customer_order_id Идентификатор заказа в нашей системе */
            'customer_order_id' => $this->customer_order_id,
            /** @var string $module_status Статус заказа в нашей системе */
            'module_status' => $this->module_status,
            /** @var string $status Статус заказа */
            'status' => $this->status,
            /** @var string $substatus Подстатус заказа */
            'substatus' => $this->substatus,
            /** @var bool $has_unmatched_items Признак наличия несопоставленных товаров */
            'has_unmatched_items' => $this->has_unmatched_items,
            /** @var bool $needs_warehouse_mapping Признак необходимости сопоставления склада */
            'needs_warehouse_mapping' => $this->needs_warehouse_mapping,
            /** @var string $number Номер заказа в нашей системе */
            'number' => $this->number,
            /** @var string $posting_number Номер отправления в Ozon */
            'posting_number' => $this->posting_number,
            /** @var string $order_id ID заказа в Ozon */
            'order_id' => $this->order_id,
            /** @var string $order_number Номер заказа в Ozon */
            'order_number' => $this->order_number,
            /** @var string $parent_posting_number Номер родительского отправления в Ozon */
            'parent_posting_number' => $this->parent_posting_number,
            /** @var string $tracking_number Номер отслеживания */
            'tracking_number' => $this->tracking_number,
            /** @var string $comment Комментарий к заказу */
            'comment' => $this->comment,
            /** @var string $total_price Общая сумма заказа (строка для BcMath) */
            'total_price' => $this->total_price,
            /** @var bool $reserve Признак резервирования */
            'reserve' => $this->reserve,
            /** @var string $delivery_date Дата доставки */
            'delivery_date' => $this->delivery_date,
            /** @var string $delivery_type Тип доставки */
            'delivery_type' => $this->delivery_type,
            /** @var string $prr_option Опция PRR */
            'prr_option' => $this->prr_option,
            /** @var string $tpl_integration_type Тип интеграции TPL */
            'tpl_integration_type' => $this->tpl_integration_type,
            /** @var array $requirements Требования */
            'requirements' => $this->requirements && is_string($this->requirements) ? json_decode($this->requirements) : ($this->requirements ?? []),
            /** @var array $available_actions Доступные действия */
            'available_actions' => $this->available_actions,
            /** @var string $cutoff_date Дата отсечки */
            'cutoff_date' => $this->cutoff_date,
            /** @var int $ozon_warehouse_id Идентификатор склада Ozon */
            'ozon_warehouse_id' => $this->ozon_warehouse_id,
            /** @var string $delivering_date Дата передачи заказа в логистическую компанию */
            'delivering_date' => $this->delivering_date,
            /** @var string $in_process_at Дата и время обработки заказа */
            'in_process_at' => $this->in_process_at,
            /** @var string $pickup_code_verified_at Дата и время подтверждения кода самовывоза */
            'pickup_code_verified_at' => $this->pickup_code_verified_at,
            /** @var string $shipment_date Дата отгрузки */
            'shipment_date' => $this->shipment_date,
            /** @var int $timeslot_changes_count Количество изменений слота */
            'timeslot_changes_count' => $this->timeslot_changes_count,
            /** @var array $current_timeslot Текущий слот */
            'current_timeslot' => $this->current_timeslot,
            /** @var string $last_timeslot_change Дата и время последнего изменения слота */
            'last_timeslot_change' => $this->last_timeslot_change,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $deleted_at Дата и время удаления записи */
            'deleted_at' => $this->deleted_at,

            'items' => $this->when($this->items, function () {
                $items = $this->items && is_string($this->items) ? json_decode($this->items) : ($this->items ?? []);
                return OzonOrderItemResource::collection($items);
            }),
        ];
    }
}
