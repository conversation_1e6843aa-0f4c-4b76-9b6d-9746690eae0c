<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Carriages;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSCarriageArrivalPassesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $carriage_id Идентификатор отгрузки */
            'carriage_id' => $this->carriage_id,
            /** @var string|null $ozon_carriage_id Идентификатор отгрузки в системе Ozon */
            'ozon_carriage_id' => $this->ozon_carriage_id,
            /** @var string $departure_date Дата отправления */
            'departure_date' => $this->departure_date,
            /** @var string $status Статус отгрузки */
            'status' => $this->status,

            /** @var array{id: string, name: string, ozon_warehouse_id: int} $warehouse Склад */
            'warehouse' => [
                'id' => $this->warehouse->id,
                'name' => $this->warehouse->name,
                'ozon_warehouse_id' => (int) $this->warehouse->ozon_warehouse_id,
            ],

            /** @var array{id: string, name: string, ozon_delivery_method_id: int} $delivery_method Способ доставки */
            'delivery_method' => [
                'id' => $this->delivery_method->id,
                'name' => $this->delivery_method->name,
                'ozon_delivery_method_id' => (int) $this->delivery_method->ozon_delivery_method_id,
            ],

            /** @var array<array{id: string, ozon_pass_id: string|null, driver_name: string, driver_phone: string, vehicle_license_plate: string, vehicle_model: string|null, with_returns: bool, status: string, created_at: string, updated_at: string}> $arrival_passes Пропуски прибытия */
            'arrival_passes' => collect($this->arrival_passes)->map(function ($pass) {
                return [
                    'id' => $pass->id,
                    'ozon_pass_id' => $pass->ozon_pass_id,
                    'driver_name' => $pass->driver_name,
                    'driver_phone' => $pass->driver_phone,
                    'vehicle_license_plate' => $pass->vehicle_license_plate,
                    'vehicle_model' => $pass->vehicle_model,
                    'with_returns' => (bool) $pass->with_returns,
                    'status' => $pass->status,
                    'created_at' => $pass->created_at,
                    'updated_at' => $pass->updated_at,
                ];
            })->toArray(),

            /** @var int $passes_count Количество пропусков прибытия */
            'passes_count' => (int) $this->passes_count,
        ];
    }
}
