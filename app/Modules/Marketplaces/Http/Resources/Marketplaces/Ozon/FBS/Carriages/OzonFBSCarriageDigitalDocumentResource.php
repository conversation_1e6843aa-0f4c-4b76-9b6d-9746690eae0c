<?php

namespace App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Carriages;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonFBSCarriageDigitalDocumentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $carriage_id Идентификатор отгрузки */
            'carriage_id' => $this->carriage_id,
            /** @var string|null $ozon_carriage_id Идентификатор отгрузки в системе Ozon */
            'ozon_carriage_id' => $this->ozon_carriage_id,
            /** @var string $doc_type Тип документа (act_of_acceptance|act_of_mismatch|act_of_excess) */
            'doc_type' => $this->doc_type,
            /** @var string|null $file_content Содержимое файла документа в формате base64 */
            'file_content' => $this->file_content,
            /** @var string $file_name Имя файла документа */
            'file_name' => $this->file_name,
            /** @var string $content_type MIME-тип файла (обычно application/pdf) */
            'content_type' => $this->content_type,
        ];
    }
}
