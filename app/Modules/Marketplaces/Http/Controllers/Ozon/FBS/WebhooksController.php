<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon\FBS;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Services\Ozon\OzonWebhookService;
use App\Traits\OzonWebhookResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use InvalidArgumentException;

class WebhooksController extends Controller
{
    use OzonWebhookResponse;

    public function __construct(
        private readonly OzonWebhookService $webhookService
    ) {}

    public function __invoke(Request $request): JsonResponse
    {
        return $this->executeOzonAction(function () use ($request) {
            $messageType = $request->input('message_type');

            if (!$messageType) {
                return $this->ozonErrorResponse(
                    'message_type is required',
                    400,
                    'ERROR_PARAMETER_VALUE_MISSED'
                );
            }

            $validated = $this->validateByMessageType($request, $messageType);
            $result = $this->webhookService->processWebhook($validated);

            return $this->ozonSuccessResponse($result);
        });
    }

    private function validateByMessageType(Request $request, string $messageType): array
    {
        return match ($messageType) {
            'TYPE_PING' => $request->validate([
                'message_type' => 'required|string',
                'time' => 'required|string'
            ]),
            'TYPE_NEW_POSTING' => $request->validate([
                'message_type' => 'required|string',
                'posting_number' => 'required|string',

                'products' => 'required|array',
                'products.*.sku' => 'required|integer',
                'products.*.quantity' => 'required|integer',

                'in_process_at' => 'required',
                'warehouse_id' => 'required|integer',
                'seller_id' => 'required|integer|in:ozon_integrations,client_id'
            ]),
            'TYPE_STATE_CHANGED' => $request->validate([
                'message_type' => 'required|string',
                'posting_number' => 'required|string',
                'new_state' => 'required|string',
                'changed_state_date' => 'required|string',
                'warehouse_id' => 'required|integer',
                'seller_id' => 'required|in:ozon_integrations,client_id'
            ]),
            default => throw new InvalidArgumentException('Unknown message type')
        };
    }
}
