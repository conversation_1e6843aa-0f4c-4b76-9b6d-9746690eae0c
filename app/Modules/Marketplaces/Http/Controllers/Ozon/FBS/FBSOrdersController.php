<?php

namespace App\Modules\Marketplaces\Http\Controllers\Ozon\FBS;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\CancelFBSOrderItemsRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\CancelFBSOrderRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\GetFBSOrdersCancelReasonRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\GetFBSOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\GetPackageLabelsRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\FBS\SetOrderItemCountryRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetCutoffDateRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetDeliveredStatusRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetDeliveringStatusRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetLastMileStatusRequest;
use App\Modules\Marketplaces\Http\Requests\Ozon\Orders\SetTimeslotRequest;
use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders\OzonFBSOrderCancelReasonsResource;
use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders\OzonFBSOrderIndexCollection;
use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders\OzonFBSOrderPackageLabelsResource;
use App\Modules\Marketplaces\Http\Resources\Marketplaces\Ozon\FBS\Orders\OzonFBSOrderTimeslotRestrictionsResource;
use App\Modules\Marketplaces\Policies\Ozon\OzonPolicy;
use App\Modules\Marketplaces\Services\Ozon\Services\FBS\FBSOrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS заказами Ozon
 */
class FBSOrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly OzonPolicy $policy,
        private readonly FBSOrdersService $ordersService
    ) {
    }
    /**
     * Загрузка FBS заказов из системы Ozon
     *
     * Загружает заказы FBS (Fulfillment by Seller) из системы Ozon за указанный период.
     * Заказы сохраняются в локальную базу данных для дальнейшей обработки.
     * Рекомендуется загружать заказы регулярно для актуальности данных.
     *
     * @param LoadOrdersRequest $request Период загрузки (date_from, date_to)
     * @param string $integrationId Идентификатор интеграции Ozon
     * @return JsonResponse Статус выполнения операции
     */
    public function loadOrders(LoadOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $dateFrom = $request->validated('date_from');
            $dateTo = $request->validated('date_to');

            $this->ordersService->loadOrders($integrationId, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }

    /**
     * Отмена FBS заказа
     *
     * Отменяет заказ FBS в системе Ozon с указанием причины отмены.
     * Отмена возможна только для заказов в определенных статусах.
     *
     * @param CancelFBSOrderRequest $request Данные запроса с причиной отмены
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function cancelOrder(CancelFBSOrderRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelOrder(
                cancelReasonId: $validated['cancel_reason_id'],
                orderId: $orderId,
                cancelReasonMessage: $validated['cancel_reason_message'] ?? null,
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Подтверждение FBS заказа
     *
     * Подтверждает заказ FBS в системе Ozon.
     * После подтверждения заказ переходит в статус, позволяющий его обработку и отправку.
     *
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function confirmOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->confirmFBSOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Получение списка FBS заказов
     *
     * Возвращает список заказов FBS с пагинацией и фильтрацией.
     * Поддерживает фильтрацию по статусу, дате создания и другим параметрам.
     *
     * @param GetFBSOrdersRequest $request Параметры фильтрации и пагинации
     * @param string $integrationId Идентификатор интеграции Ozon
     * @return JsonResponse Список заказов с метаданными пагинации
     * @response OzonFBSOrderIndexCollection<OzonFBSOrderIndexResource>
     */
    public function indexFbs(GetFBSOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $filters = $request->validated();
            $orders = $this->ordersService->getOrders($integrationId, $filters);

            $collection = new OzonFBSOrderIndexCollection($orders);
            return $this->successResponse($collection->additional([
                'page' => $filters['page'] ?? 1,
                'per_page' => $filters['per_page'] ?? 15,
            ]));
        });
    }

    /**
     * Получение причин отмены FBS заказов
     *
     * Возвращает список доступных причин отмены для указанных отправлений.
     * Причины отмены зависят от статуса заказа и могут различаться
     * для разных типов отправлений.
     *
     * @param GetFBSOrdersCancelReasonRequest $request Данные запроса с ID интеграции и номерами отправлений
     * @response OzonFBSOrderCancelReasonsResource
     */
    public function getCancelReason(GetFBSOrdersCancelReasonRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->checkPermissionsToIntegration($data['integration_id']);

            $result = $this->ordersService->getCancelReasons($data['integration_id'], $data['related_posting_numbers']);

            return $this->successResponse(OzonFBSOrderCancelReasonsResource::make($result));
        });
    }

    /**
     * Отмена отдельных товаров в FBS заказе
     *
     * Позволяет отменить только часть товаров в заказе, а не весь заказ целиком.
     * Если отменяется часть товаров, количество в БД уменьшается на указанное количество.
     * Если отменяется всё количество товара, запись удаляется из БД.
     *
     * @param CancelFBSOrderItemsRequest $request Данные запроса с товарами для отмены
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function cancelOrderItems(CancelFBSOrderItemsRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelOrderItems(
                cancelReasonId: $validated['cancel_reason_id'],
                orderId: $orderId,
                cancelReasonMessage: $validated['cancel_reason_message'],
                items: $validated['items'],
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Установка страны происхождения товара
     *
     * Устанавливает страну происхождения для конкретного товара в заказе.
     * Необходимо для корректного оформления таможенных документов.
     *
     * @param SetOrderItemCountryRequest $request Данные запроса с кодом страны
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function setProductCountry(SetOrderItemCountryRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->setProductCountry(
                orderId: $orderId,
                productId: $validated['sku'],
                countryIsoCode: $validated['country_iso2'],
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Сборка заказа FBS
     *
     * Переводит заказ в статус "собран" после комплектации товаров на складе.
     * Необходимо выполнить перед отправкой заказа покупателю.
     *
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function collectOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->collectOrder(
                orderId: $orderId,
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Получение этикеток для FBS отправлений
     *
     * Возвращает PDF файл с этикетками для указанных заказов.
     * Максимум 20 заказов за один запрос. Все заказы должны принадлежать
     * одной интеграции. Этикетки используются для маркировки посылок.
     *
     * @param GetPackageLabelsRequest $request Список ID заказов (максимум 20)
     * @response OzonFBSOrderPackageLabelsResource
     */
    public function getPackageLabels(GetPackageLabelsRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $validated = $request->validated();

            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $result = $this->ordersService->getPackageLabels($validated['order_ids']);

            return $this->successResponse(OzonFBSOrderPackageLabelsResource::make($result));
        });
    }

    /**
     * Установка даты отсечки для заказа
     *
     * Устанавливает новую дату отсечки (cutoff date) для заказа FBS.
     * Дата отсечки определяет крайний срок обработки заказа.
     *
     * @param SetCutoffDateRequest $request Данные запроса с новой датой отсечки
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function setCutoffDate(SetCutoffDateRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $validated = $request->validated();

            $this->ordersService->setCutoffDate(
                orderId: $orderId,
                newCutoffDate: $validated['new_cutoff_date']
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Установка временного слота доставки
     *
     * Устанавливает новый временной слот доставки для заказа FBS.
     * Позволяет изменить время доставки в рамках доступных слотов.
     *
     * @param SetTimeslotRequest $request Данные запроса с новым временным слотом
     * @param string $orderId Идентификатор заказа FBS
     * @return JsonResponse Статус выполнения операции
     */
    public function setTimeslot(SetTimeslotRequest $request, string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $validated = $request->validated();

            $this->ordersService->setTimeslot(
                orderId: $orderId,
                newTimeslot: $validated['new_timeslot']
            );

            return $this->noContentResponse();
        });
    }

    /**
     * Получение ограничений на изменение временного слота доставки
     *
     * Возвращает информацию о возможности изменения временного слота доставки
     * для указанного заказа, включая список доступных слотов и ограничения.
     * Данные кэшируются на 5 минут для оптимизации производительности.
     *
     * @param string $orderId Идентификатор заказа
     * @response OzonFBSOrderTimeslotRestrictionsResource
     */
    public function getTimeslotChangeRestrictions(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $result = $this->ordersService->getTimeslotChangeRestrictions($orderId);
            return $this->successResponse(OzonFBSOrderTimeslotRestrictionsResource::make($result));
        });
    }

    /**
     * Установка статуса "В доставке"
     *
     * Переводит указанные заказы в статус "В доставке" (delivering).
     * Используется когда заказы переданы в службу доставки.
     *
     * @param SetDeliveringStatusRequest $request Список идентификаторов заказов
     * @param string $integrationId Идентификатор интеграции Ozon
     * @return JsonResponse Статус выполнения операции
     */
    public function setDeliveringStatus(SetDeliveringStatusRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToIntegration($integrationId);

            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $this->ordersService->setDeliveringStatus($integrationId, $validated['order_ids']);

            return $this->noContentResponse();
        });
    }

    /**
     * Установка статуса "Последняя миля"
     *
     * Переводит указанные заказы в статус "Последняя миля" (last mile).
     * Используется когда заказы находятся на финальном этапе доставки.
     *
     * @param SetLastMileStatusRequest $request Список идентификаторов заказов
     * @param string $integrationId Идентификатор интеграции Ozon
     * @return JsonResponse Статус выполнения операции
     */
    public function setLastMileStatus(SetLastMileStatusRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $validated = $request->validated();
            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $this->ordersService->setLastMileStatus($integrationId, $validated['order_ids']);

            return $this->noContentResponse();
        });
    }

    /**
     * Установка статуса "Доставлено"
     *
     * Переводит указанные заказы в статус "Доставлено" (delivered).
     * Используется когда заказы успешно доставлены покупателю.
     *
     * @param SetDeliveredStatusRequest $request Список идентификаторов заказов
     * @param string $integrationId Идентификатор интеграции Ozon
     * @return JsonResponse Статус выполнения операции
     */
    public function setDeliveredStatus(SetDeliveredStatusRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($integrationId, $request) {
            $validated = $request->validated();
            $this->policy->checkPermissionsToIntegration($integrationId);

            foreach ($validated['order_ids'] as $orderId) {
                $this->policy->checkPermissionsToOrder($orderId);
            }

            $this->ordersService->setDeliveredStatus($integrationId, $validated['order_ids']);

            return $this->noContentResponse();
        });
    }

}
