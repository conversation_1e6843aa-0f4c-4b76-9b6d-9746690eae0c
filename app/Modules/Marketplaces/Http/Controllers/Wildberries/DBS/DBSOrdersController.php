<?php

namespace App\Modules\Marketplaces\Http\Controllers\Wildberries\DBS;

use App\Http\Controllers\Controller;
use App\Modules\Marketplaces\Http\Requests\LoadOrdersRequest;
use App\Modules\Marketplaces\Http\Requests\Wildberries\DBS\Orders\GetDBSOrdersRequest;
use App\Modules\Marketplaces\Policies\Wildberries\WildberriesFBSPolicy;
use App\Modules\Marketplaces\Services\Wildberries\Services\DBSOrdersService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

/**
 * Контроллер для управления FBS заказами Wildberries
 */
class DBSOrdersController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WildberriesFBSPolicy $policy,
        private readonly DBSOrdersService $ordersService
    ) {
    }

    /**
     * Подтверждение заказа
     */
    public function confirm(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->confirmDBSOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Отмена заказа
     */
    public function cancel(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->cancelDBSOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Сборка DBS заказа и создание заказа в УТ
     */
    public function collect(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->collectDBSOrder($orderId);

            return $this->noContentResponse();
        });
    }

    /**
     * Загрузка DBS заказов
     */
    public function loadDBSOrders(LoadOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $dateFrom = $request->validated('date_from');
            $dateTo = $request->validated('date_to');

            $this->ordersService->loadDBSOrders($integrationId, $dateFrom, $dateTo);

            return $this->noContentResponse();
        });
    }



    public function indexDbs(GetDBSOrdersRequest $request, string $integrationId): JsonResponse
    {
        return $this->executeAction(function () use ($request, $integrationId) {
            $this->policy->checkPermissionsToIntegration($integrationId);

            $filters = $request->validated();
            $orders = $this->ordersService->getOrders($integrationId, $filters);

            return $this->successResponse([
                'orders' => $orders,
                'page' => $filters['page'] ?? 1,
                'per_page' => $filters['per_page'] ?? 15,
            ]);
        });
    }

    public function assembleDBSOrder(string $orderId): JsonResponse
    {
        return $this->executeAction(function () use ($orderId) {
            $this->policy->checkPermissionsToOrder($orderId);

            $this->ordersService->assembleDBSOrder($orderId);

            return $this->noContentResponse();
        });
    }
}
