<?php

namespace App\Modules\Marketplaces\Services\Wildberries\DTO;

class OrderDTO
{
    public function __construct(
        public string $id,
        public string $cabinetId,
        public string $integrationId,
        public string $deliveryType,
        public string $supplierStatus,
        public string $wbStatus,
        public string $number,
        public ?string $customerOrderId,
        public string $comment,
        public string $createdAt,
        public string $updatedAt,
        public ?string $deletedAt,
        public string $moduleStatus,
        public string $hasUnmatchedItems,
        public string $needsWarehouseMapping,
        public string $legalEntityId,
        public string $contractorId,
        public string $departmentId,
        public ?string $warehouseId,
        public string $cabinetCurrencyId,
        public int $wbNumber,
        public ?string $trackingNumber,
        public string $totalPrice,
        public bool $reserve,
        public ?string $deliveryDate,
        public string $wbWarehouseId,
        public string $officeList,
        public int $cargoType
    ) {
    }

    public static function fromArray(array $data): self
    {
        return new self(
            id: $data['id'],
            cabinetId: $data['cabinet_id'],
            integrationId: $data['integration_id'],
            deliveryType: $data['delivery_type'],
            supplierStatus: $data['supplier_status'],
            wbStatus: $data['wb_status'],
            number: $data['number'],
            customerOrderId: $data['customer_order_id'],
            comment: $data['comment'],
            createdAt: $data['created_at'],
            updatedAt: $data['updated_at'],
            deletedAt: $data['deleted_at'],
            moduleStatus: $data['module_status'],
            hasUnmatchedItems: $data['has_unmatched_items'],
            needsWarehouseMapping: $data['needs_warehouse_mapping'],
            legalEntityId: $data['legal_entity_id'],
            contractorId: $data['contractor_id'],
            departmentId: $data['department_id'],
            warehouseId: $data['warehouse_id'],
            cabinetCurrencyId: $data['currency_id'],
            wbNumber: $data['wb_number'],
            trackingNumber: $data['tracking_number'],
            totalPrice: $data['total_price'],
            reserve: $data['reserve'],
            deliveryDate: $data['delivery_date'],
            wbWarehouseId: $data['wb_warehouse_id'],
            officeList: $data['office_list'],
            cargoType: $data['cargo_type']
        );
    }
}
