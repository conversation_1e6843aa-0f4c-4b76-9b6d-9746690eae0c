<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Repositories;

use App\Contracts\Repositories\CustomerOrderDeliveryRepositoryContract;
use App\Contracts\Repositories\CustomerOrderItemsRepositoryContract;
use App\Contracts\Repositories\CustomerOrdersRepositoryContract;
use App\Modules\Marketplaces\Contracts\Wildberries\WildberriesOrdersRepositoryContract;
use App\Modules\Marketplaces\Services\Wildberries\Enums\OrderStatusEnum;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class WildberriesOrdersRepository implements WildberriesOrdersRepositoryContract
{
    private const ORDERS_TABLE = 'wildberries_fbs_orders';
    private const ITEMS_TABLE = 'wildberries_order_items';
    private const DELIVERY_INFO_TABLE = 'wildberries_order_delivery_infos';

    private CustomerOrdersRepositoryContract $customerOrdersRepository;
    private CustomerOrderItemsRepositoryContract $customerOrderItemsRepository;
    private CustomerOrderDeliveryRepositoryContract $customerOrderDeliveryRepository;

    public function __construct(
        CustomerOrdersRepositoryContract $customerOrdersRepository,
        CustomerOrderItemsRepositoryContract $customerOrderItemsRepository,
        CustomerOrderDeliveryRepositoryContract $customerOrderDeliveryRepository
    ) {
        $this->customerOrdersRepository = $customerOrdersRepository;
        $this->customerOrderItemsRepository = $customerOrderItemsRepository;
        $this->customerOrderDeliveryRepository = $customerOrderDeliveryRepository;
    }

    /**
     * Получить заказ по ID
     *
     * @param string $id ID заказа
     * @return object|null Заказ
     */
    public function show(string $id): ?object
    {
        return DB::table(self::ORDERS_TABLE)
            ->where('id', $id)
            ->first();
    }

    /**
     * Получить заказы по ID кабинета и фильтрам
     *
     * @param string $cabinetId ID кабинета
     * @param array $filters Фильтры
     * @return Collection Коллекция заказов
     */
    public function findByCabinetId(string $cabinetId, array $filters = []): Collection
    {
        $query = DB::table(self::ORDERS_TABLE)
            ->where('cabinet_id', $cabinetId);

        // Применяем фильтры
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('number', 'like', "%{$search}%")
                    ->orWhere('wb_number', 'like', "%{$search}%");
            });
        }

        // Сортировка и пагинация
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';
        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 15;

        return $query->orderBy($sortField, $sortDirection)
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();
    }

    /**
     * Получить заказы по ID интеграции
     *
     * @param string $integrationId ID интеграции
     * @param array $filters Фильтры
     * @return Collection Коллекция заказов
     */
    public function findByIntegrationId(string $integrationId, array $filters = []): Collection
    {
        $query = DB::table(self::ORDERS_TABLE)
            ->where('integration_id', $integrationId);

        // Применяем фильтры
        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        // Сортировка
        $sortField = $filters['sort_field'] ?? 'created_at';
        $sortDirection = $filters['sort_direction'] ?? 'desc';

        return $query->orderBy($sortField, $sortDirection)->get();
    }

    /**
     * Создать заказ
     *
     * @param array $data Данные заказа
     * @return string ID созданного заказа
     */
    public function create(array $data): string
    {
        $id = $data['id'] ?? Str::orderedUuid()->toString();
        $now = Carbon::now();

        $orderData = array_merge($data, [
            'id' => $id,
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        DB::table(self::ORDERS_TABLE)->insert($orderData);

        return $id;
    }

    /**
     * Создать несколько заказов
     *
     * @param array $data Массив данных заказов
     * @return bool Результат создания
     */
    public function insert(array $data): bool
    {
        return DB::table(self::ORDERS_TABLE)->insert($data);
    }

    /**
     * Обновить заказ
     *
     * @param string $id ID заказа
     * @param array $data Данные заказа
     * @return bool Результат обновления
     */
    public function update(string $id, array $data): bool
    {
        $data['updated_at'] = Carbon::now();

        return DB::table(self::ORDERS_TABLE)
            ->where('id', $id)
            ->update($data) > 0;
    }

    /**
     * Обновить статус заказа
     *
     * @param string $id ID заказа
     * @param string $status Новый статус
     * @return bool Результат обновления
     */
    public function updateStatus(string $id, string $status): bool
    {
        return $this->update($id, ['status' => $status]);
    }

    /**
     * Подтвердить заказ
     *
     * @param string $id ID заказа
     * @return bool Результат подтверждения
     */
    public function confirm(string $id): bool
    {
        // Получаем заказ
        $order = $this->show($id);
        if (!$order) {
            return false;
        }

        // Проверяем, что заказ не имеет несопоставленных товаров
        if ($order->has_unmatched_items) {
            // Если есть несопоставленные товары, не подтверждаем заказ
            Log::channel('wildberries')->warning('Cannot confirm order with unmatched items', [
                'order_id' => $id,
                'wb_number' => $order->wb_number,
            ]);
            return false;
        }

        // Обновляем статус заказа
        return $this->updateStatus($id, OrderStatusEnum::CONFIRMED->value);
    }

    /**
     * Собрать заказ и создать заказ в УТ
     *
     * @param string $id ID заказа
     * @param array $data Дополнительные данные для создания заказа в УТ
     * @return string|null ID созданного заказа в УТ
     */
    public function collect(string $id, array $data = []): ?string
    {
        // Получаем заказ
        $order = $this->show($id);
        if (!$order || $order->module_status !== OrderStatusEnum::CONFIRMED->value) {
            return null;
        }

        // Получаем товары заказа
        $items = DB::table(self::ITEMS_TABLE)
            ->where('wildberries_order_id', $id)
            ->get();

        // Получаем информацию о доставке
        $delivery = DB::table(self::DELIVERY_INFO_TABLE)
            ->where('wildberries_order_id', $id)
            ->first();

        // Создаем заказ в УТ
        $customerOrderId = $this->createCustomerOrder($order, $items, $delivery, $data);
        if (!$customerOrderId) {
            return null;
        }

        // Обновляем статус заказа
        $this->updateStatus($id, OrderStatusEnum::PACKED->value);

        return $customerOrderId;
    }

    /**
     * Создать заказ в УТ
     *
     * @param object $order Заказ Wildberries
     * @param Collection $items Товары заказа
     * @param object|null $delivery Информация о доставке
     * @param array $data Дополнительные данные
     * @return string|null ID созданного заказа в УТ
     */
    private function createCustomerOrder(object $order, Collection $items, ?object $delivery, array $data = []): ?string
    {
        try {
            // Получаем дату создания заказа в Wildberries
            $orderDate = Carbon::parse($order->created_at)->format('Y-m-d H:i:s');

            // Определяем схему работы
            $workScheme = $data['work_scheme'] ?? 'regular';

            // Определяем склады
            $sourceWarehouseId = $data['source_warehouse_id'] ?? $order->warehouse_id;
            $targetWarehouseId = $data['target_warehouse_id'] ?? null;

            // Определяем статус заказа в УТ в зависимости от схемы работы
            $statusId = null;
            if ($workScheme === 'order') {
                // Для ордерной схемы
                $statusId = DB::table('customer_order_statuses')
                    ->where('cabinet_id', $order->cabinet_id)
                    ->where('name', 'Отгружается')
                    ->value('id');
            } else {
                // Для обычной схемы
                $statusId = DB::table('customer_order_statuses')
                    ->where('cabinet_id', $order->cabinet_id)
                    ->where('name', 'Доставка до МП')
                    ->value('id');
            }

            // Подготавливаем данные для создания заказа
            $customerOrderData = [
                'id' => Str::orderedUuid()->toString(),
                'cabinet_id' => $order->cabinet_id,
                'employee_id' => $order->employee_id,
                'department_id' => $order->department_id,
                'is_common' => false,
                'number' => $order->number,
                'date_from' => $orderDate, // Используем дату создания заказа в Wildberries
                'payment_status' => 'unpaid',
                'status_id' => $statusId ?? $data['status_id'] ?? null,
                'held' => false,
                'reserve' => $order->reserve ?? true,
                'legal_entity_id' => $order->legal_entity_id,
                'contractor_id' => $order->contractor_id,
                'plan_date' => $order->delivery_date ?? null,
                'sales_channel_id' => null,
                'warehouse_id' => $sourceWarehouseId, // Склад, с которого отгружается товар
                'currency_id' => $order->currency_id,
                'currency_value' => 1,
                'total_price' => $order->total_price,
                'comment' => $this->generateOrderComment($order, $targetWarehouseId),
                'operation_type' => 'передача на комиссию', // Тип операции
                'target_warehouse_id' => $targetWarehouseId, // Склад МП, на который отгружается товар
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];

            // Создаем заказ в транзакции
            DB::beginTransaction();

            // Создаем заказ
            $this->customerOrdersRepository->create($customerOrderData);

            // Создаем товары заказа
            foreach ($items as $item) {
                $orderItemData = [
                    'id' => Str::orderedUuid()->toString(),
                    'order_id' => $customerOrderData['id'],
                    'product_id' => $item->product_id,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'vat_rate_id' => null,
                    'discount' => $item->discount ?? 0,
                    'total_price' => $item->total_price,
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];

                $this->customerOrderItemsRepository->create($orderItemData);
            }

            // Создаем информацию о доставке
            if ($delivery) {
                $deliveryData = [
                    'id' => Str::orderedUuid()->toString(),
                    'order_id' => $customerOrderData['id'],
                    'comment' => $delivery->comment ?? null,
                    'other' => $delivery->other ?? null,
                    'city' => $delivery->city ?? null,
                    'street' => $delivery->street ?? null,
                    'house' => $delivery->house ?? null,
                    'apartment' => $delivery->apartment ?? null,
                    'postal_code' => $delivery->postal_code ?? null,
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];

                $this->customerOrderDeliveryRepository->create($deliveryData);
            }

            // Создаем перемещение товара между складами
            if ($targetWarehouseId) {
                $movementId = Str::orderedUuid()->toString();

                // Создаем запись о перемещении
                DB::table('warehouse_movements')->insert([
                    'id' => $movementId,
                    'cabinet_id' => $order->cabinet_id,
                    'employee_id' => $order->employee_id,
                    'department_id' => $order->department_id,
                    'source_warehouse_id' => $sourceWarehouseId,
                    'target_warehouse_id' => $targetWarehouseId,
                    'date_from' => $orderDate,
                    'number' => "WB-{$order->wb_number}",
                    'comment' => "Перемещение товара на склад МП по заказу Wildberries #{$order->wb_number}",
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ]);

                // Создаем связь между заказом и перемещением
                DB::table('customer_order_movements')->insert([
                    'id' => Str::orderedUuid()->toString(),
                    'order_id' => $customerOrderData['id'],
                    'movement_id' => $movementId,
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ]);

                // Добавляем товары в перемещение
                foreach ($items as $item) {
                    DB::table('warehouse_movement_items')->insert([
                        'id' => Str::orderedUuid()->toString(),
                        'movement_id' => $movementId,
                        'product_id' => $item->product_id,
                        'quantity' => $item->quantity,
                        'created_at' => Carbon::now()->toDateTimeString(),
                        'updated_at' => Carbon::now()->toDateTimeString(),
                    ]);
                }
            }

            DB::commit();

            return $customerOrderData['id'];
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error creating customer order from Wildberries order', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $order->id,
            ]);

            return null;
        }
    }

    /**
     * Сгенерировать комментарий для заказа в УТ
     *
     * @param object $order Заказ Wildberries
     * @param string|null $targetWarehouseId ID склада назначения
     * @return string Комментарий
     */
    private function generateOrderComment(object $order, ?string $targetWarehouseId = null): string
    {
        $comment = "Заказ Wildberries #{$order->wb_number}";

        // Добавляем дату доставки на склад МП
        if (isset($order->delivery_date)) {
            $comment .= "\nДата доставки на склад МП: " . Carbon::parse($order->delivery_date)->format('d.m.Y');
        }

        // Добавляем номер отправления
        if (isset($order->tracking_number)) {
            $comment .= "\nНомер отправления: {$order->tracking_number}";
        }

        // Добавляем информацию о складе назначения
        if ($targetWarehouseId) {
            $targetWarehouse = DB::table('warehouses')
                ->where('id', $targetWarehouseId)
                ->first();

            if ($targetWarehouse) {
                $comment .= "\nСклад назначения: {$targetWarehouse->name}";
            }
        }

        // Добавляем информацию о дате создания заказа в Wildberries
        if (isset($order->created_at)) {
            $comment .= "\nДата создания заказа в Wildberries: " . Carbon::parse($order->created_at)->format('d.m.Y H:i:s');
        }

        // Добавляем информацию о типе операции
        $comment .= "\nТип операции: передача на комиссию";

        return $comment;
    }

    /**
     * Получить заказы по статусу
     *
     * @param string $cabinetId ID кабинета
     * @param string $status Статус заказа
     * @return Collection Коллекция заказов
     */
    public function findByStatus(string $cabinetId, string $status): Collection
    {
        return DB::table(self::ORDERS_TABLE)
            ->where('cabinet_id', $cabinetId)
            ->where('status', $status)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Получить новые заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция новых заказов
     */
    public function findNewOrders(string $cabinetId): Collection
    {
        return $this->findByStatus($cabinetId, OrderStatusEnum::NEW->value);
    }

    /**
     * Получить заказы, ожидающие сборки
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция заказов, ожидающих сборки
     */
    public function findAwaitingCollectionOrders(string $cabinetId): Collection
    {
        return $this->findByStatus($cabinetId, OrderStatusEnum::CONFIRMED->value);
    }

    /**
     * Получить заказы, ожидающие отгрузки
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция заказов, ожидающих отгрузки
     */
    public function findAwaitingShipmentOrders(string $cabinetId): Collection
    {
        return $this->findByStatus($cabinetId, OrderStatusEnum::PACKED->value);
    }

    /**
     * Получить спорные заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция спорных заказов
     */
    public function findDisputedOrders(string $cabinetId): Collection
    {
        // Здесь можно определить, какие заказы считаются спорными
        // Например, заказы с несопоставленными товарами
        return DB::table(self::ORDERS_TABLE . ' as o')
            ->join(self::ITEMS_TABLE . ' as i', 'o.id', '=', 'i.order_id')
            ->where('o.cabinet_id', $cabinetId)
            ->where('i.is_matched', false)
            ->select('o.*')
            ->distinct()
            ->orderBy('o.created_at', 'desc')
            ->get();
    }

    /**
     * Получить заказы в доставке
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция заказов в доставке
     */
    public function findDeliveringOrders(string $cabinetId): Collection
    {
        return $this->findByStatus($cabinetId, OrderStatusEnum::DELIVERING->value);
    }

    /**
     * Получить доставленные заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция доставленных заказов
     */
    public function findDeliveredOrders(string $cabinetId): Collection
    {
        return $this->findByStatus($cabinetId, OrderStatusEnum::DELIVERED->value);
    }

    /**
     * Получить отмененные заказы
     *
     * @param string $cabinetId ID кабинета
     * @return Collection Коллекция отмененных заказов
     */
    public function findCanceledOrders(string $cabinetId): Collection
    {
        return $this->findByStatus($cabinetId, OrderStatusEnum::CANCELED->value);
    }
}
