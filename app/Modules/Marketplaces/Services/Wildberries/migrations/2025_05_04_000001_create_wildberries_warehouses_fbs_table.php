<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use App\Modules\Marketplaces\Services\Wildberries\Enums\FBSWarehouseStatusEnum;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wildberries_warehouses_fbs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            $table->foreignUuid('cabinet_id')->constrained()->cascadeOnDelete();

            $table->foreignUuid('wildberries_integration_id')->references('id')->on('wildberries_integrations')->cascadeOnDelete();

            $table->string('status')->default(FBSWarehouseStatusEnum::WAITING->value); //статус сопоставления
            $table->string('name')->nullable(); // Название склада

            $table->integer('office_id'); // ID склада WB
            $table->integer('seller_warehouse_id'); // ID склада продавца
            $table->foreignUuid('warehouse_id')->constrained(); // ID склада в системе

            /**
             * Enum: 1 2 3
             * Тип товара, который принимает склад:
             * 1 - МГТ (малогабаритный, то есть обычный товар)
             * 2 - СГТ (Сверхгабаритный товар)
             * 3 - КГТ+ (Крупногабаритный товар)
             */
            $table->integer('cargo_type');

            /**
             * Enum: 1 2 3
             * Тип доставки:
             * 1 - доставка на склад WB (FBS)
             * 2 - доставка силами продавца (DBS)
             * 3 - доставка курьером WB (DBW)
             */
            $table->integer('delivery_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wildberries_warehouses_fbs');
    }
};
