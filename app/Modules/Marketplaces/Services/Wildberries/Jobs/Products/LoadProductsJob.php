<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Products;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Services\Api\RateLimiter\Facades\RateLimiter;
use App\Traits\HasOrderedUuid;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Job для загрузки товаров из Wildberries
 */
class LoadProductsJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use HasOrderedUuid;

    private API $api;

    /**
     * @throws WBSellerException
     */
    public function __construct(
        private readonly string $cabinetId,
        private readonly string $token,
        private readonly string $integrationId,
    ) {
        $this->api = new API(['masterkey' => $this->token]);
    }

    /**
     * @throws BindingResolutionException|\Throwable
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $products = $this->fetchProductsFromWildberries();
            $this->processProducts($products);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @throws Exception
     */
    private function fetchProductsFromWildberries(): Collection
    {
        $products = collect();

        // Применяем рейт-лимитер перед запросом
        RateLimiter::throttle('content');

        try {
            // Получаем все товары из Wildberries
            $request = $this->api->content()->getCardsList();

            // Если нет товаров, возвращаем пустую коллекцию
            if (empty($request->cards)) {
                return collect();
            }

            $products->push($request->cards);
            $totalRemaining = $request->cursor->total - 100;

            while ($totalRemaining > 0) {
                // Применяем рейт-лимитер перед каждым запросом
                RateLimiter::throttle('content');

                try {
                    $request = $this->api->content()->getCardsList(
                        updatedAt: $request->cursor->updatedAt,
                        nmId: $request->cursor->nmID
                    );

                    $products->push($request->cards);
                    $totalRemaining -= count($request->cards);
                } catch (Exception $e) {
                    // Если получили ошибку 409, регистрируем её в рейт-лимитере
                    if ($e->getCode() == 409) {
                        RateLimiter::registerError409('content');
                    }
                    Log::error('Error fetching products from Wildberries', [
                        'cabinet_id' => $this->cabinetId,
                        'error' => $e->getMessage(),
                        'cursor' => [
                            'updatedAt' => $request->cursor->updatedAt ?? null,
                            'nmID' => $request->cursor->nmID ?? null
                        ]
                    ]);
                    // Пробуем продолжить с следующей страницей
                    if (isset($request->cursor->nmID)) {
                        $totalRemaining -= 100; // Примерно уменьшаем оставшееся количество
                    } else {
                        break; // Если нет курсора, прерываем цикл
                    }
                }
            }
        } catch (Exception $e) {
            // Если получили ошибку 409, регистрируем её в рейт-лимитере
            if ($e->getCode() == 409) {
                RateLimiter::registerError409('content');
            }
            Log::error('Error fetching initial products from Wildberries', [
                'cabinet_id' => $this->cabinetId,
                'error' => $e->getMessage()
            ]);
            // Возвращаем пустую коллекцию в случае ошибки
            return collect();
        }

        return $products->flatten(1);
    }

    /**
     * @throws Exception
     */
    private function processProducts(Collection $products): void
    {
        // Если нет товаров для обработки, выходим
        if ($products->isEmpty()) {
            return;
        }

        // Получаем все товары из кабинета
        $userProducts = DB::table('products')
            ->where('cabinet_id', $this->cabinetId)
            ->get();

        // Получаем список уже сопоставленных товаров
        $existingMatchedProducts = DB::table('wildberries_matched_products')
            ->where('cabinet_id', $this->cabinetId)
            ->where('wildberries_integration_id', $this->integrationId)
            ->get();

        // Создаем карту соответствий внешних ID и ID размеров для сопоставленных товаров
        $existingMatchedIds = [];
        foreach ($existingMatchedProducts as $matchedProduct) {
            $key = "{$matchedProduct->wb_id}-{$matchedProduct->size_id}";
            $existingMatchedIds[$key] = $matchedProduct->id;
        }

        // Получаем список товаров к сопоставлению
        $existingProductsToMatch = DB::table('wildberries_products_to_match')
            ->where('cabinet_id', $this->cabinetId)
            ->where('wildberries_integration_id', $this->integrationId)
            ->get();

        // Создаем карту соответствий внешних ID и ID размеров для товаров к сопоставлению
        $existingProductsToMatchIds = [];
        foreach ($existingProductsToMatch as $productToMatch) {
            $key = "{$productToMatch->wb_id}-{$productToMatch->size_id}";
            $existingProductsToMatchIds[$key] = $productToMatch->id;
        }

        // Получаем цены товаров
        $pricesMap = $this->fetchPricesFromWildberries($products->pluck('nmID')->toArray());

        // Обрабатываем каждый товар из Wildberries
        foreach ($products as $wbProduct) {
            foreach ($wbProduct->sizes as $size) {
                // Формируем ключ для проверки существования товара
                $key = "{$wbProduct->nmID}-{$size->chrtID}";

                // Если товар уже сопоставлен, пропускаем его
                if (isset($existingMatchedIds[$key])) {
                    continue;
                }

                // Если товар уже добавлен в список товаров к сопоставлению, пропускаем его
                if (isset($existingProductsToMatchIds[$key])) {
                    continue;
                }

                // Получаем цену товара
                $price = null;
                $discountPrice = null;
                foreach ($pricesMap as $priceKey => $priceData) {
                    if (strpos($priceKey, "{$wbProduct->nmID}-") === 0) {
                        $price = $priceData['price'];
                        $discountPrice = $priceData['discount_price'];
                        break;
                    }
                }

                // Сохраняем товар в таблицу товаров к сопоставлению
                $uuid = Str::orderedUuid()->toString();

                DB::table('wildberries_products_to_match')->insert([
                    'id' => $uuid,
                    'cabinet_id' => $this->cabinetId,
                    'wildberries_integration_id' => $this->integrationId,
                    'wb_id' => $wbProduct->nmID,
                    'vendor_code' => $wbProduct->vendorCode,
                    'title' => $wbProduct->title,
                    'description' => $wbProduct->description,
                    'size_id' => $size->chrtID,
                    'size_name' => $size->wbSize,
                    'tech_size' => $size->techSize,
                    'skus' => json_encode($size->skus, JSON_THROW_ON_ERROR),
                    'price' => $price,
                    'discount_price' => $discountPrice,
                    'is_matched' => false,
                    'suggested_matches' => null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Получает цены товаров из Wildberries
     */
    private function fetchPricesFromWildberries(array $nmIds): array
    {
        if (empty($nmIds)) {
            return [];
        }

        $pricesMap = [];

        try {
            // Выполняем запросы с пагинацией
            $offset = 1;
            $limit = 1000;
            $hasMoreData = true;

            while ($hasMoreData) {
                // Применяем рейт-лимитер перед запросом
                RateLimiter::throttle('prices');

                try {
                    // Получаем цены для текущей партии товаров
                    $response = $this->api->Prices()->getPrices($offset, $limit);

                    if (empty($response->data->listGoods)) {
                        // Если нет данных, значит мы достигли конца списка
                        $hasMoreData = false;
                        continue;
                    }

                    // Обрабатываем ответ и формируем массив цен
                    foreach ($response->data->listGoods as $good) {
                        // Проверяем, что этот товар есть в нашем списке nmIds
                        if (!in_array($good->nmID, $nmIds)) {
                            continue;
                        }

                        if (empty($good->sizes)) {
                            continue;
                        }

                        // Обрабатываем все размеры товара
                        foreach ($good->sizes as $size) {
                            // Формируем уникальный ключ для каждого размера
                            $key = "{$good->nmID}-{$size->sizeID}";

                            $pricesMap[$key] = [
                                'price' => $size->price,
                                'discount_price' => $size->discountedPrice,
                            ];
                        }
                    }

                    // Увеличиваем смещение для следующего запроса
                    $offset += $limit;
                } catch (Exception $e) {
                    // Если получили ошибку 409, регистрируем её в рейт-лимитере
                    if ($e->getCode() == 409) {
                        RateLimiter::registerError409('prices');
                    }
                    Log::error('Error fetching prices from Wildberries', [
                        'cabinet_id' => $this->cabinetId,
                        'error' => $e->getMessage(),
                        'offset' => $offset,
                        'limit' => $limit
                    ]);
                    // Если произошла ошибка, увеличиваем смещение и продолжаем
                    $offset += $limit;
                }
            }

            return $pricesMap;
        } catch (Exception $e) {
            // В случае ошибки возвращаем пустой массив
            throw new \RuntimeException('Error fetching prices from Wildberries: ' . $e->getMessage());
        }
    }
}
