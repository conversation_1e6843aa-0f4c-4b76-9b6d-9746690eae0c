<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Jobs\Stocks;

use Exception;
use App\Clients\WB\API;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Clients\WB\Exception\WBSellerException;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Services\Api\RateLimiter\Facades\RateLimiter;
use Throwable;

/**
 * Job для синхронизации остатков с Wildberries
 */
class SyncStocksJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    
    private API $api;

    /**
     * @throws WBSellerException
     */
    public function __construct(
        private readonly string $integrationId,
        private readonly string $token
    ) {
        $this->api = new API(['masterkey' => $this->token]);
    }

    /**
     * @throws BindingResolutionException
     * @throws Exception|Throwable
     */
    public function handle(): void
    {
        try {
            DB::beginTransaction();

            $integratedProducts = DB::table('wildberries_matched_products')
                ->join('products', 'wildberries_matched_products.product_id', '=', 'products.id')
                ->where('wildberries_integration_id', $this->integrationId)
                ->select('wildberries_matched_products.*', 'products.article', 'products.code')
                ->get();

            $warehouses = DB::table('wildberries_warehouses_fbs')
                ->where('wildberries_integration_id', $this->integrationId)
                ->get();

            $stocksByWarehouse = [];

            foreach ($integratedProducts as $product) {
                foreach ($warehouses as $warehouse) {
                    $warehouseId = $warehouse['warehouse_id'];
                    $wbWarehouseId = $warehouse['seller_warehouse_id'];

                    // Получаем остаток товара на складе
                    $stockQuantity = $this->getProductStock($product->product_id, $warehouseId);

                    $quantityToSend = $stockQuantity;

                    $skus = $this->getProductSkus($product);

                    if (!empty($skus)) {

                        if (!isset($stocksByWarehouse[$wbWarehouseId])) {
                            $stocksByWarehouse[$wbWarehouseId] = [];
                        }

                        // Первый штрихкод (главный) получает реальное количество
                        $mainSku = array_shift($skus);
                        $stocksByWarehouse[$wbWarehouseId][] = [
                            'sku' => $mainSku,
                            'quantity' => $quantityToSend
                        ];

                        // Остальные штрихкоды устанавливаем в 0
                        foreach ($skus as $sku) {
                            $stocksByWarehouse[$wbWarehouseId][] = [
                                'sku' => $sku,
                                'quantity' => 0
                            ];
                        }
                    }
                }
            }

            foreach ($stocksByWarehouse as $wbWarehouseId => $stocks) {

                $chunks = array_chunk($stocks, 1000);

                foreach ($chunks as $chunk) {
                    RateLimiter::throttle('marketplace');

                    try {
                        $this->api->Marketplace()->updateWarehouseStocks(
                            (int)$wbWarehouseId,
                            ['stocks' => $chunk]
                        );

                    } catch (Exception $e) {
                        if ($e->getCode() == 409) {
                            RateLimiter::registerError409('marketplace');
                        }
                        throw $e;
                    }
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Получает текущий остаток товара на складе
     *
     * @param string $productId ID товара
     * @param string $warehouseId ID склада
     * @return int Количество товара на складе
     */
    private function getProductStock(string $productId, string $warehouseId): int
    {
        $stock = DB::table('warehouse_items')
            ->where('product_id', $productId)
            ->where('warehouse_id', $warehouseId)
            ->where('status', 'in_stock') // Только товары в наличии
            ->sum('quantity');

        return (int)$stock;
    }

    /**
     * Получает все штрихкоды товара для Wildberries
     *
     * @param object $product Товар
     * @return array Массив штрихкодов товара
     */
    private function getProductSkus(object $product): array
    {
        return DB::table('barcodes')
            ->where('barcodable_id', $product->product_id)
            ->where('barcodable_type', 'products')
            ->pluck('value')
            ->unique('value')
            ->toArray();
    }
}
