<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services\FBS;

use App\Clients\WB\Exception\ApiClientException;
use App\Clients\WB\Exception\ApiTimeRestrictionsException;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies\CompleteSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies\CreateSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies\DeleteSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Jobs\Orders\SyncSuppliesJob;
use Illuminate\Support\Facades\DB;
use Queue;
use RuntimeException;

/**
 * Сервис для работы с поставками Wildberries
 */
class SuppliesService
{
    /**
     * Завершение поставки
     *
     * @throws NotFoundException Если поставка не найдена
     * @throws WBSellerException При ошибке API Wildberries
     * @throws RuntimeException При ошибках валидации
     */
    public function toDelivery(string $supplyId): void
    {
        app(CompleteSupplyAction::class)->run($supplyId);
    }

    public function index(string $integrationId, ?array $filters): array
    {
        $query = DB::table('wildberries_supplies')
            ->where('integration_id', $integrationId)
            ->when(isset($filters['status']), function ($query) use ($filters) {
                switch ($filters['status']) {
                    case 'active':
                    case 'new':
                        $query->whereNull('closed_at')->where('done', false);
                        break;
                    case 'closed':
                        $query->whereNotNull('closed_at');
                        break;
                    case 'done':
                        $query->where('done', true);
                        break;
                }
            })
            ->when(isset($filters['date_from']), fn ($query) => $query->where('created_at', '>=', $filters['date_from']))
            ->when(isset($filters['date_to']), fn ($query) => $query->where('created_at', '<=', $filters['date_to']))
            ->when(isset($filters['search']), function ($query) use ($filters) {
                $query->where('name', 'ilike', '%'.$filters['search'].'%');
            });

        $page = $filters['page'] ?? 1;
        $perPage = $filters['per_page'] ?? 15;

        $supplies = $query->orderBy(
            $filters['sort_field'] ?? 'created_at',
            $filters['sort_direction'] ?? 'desc'
        )
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        return [
            'data' => $supplies,
            'page' => $page,
            'per_page' => $perPage,
        ];
    }

    /**
     * @throws NotFoundException
     * @throws WBSellerException
     * @throws ApiClientException
     * @throws ApiTimeRestrictionsException
     */
    public function createSupply(string $name, string $integrationId): string
    {
        $integration = DB::table('wildberries_integrations')
            ->where('id', $integrationId)
            ->first();

        if (!$integration) {
            throw new NotFoundException('Интеграция не найдена');
        }

        $token = decrypt($integration->token);
        return (new CreateSupplyAction())->run($name, $integrationId, $integration->cabinet_id, $token);
    }

    public function show(string $supplyId): object
    {
        $supply = DB::table('wildberries_supplies as ws')
            ->where('ws.id', $supplyId)
            ->select([
                'ws.*',

                        // Заказы в коробках
                DB::raw("(
                    SELECT COALESCE(JSON_AGG(boxes), '[]') FROM (
                        SELECT
                            wb.id,
                            wb.created_at,
                            wb.updated_at,
                            \"wb\".\"trbxIds\",
                            (
                                SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
                                    'id', wfo.id
                                )), '[]')
                                FROM wildberries_box_orders wbo
                                JOIN wildberries_fbs_orders wfo ON wfo.id = wbo.order_id
                                WHERE wbo.box_id = wb.id
                            ) AS orders
                        FROM wildberries_boxes wb
                        WHERE wb.supply_id = ws.id
                    ) AS boxes
                ) AS boxes"),

                        // Заказы напрямую в поставке (не в коробках)
                DB::raw("(
                    SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
                        'id', wfo.id
                    )), '[]')
                    FROM wildberries_supply_orders wso
                    JOIN wildberries_fbs_orders wfo ON wfo.id = wso.order_id
                    WHERE wso.supply_id = ws.id
                      AND wfo.id NOT IN (
                          SELECT wbo.order_id
                          FROM wildberries_box_orders wbo
                      )
                ) AS orders"),
            ])
            ->first();
        if ($supply) {
            $supply->orders = json_decode($supply->orders, true);
            $supply->boxes = json_decode($supply->boxes, true);
        }
        return $supply;
    }

    public function delete(string $supplyId): void
    {
        (new DeleteSupplyAction())->run($supplyId);
    }

    /**
     * @throws WBSellerException
     */
    public function loadSupplies(string $integrationId): void
    {
        $integration = DB::table('wildberries_integrations')
            ->where('id', $integrationId)
            ->select(['id','token','cabinet_id'])
            ->first();

        $token = decrypt($integration->token);
        Queue::push(
            new SyncSuppliesJob(
                integrationId: $integrationId,
                token: $token,
                cabinetId: $integration->cabinet_id
            )
        );
    }
}
