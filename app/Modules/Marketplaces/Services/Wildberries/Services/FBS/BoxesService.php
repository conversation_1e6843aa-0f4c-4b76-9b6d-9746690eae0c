<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Services\FBS;

use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\AddOrdersToBoxInSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\CreateBoxInSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\DeleteBoxInSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\DeleteOrderFromBoxInSupplyAction;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\GetBoxStickersAction;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для работы с коробками в поставках Wildberries
 */
class BoxesService
{
    /**
     * Получение списка коробок в поставке
     */
    public function getSupplyBoxes(string $supplyId): Collection
    {
        return DB::table('wildberries_boxes')
            ->where('supply_id', $supplyId)
            ->get();
    }

    /**
     * Создание коробок в поставке
     *
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function createBoxes(string $supplyId, int $amount): array
    {
        return (new CreateBoxInSupplyAction())->run($supplyId, $amount);
    }

    /**
     * Удаление коробок из поставки
     *
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function deleteBoxes(string $supplyId, array $boxIds): void
    {
        (new DeleteBoxInSupplyAction())->run($supplyId, $boxIds);
    }

    /**
     * Добавление заказов в коробку
     *
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function addOrdersToBox(string $supplyId, string $boxId, array $orderIds): void
    {
        (new AddOrdersToBoxInSupplyAction())->run($supplyId, $boxId, $orderIds);
    }

    /**
     * Удаление заказа из коробки
     *
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function deleteOrderFromBox(string $supplyId, string $boxId, string $orderId): void
    {
        (new DeleteOrderFromBoxInSupplyAction())->run($supplyId, $boxId, $orderId);
    }

    /**
     * Получение стикеров для коробок
     *
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function getBoxStickers(string $supplyId, array $boxIds): array
    {
        return (new GetBoxStickersAction())->run($supplyId, $boxIds);
    }
}
