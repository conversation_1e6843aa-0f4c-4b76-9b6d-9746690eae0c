<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Products;

use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class CancelMatchAction
{
    /**
     * Отменяет сопоставление товаров из Wildberries и возвращает их в список товаров к сопоставлению
     *
     * @param string $cabinetId ID кабинета
     * @param array $matchedProductIds Массив ID сопоставленных товаров
     */
    public function run(string $cabinetId, array $matchedProductIds): void
    {
        if (empty($matchedProductIds)) {
            return;
        }

        $matchedProducts = DB::table('wildberries_matched_products')
            ->whereIn('id', $matchedProductIds)
            ->where('cabinet_id', $cabinetId)
            ->get()
            ->keyBy('id');

        if ($matchedProducts->isEmpty()) {
            throw new RuntimeException('Сопоставленные товары не найдены');
        }

        DB::table('wildberries_products_to_match')
            ->whereIn('matched_product_id', $matchedProductIds)
            ->update([
                'is_matched' => false,
                'matched_product_id' => null,
                'updated_at' => now(),
            ]);

        DB::table('wildberries_matched_products')
            ->whereIn('id', $matchedProductIds)
            ->delete();
    }
}
