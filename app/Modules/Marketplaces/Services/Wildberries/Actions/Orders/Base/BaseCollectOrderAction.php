<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Base;

use App\Clients\WB\Exception\WBSellerException;
use App\Enums\Api\Internal\StatusTypeEnum;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RuntimeException;
use Throwable;

/**
 * Базовый класс для Actions сборки заказов
 */
abstract class BaseCollectOrderAction
{
    use HasOrderedUuid;

    /**
     * Получение заказа с проверкой типа
     *
     * @param string $orderId ID заказа
     * @return object Данные заказа
     * @throws NotFoundException
     * @throws RuntimeException
     */
    protected function getOrderWithValidation(string $orderId): object
    {
        $tableName = $this->getOrderTableName();

        $order = DB::table($tableName)
            ->join('wildberries_integrations', "{$tableName}.integration_id", '=', 'wildberries_integrations.id')
            ->where("{$tableName}.id", $orderId)
            ->select([
                "{$tableName}.*",
                'wildberries_integrations.token as token',
            ])
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if ($order->module_status !== 'confirmed') {
            throw new RuntimeException('Order is not in confirmed status');
        }

        if ($order->needs_warehouse_mapping) {
            throw new RuntimeException('Order needs warehouse mapping');
        }

        if ($order->has_unmatched_items) {
            throw new RuntimeException('Order has unmatched items');
        }

        return $order;
    }

    /**
     * Создание заказа в УТ
     *
     * @param object $order Заказ Wildberries
     * @param Collection $items Товары заказа
     * @param object|null $delivery Информация о доставке
     * @param string $statusId
     * @return string ID созданного заказа в УТ
     */
    protected function createCustomerOrder(object $order, Collection $items, ?object $delivery, string $statusId): string
    {
        // Получаем дату создания заказа в Wildberries
        $orderDate = Carbon::parse($order->created_at)->format('Y-m-d H:i:s');

        // Подготавливаем данные для создания заказа
        $customerOrderId = $this->generateUuid();
        $customerOrderData = [
            'id' => $customerOrderId,
            'cabinet_id' => $order->cabinet_id,
            'employee_id' => $order->employee_id,
            'department_id' => $order->department_id,
            'is_common' => false,
            'number' => $order->number,
            'date_from' => $orderDate,
            'payment_status' => 'unpaid',
            'status_id' => $statusId,
            'held' => false,
            'reserve' => $order->reserve ?? true,
            'legal_entity_id' => $order->legal_entity_id,
            'contractor_id' => $order->contractor_id,
            'plan_date' => $order->delivery_date ?? null,
            'sales_channel_id' => null,
            'warehouse_id' => $order->warehouse_id,
            'currency_id' => $order->currency_id,
            'currency_value' => 1,
            'total_price' => $order->total_price,
            'comment' => $this->generateOrderComment($order),
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('customer_orders')->insert($customerOrderData);

        $toInsertItems = [];
        foreach ($items as $item) {
            $toInsertItems[] = [
                'id' => $this->generateUuid(),
                'order_id' => $customerOrderId,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->price,
                'vat_rate_id' => null,
                'discount' => $item->discount ?? 0,
                'total_price' => $item->total_price,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        DB::table('customer_order_items')->insert($toInsertItems);

        // Создаем информацию о доставке
        if ($delivery) {
            DB::table('customer_order_delivery_infos')->insert([
                'id' => Str::orderedUuid()->toString(),
                'order_id' => $customerOrderId,
                'comment' => $delivery->comment ?? null,
                'other' => $delivery->other ?? null,
                'city' => $delivery->city ?? null,
                'street' => $delivery->street ?? null,
                'house' => $delivery->house ?? null,
                'apartment' => $delivery->apartment ?? null,
                'post_code' => $delivery->postal_code ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        return $customerOrderId;
    }

    /**
     * Генерация комментария для заказа в УТ
     *
     * @param object $order Заказ Wildberries
     * @return string Комментарий
     */
    protected function generateOrderComment(object $order): string
    {
        $comment = "Заказ Wildberries #{$order->wb_number}";

        // Добавляем дату доставки на склад МП
        if (isset($order->delivery_date)) {
            $comment .= "\nДата доставки на склад МП: " . Carbon::parse($order->delivery_date)->format('d.m.Y');
        }

        // Добавляем номер отправления
        if (isset($order->tracking_number)) {
            $comment .= "\nНомер отправления: {$order->tracking_number}";
        }

        // Добавляем информацию о дате создания заказа в Wildberries
        if (isset($order->created_at)) {
            $comment .= "\nДата создания заказа в Wildberries: " . Carbon::parse($order->created_at)->format('d.m.Y H:i:s');
        }

        // Добавляем информацию о типе операции
        $comment .= "\nТип операции: передача на комиссию";

        return $comment;
    }

    /**
     * Обновление статуса заказа
     *
     * @param string $orderId ID заказа
     * @return void
     */
    protected function updateOrderStatus(string $orderId): void
    {
        $tableName = $this->getOrderTableName();

        DB::table($tableName)
            ->where('id', $orderId)
            ->update([
                'module_status' => 'confirm',
                'supplier_status' => 'confirm',
                'updated_at' => now(),
            ]);
    }

    /**
     * Сборка заказа и создание заказа в УТ
     *
     * @param string $orderId ID заказа
     * @return string|null ID созданного заказа в УТ
     * @throws NotFoundException
     * @throws WBSellerException
     * @throws Throwable
     */
    public function run(string $orderId): ?string
    {
        $order = $this->getOrderWithValidation($orderId);

        try {
            DB::beginTransaction();

            $this->performTypeSpecificValidation($order);
            $itemsTableName = $this->getOrderItemsTableName();
            $deliveryTableName = $this->getOrderDeliveryInfoTableName();

            $items = DB::table($itemsTableName)
                ->where('order_id', $orderId)
                ->get();

            $delivery = DB::table($deliveryTableName)
                ->where('order_id', $orderId)
                ->first();

            // Определяем схему работы
            $workScheme = DB::table('warehouse_order_schemes')
                ->where('warehouse_id', $order->warehouse_id)
                ->where('on_shipment_from', '<', Carbon::now())
                ->exists();

            // Определяем статус заказа в УТ в зависимости от схемы работы
            $statusType = StatusTypeEnum::CUSTOMER_ORDERS->value;

            $statusId = DB::table('statuses')
                ->where('type', $statusType)
                ->where('name', $workScheme ? 'Собран' : 'Новый')
                ->value('id');

            if (!$statusId) {
                return null;
            }

            $customerOrderId = $this->createCustomerOrder($order, $items, $delivery, $statusId);
            $this->updateOrderStatus($orderId);
            $this->confirmOrderInWildberries($order);
            $this->performTypeSpecificConfirmation($order);

            DB::commit();
            return $customerOrderId;
        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Получить имя таблицы заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderTableName(): string;

    /**
     * Получить имя таблицы товаров заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderItemsTableName(): string;

    /**
     * Получить имя таблицы информации о доставке
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderDeliveryInfoTableName(): string;

    /**
     * Подтверждение заказа в Wildberries через API
     *
     * @param object $order Данные заказа
     * @return void
     * @throws WBSellerException
     */
    abstract protected function confirmOrderInWildberries(object $order): void;

    protected function performTypeSpecificConfirmation(object $order): void
    {
        // Базовая реализация - ничего не делаем
        // Наследники могут переопределить этот метод для специфичной логики
    }

    protected function performTypeSpecificValidation(object $order): void
    {
        // Базовая реализация - ничего не делаем
        // Наследники могут переопределить этот метод для специфичной логики
    }
}
