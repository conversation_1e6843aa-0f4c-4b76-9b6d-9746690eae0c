<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Helpers;

use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Enums\Api\Internal\StatusTypeEnum;
use App\Modules\Marketplaces\Services\Wildberries\DTO\OrderDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Класс для создания заказов клиентов на основе заказов Wildberries
 */
readonly class CustomerOrderCreator
{
    use HasOrderedUuid;

    /**
     * @param EmployeeRepositoryContract $employeeRepository Репозиторий сотрудников
     */
    public function __construct(
        private EmployeeRepositoryContract $employeeRepository
    ) {
    }

    /**
     * Создает заказ клиента на основе заказа Wildberries
     *
     * @param OrderDTO $order Данные заказа Wildberries
     * @return string ID созданного заказа клиента
     * @throws RuntimeException Если сотрудник не найден
     */
    public function createCustomerOrder(OrderDTO $order): string
    {
        // Получение или создание статуса "На сборке"
        $statusId = $this->getOrCreateAssemblyStatus($order->cabinetId);

        // Получение сотрудника
        $employee = $this->getEmployee($order->cabinetId);

        // Создание заказа клиента
        $customerOrderId = $this->createOrder($order, $statusId, $employee->id);

        // Создание информации о доставке
        $this->createDeliveryInfo($order->id, $customerOrderId);

        // Создание позиций заказа
        $this->createOrderItems($order->id, $customerOrderId);

        return $customerOrderId;
    }

    /**
     * Получает или создает статус "На сборке"
     *
     * @param string $cabinetId ID кабинета
     * @return string ID статуса
     */
    private function getOrCreateAssemblyStatus(string $cabinetId): string
    {
        $statusTypeId = StatusTypeEnum::CUSTOMER_ORDERS->value;

        $statusId = DB::table('statuses')
            ->where('cabinet_id', $cabinetId)
            ->where('type', $statusTypeId)
            ->where('name', 'На сборке')
            ->value('id');

        if (!$statusId) {
            $statusId = $this->generateUuid();
            DB::table('statuses')
                ->insert([
                    'id' => $statusId,
                    'cabinet_id' => $cabinetId,
                    'type' => $statusTypeId,
                    'name' => 'На сборке',
                    'color' => '#fff',
                ]);
        }

        return $statusId;
    }

    /**
     * Получает сотрудника по ID пользователя и кабинета
     *
     * @param string $cabinetId ID кабинета
     * @return object Данные сотрудника
     * @throws RuntimeException Если сотрудник не найден
     */
    private function getEmployee(string $cabinetId): object
    {
        $employee = $this->employeeRepository->getByUserIdAndCabinet(
            Auth::id(),
            $cabinetId
        );

        if (!$employee) {
            throw new RuntimeException('Сотрудник не найден');
        }

        return $employee;
    }

    /**
     * Создает заказ клиента
     *
     * @param OrderDTO $order Данные заказа Wildberries
     * @param string $statusId ID статуса
     * @param string $employeeId ID сотрудника
     * @return string ID созданного заказа
     */
    private function createOrder(OrderDTO $order, string $statusId, string $employeeId): string
    {
        $customerOrderId = $this->generateUuid();

        DB::table('customer_orders')
            ->insert([
                'id' => $customerOrderId,
                'cabinet_id' => $order->cabinetId,
                'employee_id' => $employeeId,
                'department_id' => $order->departmentId,
                'is_common' => true,
                'number' => $order->number,
                'date_from' => $order->createdAt,
                'payment_status' => 'unpaid',
                'status_id' => $statusId,
                'held' => false,
                'reserve' => $order->reserve,
                'legal_entity_id' => $order->legalEntityId,
                'contractor_id' => $order->contractorId,
                'plan_date' => $order->deliveryDate ?? null,
                'sales_channel_id' => null,
                'warehouse_id' => $order->warehouseId,
                'currency_id' => $order->cabinetCurrencyId,
                'currency_value' => 1,
                'total_price' => $order->totalPrice,
                'comment' => $order->comment,
            ]);

        return $customerOrderId;
    }

    /**
     * Создает информацию о доставке для заказа клиента
     *
     * @param string $wildberriesOrderId ID заказа Wildberries
     * @param string $customerOrderId ID заказа клиента
     * @return void
     */
    private function createDeliveryInfo(string $wildberriesOrderId, string $customerOrderId): void
    {
        $orderDeliveryInfo = DB::table('customer_order_delivery_infos')
            ->where('order_id', $wildberriesOrderId)
            ->first();

        if ($orderDeliveryInfo) {
            DB::table('customer_order_delivery_infos')
                ->insert([
                    'id' => $this->generateUuid(),
                    'order_id' => $customerOrderId,
                    'comment' => 'Широта: '.$orderDeliveryInfo->latitude.', Долгота: '.$orderDeliveryInfo->longitude,
                    'other' => $orderDeliveryInfo->full_address,
                    'city' => $orderDeliveryInfo->city,
                    'street' => $orderDeliveryInfo->street,
                    'house' => $orderDeliveryInfo->house,
                    'office' => $orderDeliveryInfo->apartment,
                    'post_code' => $orderDeliveryInfo->post_code,
                    'country' => $orderDeliveryInfo->country,
                    'region' => $orderDeliveryInfo->region,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
        }
    }

    /**
     * Создает позиции заказа клиента
     *
     * @param string $wildberriesOrderId ID заказа Wildberries
     * @param string $customerOrderId ID заказа клиента
     * @return void
     */
    private function createOrderItems(string $wildberriesOrderId, string $customerOrderId): void
    {
        $orderItems = DB::table('wildberries_fbs_order_items')
            ->where('order_id', $wildberriesOrderId)
            ->get();

        $toInsertItems = [];
        foreach ($orderItems as $item) {
            $discount = $item->discount ?? 0;
            $toInsertItems[] = [
                'id' => $this->generateUuid(),
                'order_id' => $customerOrderId,
                'product_id' => $item->product_id,
                'quantity' => $item->quantity,
                'price' => $item->price,
                'vat_rate_id' => null,
                'discount' => $discount,
                'total_price' => $item->price * ($item->quantity - $discount) / ($item->quantity * (1 - $discount)),
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        }

        if (!empty($toInsertItems)) {
            DB::table('customer_order_items')->insert($toInsertItems);
        }
    }
}
