<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\Helpers;

use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\DTO\OrderDTO;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Класс для валидации поставок и заказов в поставках
 */
readonly class SupplyValidator
{
    /**
     * Валидирует поставку по ID
     *
     * @param string $supplyId ID поставки
     * @return object Данные поставки
     * @throws NotFoundException Если поставка не найдена
     * @throws RuntimeException Если поставка завершена или закрыта
     */
    public function validateSupply(string $supplyId): object
    {
        $supply = DB::table('wildberries_supplies')
            ->where('id', $supplyId)
            ->first();

        if (! $supply) {
            throw new NotFoundException('Поставка не найдена');
        }

        if ($supply->done || Carbon::parse($supply->closed_at)->gt(Carbon::parse('0001-01-01T00:00:00Z'))) {
            throw new RuntimeException('Поставка завершена или закрыта');
        }

        return $supply;
    }

    /**
     * Валидирует заказ в поставке
     *
     * @param OrderDTO $order Данные заказа
     * @param object $supply Данные поставки
     * @throws RuntimeException Если заказ уже добавлен в завершенную поставку или типы груза не совпадают
     */
    public function validateOrderInSupply(OrderDTO $order, object $supply): void
    {
        $this->validateOrderNotInClosedSupply($order);
        $this->validateCargoTypeMatch($order, $supply);
        $this->validateDeliveryTypes($order);
    }

    /**
     * Проверяет, что заказ не добавлен в завершенную поставку
     *
     * @param OrderDTO $order Данные заказа
     * @throws RuntimeException Если заказ уже добавлен в завершенную поставку
     */
    private function validateOrderNotInClosedSupply(OrderDTO $order): void
    {
        $supplyOrder = DB::table('wildberries_supply_orders')
            ->join('wildberries_supplies', 'wildberries_supply_orders.supply_id', '=', 'wildberries_supplies.id')
            ->where('order_id', $order->id)
            ->select([
                'wildberries_supply_orders.*',
                'wildberries_supplies.done as done',
                'wildberries_supplies.closed_at as closed_at',
            ])
            ->first();

        if ($supplyOrder &&
            (
                ($supplyOrder->done && !$supplyOrder->requires_reshipping)
                ||
                (Carbon::parse($supplyOrder->closed_at)->gt(Carbon::parse('0001-01-01T00:00:00Z')) && !$supplyOrder->requires_reshipping)
            )
        ) {
            throw new RuntimeException('Заказ уже добавлен в завершенную или закрытую поставку и не требует повторной отправки');
        }
    }

    /**
     * Проверяет соответствие типов груза
     *
     * @param OrderDTO $order Данные заказа
     * @param object $supply Данные поставки
     * @throws RuntimeException Если типы груза не совпадают
     */
    private function validateCargoTypeMatch(OrderDTO $order, object $supply): void
    {
        if ($supply->cargo_type && $supply->cargo_type !== $order->cargoType) {
            throw new RuntimeException('Несоответствие типа груза');
        }
    }

    private function validateDeliveryTypes(OrderDTO $order): void
    {
        if ($order->deliveryType == 'wbgo') {
            throw new RuntimeException('Сборочное задание с типом доставки WBGO нельзя добавить в поставку.');
        }
    }
}
