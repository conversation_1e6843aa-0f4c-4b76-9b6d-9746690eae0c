<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

readonly class DeleteBoxInSupplyAction
{
    use HasOrderedUuid;

    /**
     * Удалить короба из поставки
     * Метод убирает заказы из перечисленных коробов поставки и удаляет короба.
     *
     * Можно удалить только пока поставка на сборке.
     *
     * @param string $supplyId
     * @param array $boxIds
     * @return void
     *
     * @throws NotFoundException
     * @throws WBSellerException
     */
    public function run(string $supplyId, array $boxIds): void
    {
        $supply = DB::table('wildberries_supplies')
            ->join('wildberries_integrations', 'wildberries_supplies.integration_id', '=', 'wildberries_integrations.id')
            ->where('wildberries_supplies.id', $supplyId)
            ->select([
                'wildberries_supplies.*',
                'wildberries_integrations.token as token',
            ])
            ->first();

        if (! $supply) {
            throw new NotFoundException('Supply not found');
        }

        if ($supply->done || \Illuminate\Support\Carbon::parse($supply->closed_at)->gt(Carbon::parse('0001-01-01T00:00:00Z'))) {
            throw new RuntimeException('Supply is done or closed');
        }

        $boxes = DB::table('wildberries_boxes')
            ->where('supply_id', $supplyId)
            ->whereIn('id', $boxIds)
            ->get();

        $token = decrypt($supply->token);
        $api = new API(['masterkey' => $token]);
        $api->Marketplace()->deleteSupplyBoxes($supply->supply_id, $boxes->pluck('trbxIds')->toArray());

        DB::table('wildberries_boxes')
            ->where('supply_id', $supplyId)
            ->whereIn('id', $boxIds)
            ->delete();
    }
}
