<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies;

use App\Clients\WB\API;
use App\Clients\WB\Exception\ApiClientException;
use App\Clients\WB\Exception\ApiTimeRestrictionsException;
use App\Clients\WB\Exception\WBSellerException;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

readonly class CreateSupplyAction
{
    use HasOrderedUuid;

    /**
     * Создание новой поставки
     *
     * @param string $name
     * @param string $integrationId
     * @param string $cabinetId
     * @param string $token
     * @return string ID новой поставки
     *
     * @throws ApiClientException
     * @throws ApiTimeRestrictionsException
     * @throws WBSellerException
     */
    public function run(string $name, string $integrationId, string $cabinetId, string $token): string
    {

        $api = new API(['masterkey' => $token]);

        $result = $api->Marketplace()->createSupply($name);

        $supplyId = $this->generateUuid();
        DB::table('wildberries_supplies')
            ->insert([
                'id' => $supplyId,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
                'cabinet_id' => $cabinetId,
                'integration_id' => $integrationId,
                'supply_id' => $result->id,
                'name' => $name,
                'created_at_wb' => Carbon::now(),
                'closed_at' => '0001-01-01T00:00:00Z'
            ]);

        return $supplyId;
    }
}
