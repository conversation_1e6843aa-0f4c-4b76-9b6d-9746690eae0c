<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\DBW;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Action переводит сборочное задание в статус complete — в доставке.
 */
class AssembleFBSDBWOrderAction
{
    /**
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function run(string $orderId): void
    {
        $order = DB::table('wildberries_fbs_orders')
            ->where('id', $orderId)
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if ($order->delivery_type !== 'wbgo') {
            throw new RuntimeException('Order is not in wbgo delivery type');
        }

        if ($order->module_status !== 'confirmed' || $order->wb_status !== 'confirm') {
            throw new RuntimeException('Order is not in confirmed state');
        }

        $api = new API(['masterkey' => $order->token]);
        $api->Marketplace()->WBGO()->assemble($order->wb_number);

        DB::table('wildberries_fbs_orders')
            ->where('id', $orderId)
            ->update([
                'module_status' => 'completed',
                'wb_status' => 'complete',
                'updated_at' => now(),
            ]);
    }
}
