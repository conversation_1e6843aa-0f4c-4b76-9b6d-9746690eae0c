<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Boxes\Helpers\SupplyValidator;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/**
 * Класс для завершения поставки Wildberries
 *
 * Метод закрывает поставку и переводит все сборочные задания в ней в статус complete – в доставке.
 * После закрытия поставки добавить новые сборочные задания к ней нельзя.
 *
 * Если поставка не была передана в доставку, то при сканировании её штрихкода или приёмке первого товара
 * поставка автоматически закроется.
 *
 * Передать поставку в доставку можно только если в ней:
 * - есть хотя бы одно сборочное задания
 * - отсутствуют пустые короба
 *
 * @see https://dev.wildberries.ru/openapi/orders-fbs/#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1{supplyId}~1deliver/patch
 */
readonly class CompleteSupplyAction
{
    use HasOrderedUuid;

    /**
     * @param SupplyValidator $supplyValidator Валидатор поставок
     */
    public function __construct(
        private SupplyValidator $supplyValidator
    ) {
    }

    /**
     * Завершение поставки
     *
     * @param string $supplyId ID поставки
     * @return void
     *
     * @throws NotFoundException Если поставка не найдена
     * @throws WBSellerException При ошибке API Wildberries
     * @throws RuntimeException При ошибках валидации
     */
    public function run(string $supplyId): void
    {
        $supply = $this->supplyValidator->validateSupply($supplyId);

        $supplyOrders = $this->validateSupplyHasOrders($supplyId);

        $this->validateNoEmptyBoxes($supplyId);

        $token = decrypt($supply->token);
        $api = new API(['masterkey' => $token]);
        $api->Marketplace()->closeSupply($supply->supply_id);

        $this->updateSupplyStatus($supplyId);

        $this->updateOrdersStatus($supplyOrders);
    }

    /**
     * Проверяет наличие заказов в поставке
     *
     * @param string $supplyId ID поставки
     * @return object Заказы в поставке
     * @throws RuntimeException Если поставка не содержит заказов
     */
    private function validateSupplyHasOrders(string $supplyId): object
    {
        $supplyOrders = DB::table('wildberries_supply_orders')
            ->where('supply_id', $supplyId)
            ->get();

        if ($supplyOrders->isEmpty()) {
            throw new RuntimeException('Поставка не содержит заказов');
        }

        return $supplyOrders;
    }

    /**
     * Проверяет отсутствие пустых коробов в поставке
     *
     * @param string $supplyId ID поставки
     * @throws RuntimeException Если поставка содержит пустые короба
     */
    private function validateNoEmptyBoxes(string $supplyId): void
    {
        $emptyBoxes = DB::table('wildberries_boxes')
            ->where('supply_id', $supplyId)
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('wildberries_box_orders')
                    ->whereColumn('wildberries_box_orders.box_id', 'wildberries_boxes.id');
            })
            ->exists();

        if ($emptyBoxes) {
            throw new RuntimeException('Поставка содержит пустые короба');
        }
    }

    /**
     * Обновляет статус поставки
     *
     * @param string $supplyId ID поставки
     */
    private function updateSupplyStatus(string $supplyId): void
    {
        DB::table('wildberries_supplies')
            ->where('id', $supplyId)
            ->update([
                'updated_at' => Carbon::now(),
            ]);
    }

    /**
     * Обновляет статусы заказов в поставке
     *
     * @param object $supplyOrders Заказы в поставке
     */
    private function updateOrdersStatus(object $supplyOrders): void
    {
        $orderIds = $supplyOrders->pluck('order_id')->toArray();

        DB::table('wildberries_fbs_orders')
            ->whereIn('id', $orderIds)
            ->update([
                'supplier_status' => 'complete',
                'module_status' => 'complete',
                'updated_at' => Carbon::now(),
            ]);
    }
}
