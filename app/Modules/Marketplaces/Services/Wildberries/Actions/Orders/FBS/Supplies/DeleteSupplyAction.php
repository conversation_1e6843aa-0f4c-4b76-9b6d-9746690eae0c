<?php

namespace App\Modules\Marketplaces\Services\Wildberries\Actions\Orders\FBS\Supplies;

use App\Clients\WB\API;
use App\Clients\WB\Exception\WBSellerException;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use RuntimeException;

/*
 * Метод удаляет поставку, если она активна и за ней не закреплено ни одно сборочное задание.
 * https://dev.wildberries.ru/openapi/orders-fbs/#tag/Postavki-FBS/paths/~1api~1v3~1supplies~1{supplyId}/delete
 */
readonly class DeleteSupplyAction
{
    /**
     * @throws WBSellerException
     * @throws NotFoundException
     */
    public function run(string $supplyId): void
    {
        $supply = DB::table('wildberries_supplies as ws')
            ->join('wildberries_integrations as wi', 'ws.integration_id', '=', 'wi.id')
            ->leftJoin('wildberries_supply_orders as wso', 'ws.id', '=', 'wso.supply_id')
            ->leftJoin('wildberries_boxes as wb', 'ws.id', '=', 'wb.supply_id')
            ->where('ws.id', $supplyId)
            ->select([
                'ws.done as done',
                'ws.closed_at as closed_at',
                DB::raw('COUNT(wso.id) as orders_count'),
                DB::raw('COUNT(wb.id) as boxes_count'),
                'wi.token as token',
                'ws.supply_id as supply_id',
            ])
            ->groupBy('ws.id', 'ws.done', 'ws.closed_at', 'wi.token', 'ws.supply_id')
            ->first();


        if (!$supply) {
            throw new NotFoundException('Supply not found');
        }

        if ($supply->done || Carbon::parse($supply->closed_at)->gt(
                \Carbon\Carbon::parse('0001-01-01T00:00:00Z'))) {
            throw new RuntimeException('Cannot delete supply that is done or closed');
        }

        if ($supply->orders_count != 0) {
            throw new RuntimeException('Cannot delete supply with orders');
        }
        if ($supply->boxes_count != 0) {
            throw new RuntimeException('Cannot delete supply with orders');
        }

        DB::table('wildberries_supplies')
            ->where('id', $supplyId)
            ->delete();

        $token = decrypt($supply->token);
        $api = new API(['masterkey' => $token]);
        $api->Marketplace()->deleteSupply($supply->supply_id);
    }
}
