<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Reports;

use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Ozon\Jobs\Reports\SyncFinanceTransactionsJob;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Throwable;

readonly class LoadReportsAction
{
    /**
     * Запуск загрузки отчетов о финансовых транзакциях
     *
     * @param string $integrationId ID интеграции
     * @param string $dateFrom Дата начала периода (Y-m-d)
     * @param string $dateTo Дата окончания периода (Y-m-d)
     * @throws Throwable
     */
    public function run(string $integrationId, string $dateFrom, string $dateTo): void
    {
        $integration = DB::table('ozon_integrations')
            ->where('id', $integrationId)
            ->first();

        if (!$integration) {
            throw new NotFoundException("Integration with ID {$integrationId} not found");
        }

        $clientId = decrypt($integration->client_id);
        $apiKey = decrypt($integration->api_key);

        Queue::push(new SyncFinanceTransactionsJob(
            integrationId: $integration->id,
            apiKey: $apiKey,
            clientId: $clientId,
            dateFrom: $dateFrom,
            dateTo: $dateTo,
            legalEntityId: $integration->legal_entity_id,
            contractorId: $integration->contractor_id,
            contractId: $integration->commission_contract_id,
            salesChannelId: $integration->sales_channel_id,
            cabinetId: $integration->cabinet_id,
            departmentId: $integration->department_id,
        ));
    }
}
