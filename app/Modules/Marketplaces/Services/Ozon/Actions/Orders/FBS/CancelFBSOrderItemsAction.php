<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS;

use App\Clients\Ozon\API;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base\BaseCancelOrderItemsAction;

/**
 * Action для отмены товаров в FBS заказах
 */
class CancelFBSOrderItemsAction extends BaseCancelOrderItemsAction
{
    /**
     * Получить имя таблицы заказов
     *
     * @return string Имя таблицы
     */
    protected function getOrderTableName(): string
    {
        return 'ozon_fbs_orders';
    }

    /**
     * Получить имя поля для связи с заказом в связанных таблицах
     *
     * @return string Имя поля
     */
    protected function getOrderIdFieldName(): string
    {
        return 'order_id';
    }

    /**
     * Получить имя таблицы товаров заказа
     *
     * @return string Имя таблицы
     */
    protected function getOrderItemsTableName(): string
    {
        return 'ozon_fbs_order_items';
    }

    /**
     * Отменить товары заказа в Ozon через API
     *
     * @param string $postingNumber Номер отправления
     * @param int $cancelReasonId ID причины отмены
     * @param string $cancelReasonMessage Сообщение причины отмены
     * @param array $items Массив товаров для отмены
     * @param int $clientId ID клиента
     * @param string $apiKey API ключ
     * @return void
     */
    protected function cancelOrderItemsInOzon(
        string $postingNumber,
        int $cancelReasonId,
        string $cancelReasonMessage,
        array $items,
        int $clientId,
        string $apiKey,
    ): void {
        $api = new API(
            apiKey: $apiKey,
            clientId: $clientId
        );
        $api->FBS()->cancelOrderItems($postingNumber, $cancelReasonId, $cancelReasonMessage, $items);
    }
}
