<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use Illuminate\Support\Facades\DB;
use InvalidArgumentException;

/**
 * Action для получения этикеток FBS отправлений
 */
class GetPackageLabelsAction
{
    /**
     * Получить этикетки для отправлений
     *
     * @param array $orderIds Массив ID заказов (максимум 20)
     * @return object Ответ API с PDF файлом этикеток
     * @throws NotFoundException
     * @throws InvalidArgumentException
     */
    public function run(array $orderIds): object
    {
        if (count($orderIds) > 20) {
            throw new InvalidArgumentException('Maximum 20 orders allowed per request');
        }

        if (empty($orderIds)) {
            throw new InvalidArgumentException('At least one order ID is required');
        }

        // Получаем заказы с номерами отправлений
        $orders = DB::table('ozon_fbs_orders')
            ->join('ozon_integrations', 'ozon_fbs_orders.integration_id', '=', 'ozon_integrations.id')
            ->whereIn('ozon_fbs_orders.id', $orderIds)
            ->select([
                'ozon_fbs_orders.posting_number',
                'ozon_integrations.api_key',
                'ozon_integrations.client_id',
            ])
            ->get();

        if ($orders->isEmpty()) {
            throw new NotFoundException('No orders found');
        }

        if ($orders->count() !== count($orderIds)) {
            throw new NotFoundException('Some orders not found');
        }

        // Проверяем, что все заказы принадлежат одной интеграции
        $firstOrder = $orders->first();
        $apiKey = $firstOrder->api_key;
        $clientId = $firstOrder->client_id;

        foreach ($orders as $order) {
            if ($order->api_key !== $apiKey || $order->client_id !== $clientId) {
                throw new InvalidArgumentException('All orders must belong to the same integration');
            }
        }

        // Собираем номера отправлений
        $postingNumbers = $orders->pluck('posting_number')->toArray();

        // Вызываем API Ozon
        $api = new API(
            apiKey: decrypt($apiKey),
            clientId: decrypt($clientId)
        );

        return $api->FBS()->getPackageLabels($postingNumbers);
    }
}
