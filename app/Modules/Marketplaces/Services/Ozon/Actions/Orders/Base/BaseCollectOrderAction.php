<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Orders\Base;

use App\Enums\Api\Internal\StatusTypeEnum;
use App\Exceptions\NotFoundException;
use App\Traits\HasOrderedUuid;
use App\Traits\PrecisionCalculator;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

/**
 * Базовый класс для Actions сборки заказов
 */
abstract class BaseCollectOrderAction
{
    use PrecisionCalculator;
    use HasOrderedUuid;

    /**
     * Получение заказа с проверкой типа
     */
    protected function getOrderWithValidation(string $orderId): object
    {
        $tableName = $this->getOrderTableName();

        $order = DB::table($tableName)
            ->join('ozon_integrations', "{$tableName}.integration_id", '=', 'ozon_integrations.id')
            ->where("{$tableName}.id", $orderId)
            ->select([
                "{$tableName}.*",
                'ozon_integrations.client_id as client_id',
                'ozon_integrations.api_key as api_key',
            ])
            ->first();

        if (!$order) {
            throw new NotFoundException('Order not found');
        }

        if ($order->module_status !== 'confirmed') {
            throw new RuntimeException('Order is not in confirmed status');
        }

        if ($order->status === 'awaiting_packaging') {
            throw new RuntimeException('Order is in awaiting_packaging status');
        }

        $requirements = json_decode($order->requirements, false, 512, JSON_THROW_ON_ERROR);
        if (!empty($requirements->products_requiring_country)) {
            throw new RuntimeException('Order has products requiring country');
        }
        if (!empty($requirements->products_requiring_gtd)) {
            throw new RuntimeException('Order has products requiring gtd');
        }
        if (!empty($requirements->products_requiring_mandatory_mark)) {
            throw new RuntimeException('Order has products requiring mandatory mark');
        }
        if (!empty($requirements->products_requiring_jw_uin)) {
            throw new RuntimeException('Order has products requiring jw uin');
        }
        if (!empty($requirements->products_requiring_rnpt)) {
            throw new RuntimeException('Order has products requiring rnpt');
        }

        if ($order->needs_warehouse_mapping) {
            throw new RuntimeException('Order needs warehouse mapping');
        }

        if ($order->has_unmatched_items) {
            throw new RuntimeException('Order has unmatched items');
        }

        return $order;
    }

    /**
     * Создание заказов покупателя в УТ (по одному заказу на каждую позицию)
     */
    protected function createCustomerOrders(object $order, Collection $items, ?object $delivery, string $statusId): array
    {
        $orderDate = Carbon::parse($order->created_at)->format('Y-m-d H:i:s');
        $customerOrderIds = [];
        $orderCounter = 1;

        foreach ($items as $item) {

            $discountPercentage = '0';
            if (isset($item->old_price, $item->price) && $this->greaterThan(
                $item->old_price,
                '0'
            ) && $this->greaterThan($item->old_price, $item->price)) {

                $discountAmount = $this->subtract($item->old_price, $item->price);
                $discountPercentage = $this->divide(
                    $this->multiply($discountAmount, '100'),
                    $item->old_price
                );
            }

            for ($i = 0; $i < $item->quantity; $i++) {
                $customerOrderId = $this->generateUuid();
                $customerOrderData = [
                    'id' => $customerOrderId,
                    'cabinet_id' => $order->cabinet_id,
                    'employee_id' => $order->employee_id,
                    'department_id' => $order->department_id,
                    'is_common' => false,
                    'number' => $order->number . '-' . $orderCounter, // Уникальный номер для каждого заказа
                    'date_from' => $orderDate,
                    'payment_status' => 'unpaid',
                    'status_id' => $statusId,
                    'held' => false,
                    'reserve' => $order->reserve ?? true,
                    'legal_entity_id' => $order->legal_entity_id,
                    'contractor_id' => $order->contractor_id,
                    'plan_date' => $order->delivery_date ?? null,
                    'sales_channel_id' => null,
                    'warehouse_id' => $order->warehouse_id,
                    'currency_id' => $order->currency_id,
                    'currency_value' => 1,
                    'total_price' => $item->price,
                    'comment' => $this->generateOrderComment($order),
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                DB::table('customer_orders')->insert($customerOrderData);

                $orderItemData = [
                    'id' => $this->generateUuid(),
                    'order_id' => $customerOrderId,
                    'product_id' => $item->product_id,
                    'quantity' => 1,
                    'price' => $item->price,
                    'vat_rate_id' => null,
                    'discount' => $discountPercentage,
                    'total_price' => $item->price,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                DB::table('customer_order_items')->insert($orderItemData);

                if ($delivery) {
                    DB::table('customer_order_delivery_infos')->insert([
                        'id' => $this->generateUuid(),
                        'order_id' => $customerOrderId,
                        'city' => $delivery->city ?? null,
                        'street' => $delivery->street ?? null,
                        'house' => $delivery->house ?? null,
                        'apartment' => $delivery->apartment ?? null,
                        'post_code' => $delivery->post_code ?? null,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }

                $customerOrderIds[] = $customerOrderId;
                $orderCounter++;
            }
        }

        return $customerOrderIds;
    }

    /**
     * Подготовка данных упаковок для API сборки заказа
     * Каждая единица товара помещается в отдельную упаковку
     */
    protected function preparePackagesForShipping(Collection $items): array
    {
        $packages = [];

        foreach ($items as $item) {
            for ($i = 0; $i < $item->quantity; $i++) {
                $packages[] = [
                    'products' => [
                        [
                            'product_id' => (int) $item->ozon_product_id,
                            'quantity' => 1,
                        ]
                    ]
                ];
            }
        }

        return $packages;
    }

    /**
     * Генерация комментария для заказа в УТ
     */
    protected function generateOrderComment(object $order): string
    {
        $comment = "Заказ ozon #{$order->wb_number}";

        if (isset($order->delivery_date)) {
            $comment .= "\nДата доставки на склад МП: " . Carbon::parse($order->delivery_date)->format('d.m.Y');
        }

        if (isset($order->tracking_number)) {
            $comment .= "\nНомер отправления: {$order->tracking_number}";
        }

        if (isset($order->created_at)) {
            $comment .= "\nДата создания заказа в ozon: " . Carbon::parse($order->created_at)->format('d.m.Y H:i:s');
        }

        $comment .= "\nТип операции: передача на комиссию";

        return $comment;
    }

    /**
     * Обновление статуса заказа
     */
    protected function updateOrderStatus(string $orderId): void
    {
        $tableName = $this->getOrderTableName();

        DB::table($tableName)
            ->where('id', $orderId)
            ->update([
                'status' => 'awaiting_deliver',
                'updated_at' => now(),
            ]);
    }

    /**
     * Сборка заказа и создание заказов в УТ (по одному заказу на каждую позицию)
     */
    public function run(string $orderId): ?array
    {
        $order = $this->getOrderWithValidation($orderId);

        try {
            DB::beginTransaction();

            $this->performTypeSpecificValidation($order);
            $itemsTableName = $this->getOrderItemsTableName();
            $deliveryTableName = $this->getOrderDeliveryInfoTableName();

            $items = DB::table($itemsTableName)
                ->where('order_id', $orderId)
                ->get();

            $delivery = DB::table($deliveryTableName)
                ->where('order_id', $orderId)
                ->first();

            $workScheme = DB::table('warehouse_order_schemes')
                ->where('warehouse_id', $order->warehouse_id)
                ->where('on_shipment_from', '<', Carbon::now())
                ->exists();

            $statusType = StatusTypeEnum::CUSTOMER_ORDERS->value;

            $statusId = DB::table('statuses')
                ->where('type', $statusType)
                ->where('name', $workScheme ? 'Собран' : 'Новый')
                ->value('id');

            if (!$statusId) {
                return null;
            }

            $customerOrderIds = $this->createCustomerOrders($order, $items, $delivery, $statusId);

            $packages = $this->preparePackagesForShipping($items);

            $this->updateOrderStatus($orderId);
            $this->confirmOrderInOzon($order, $packages);
            $this->performTypeSpecificConfirmation($order);

            DB::commit();
            return $customerOrderIds;
        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Получить имя таблицы заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderTableName(): string;

    /**
     * Получить имя таблицы товаров заказов
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderItemsTableName(): string;

    /**
     * Получить имя таблицы информации о доставке
     *
     * @return string Имя таблицы
     */
    abstract protected function getOrderDeliveryInfoTableName(): string;

    /**
     * Подтверждение заказа в ozon через API с отправкой данных о сборке
     *
     * @param object $order Данные заказа
     * @param array $packages Массив упаковок для API сборки
     * @return void
     */
    abstract protected function confirmOrderInOzon(object $order, array $packages): void;

    protected function performTypeSpecificConfirmation(object $order): void
    {
        // Базовая реализация - ничего не делаем
        // Наследники могут переопределить этот метод для специфичной логики
    }

    protected function performTypeSpecificValidation(object $order): void
    {
        // Базовая реализация - ничего не делаем
        // Наследники могут переопределить этот метод для специфичной логики
    }
}
