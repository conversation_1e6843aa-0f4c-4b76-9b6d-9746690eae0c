<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Carriages;

use App\Clients\Ozon\API;
use Illuminate\Support\Facades\DB;

class CheckDocumentsStatusAction
{
    public function run(int $maxAttempts = 5, int $batchSize = 50): array
    {
        $carriages = $this->getCarriagesToCheck($maxAttempts, $batchSize);

        $stats = [
            'processed' => 0,
            'ready' => 0,
            'in_process' => 0,
            'errors' => 0,
            'failed' => 0,
        ];

        foreach ($carriages as $carriage) {
            try {
                $result = $this->checkCarriageDocuments($carriage);
                $stats['processed']++;
                $stats[$result]++;
            } catch (\Exception $e) {
                $this->updateCheckAttempt($carriage->id, 'error');
                $stats['failed']++;
            }
        }

        return $stats;
    }

    protected function getCarriagesToCheck(int $maxAttempts, int $batchSize): \Illuminate\Support\Collection
    {
        return DB::table('ozon_carriages as c')
            ->join('ozon_integrations as i', 'c.integration_id', '=', 'i.id')
            ->where('c.documents_checked', false)
            ->where('c.documents_check_attempts', '<', $maxAttempts)
            ->whereIn('c.status', ['approved', 'confirmed'])
            ->where(function ($query) {
                $query->whereNull('c.last_documents_check')
                      ->orWhere('c.last_documents_check', '<', now()->subMinutes(5));
            })
            ->select([
                'c.id',
                'c.ozon_carriage_id',
                'c.status',
                'c.documents_check_attempts',
                'i.api_key',
                'i.client_id'
            ])
            ->limit($batchSize)
            ->get();
    }

    protected function checkCarriageDocuments(object $carriage): string
    {
        $this->updateCheckAttempt($carriage->id);

        try {
            $documentsInfo = $this->getDocumentsStatusFromOzon($carriage);
            $this->saveDocumentsStatus($carriage->id, $documentsInfo);

            $status = $documentsInfo->result->status ?? 'unknown';

            if ($status === 'ready') {
                $this->markDocumentsAsChecked($carriage->id);
                return 'ready';
            }

            if ($status === 'in_process') {
                return 'in_process';
            }

            return 'errors';
        } catch (\Exception $e) {
            $this->updateCheckAttempt($carriage->id, 'error');
            throw $e;
        }
    }

    protected function getDocumentsStatusFromOzon(object $carriage): object
    {
        $api = new API(
            apiKey: decrypt($carriage->api_key),
            clientId: decrypt($carriage->client_id)
        );

        return $api->FBS()->checkActStatus((int) $carriage->ozon_carriage_id);
    }

    protected function saveDocumentsStatus(string $carriageId, object $documentsInfo): void
    {
        $result = $documentsInfo->result ?? null;

        if (!$result) {
            return;
        }

        DB::table('ozon_carriage_details')
            ->updateOrInsert(
                ['carriage_id' => $carriageId],
                [
                    'id' => DB::table('ozon_carriage_details')
                        ->where('carriage_id', $carriageId)
                        ->value('id') ?: \Illuminate\Support\Str::orderedUuid()->toString(),
                    'carriage_id' => $carriageId,
                    'act_type' => $result->act_type ?? null,
                    'is_partial' => $result->is_partial ?? false,
                    'partial_num' => $result->partial_num ?? null,
                    'has_postings_for_next_carriage' => $result->has_postings_for_next_carriage ?? false,
                    'updated_at' => now(),
                    'created_at' => DB::table('ozon_carriage_details')
                        ->where('carriage_id', $carriageId)
                        ->value('created_at') ?: now(),
                ]
            );
    }

    protected function updateCheckAttempt(string $carriageId, ?string $status = null): void
    {
        DB::table('ozon_carriages')
            ->where('id', $carriageId)
            ->increment('documents_check_attempts', 1, [
                'last_documents_check' => now(),
                'documents_status' => $status,
                'updated_at' => now(),
            ]);
    }

    protected function markDocumentsAsChecked(string $carriageId): void
    {
        DB::table('ozon_carriages')
            ->where('id', $carriageId)
            ->update([
                'documents_checked' => true,
                'documents_status' => 'ready',
                'updated_at' => now(),
            ]);
    }
}
