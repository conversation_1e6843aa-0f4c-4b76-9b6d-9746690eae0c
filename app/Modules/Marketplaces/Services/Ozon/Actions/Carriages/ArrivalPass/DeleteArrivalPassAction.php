<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Carriages\ArrivalPass;

use App\Clients\Ozon\API;
use App\Exceptions\NotFoundException;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

class DeleteArrivalPassAction
{
    public function run(string $carriageId, array $arrivalPassIds): array
    {
        $carriage = $this->getCarriageWithIntegration($carriageId);

        $this->validatePassesExist($carriageId, $arrivalPassIds);

        try {
            DB::beginTransaction();

            $this->deleteArrivalPassInOzon($carriage, $arrivalPassIds);

            $deletedCount = $this->deleteArrivalPasses($carriageId, $arrivalPassIds);

            $this->updateCarriageDetails($carriageId, $arrivalPassIds);

            DB::commit();

            return [
                'carriage_id' => $carriageId,
                'ozon_carriage_id' => $carriage->ozon_carriage_id,
                'deleted_pass_ids' => $arrivalPassIds,
                'deleted_count' => $deletedCount,
                'message' => 'Arrival passes deleted successfully',
            ];

        } catch (Exception|Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    protected function getCarriageWithIntegration(string $carriageId): object
    {
        $carriage = DB::table('ozon_carriages as c')
            ->join('ozon_integrations as i', 'c.integration_id', '=', 'i.id')
            ->where('c.id', $carriageId)
            ->select([
                'c.id',
                'c.ozon_carriage_id',
                'c.status',
                'i.api_key',
                'i.client_id'
            ])
            ->first();

        if (!$carriage) {
            throw new NotFoundException('Carriage not found');
        }

        return $carriage;
    }

    protected function validatePassesExist(string $carriageId, array $arrivalPassIds): void
    {
        $existingPasses = DB::table('ozon_arrival_passes')
            ->where('carriage_id', $carriageId)
            ->whereIn('ozon_pass_id', $arrivalPassIds)
            ->pluck('ozon_pass_id')
            ->toArray();

        $missingPasses = array_diff($arrivalPassIds, $existingPasses);

        if (!empty($missingPasses)) {
            throw new NotFoundException('Arrival passes not found: ' . implode(', ', $missingPasses));
        }
    }

    protected function deleteArrivalPassInOzon(object $carriage, array $arrivalPassIds): object
    {
        $api = new API(
            apiKey: decrypt($carriage->api_key),
            clientId: decrypt($carriage->client_id)
        );

        return $api->FBS()->deleteArrivalPass(
            carriageId: (int) $carriage->ozon_carriage_id,
            arrivalPassIds: $arrivalPassIds
        );
    }

    protected function deleteArrivalPasses(string $carriageId, array $arrivalPassIds): int
    {
        return DB::table('ozon_arrival_passes')
            ->where('carriage_id', $carriageId)
            ->whereIn('ozon_pass_id', $arrivalPassIds)
            ->delete();
    }

    protected function updateCarriageDetails(string $carriageId, array $deletedPassIds): void
    {
        $carriageDetails = DB::table('ozon_carriage_details')
            ->where('carriage_id', $carriageId)
            ->first();

        if ($carriageDetails && $carriageDetails->arrival_pass_ids) {
            $currentPassIds = json_decode($carriageDetails->arrival_pass_ids, true) ?? [];
            $updatedPassIds = array_diff($currentPassIds, $deletedPassIds);

            DB::table('ozon_carriage_details')
                ->where('carriage_id', $carriageId)
                ->update([
                    'arrival_pass_ids' => json_encode(array_values($updatedPassIds)),
                    'updated_at' => now(),
                ]);
        }
    }
}
