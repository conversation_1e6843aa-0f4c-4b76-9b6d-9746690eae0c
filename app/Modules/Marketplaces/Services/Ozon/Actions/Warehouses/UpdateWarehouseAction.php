<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Warehouses;

use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Ozon\DTO\UpdateWarehouseDTO;
use Exception;
use Illuminate\Support\Facades\DB;
use Throwable;

readonly class UpdateWarehouseAction
{
    /**
     * @throws Throwable
     * @throws NotFoundException
     */
    public function run(UpdateWarehouseDTO $dto): void
    {
        $fbsWarehouse = DB::table('ozon_fbs_warehouses')
            ->where('id', $dto->FBSWarehouseId)
            ->first();

        if (!$fbsWarehouse) {
            throw new NotFoundException('FBS Warehouse not found');
        }

        try {
            DB::beginTransaction();

            DB::table('ozon_fbs_orders')
                ->where('needs_warehouse_mapping', true)
                ->where('ozon_warehouse_id', $fbsWarehouse->ozon_warehouse_id)
                ->update([
                    'warehouse_id' => $dto->warehouseId,
                    'updated_at' => now(),
                    'needs_warehouse_mapping' => false
                ]);

            DB::table('ozon_fbs_warehouses')
                ->where('id', $dto->FBSWarehouseId)
                ->update([
                    'warehouse_id' => $dto->warehouseId,
                    'name' => $dto->name,
                    'updated_at' => now(),
                ]);

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
