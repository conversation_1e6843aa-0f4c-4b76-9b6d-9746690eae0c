<?php

namespace App\Modules\Marketplaces\Services\Ozon\Actions\Products;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

readonly class GetProductsToMatchAction
{
    /**
     * Получает список товаров к сопоставлению для указанного кабинета и интеграции
     *
     * @param string $cabinetId ID кабинета
     * @param string $integrationId ID интеграции
     * @return Collection Коллекция товаров к сопоставлению
     */
    public function run(string $cabinetId, string $integrationId): Collection
    {
        return DB::table('ozon_products_to_match')
            ->where('cabinet_id', $cabinetId)
            ->where('integration_id', $integrationId)
            ->where('is_matched', false)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
