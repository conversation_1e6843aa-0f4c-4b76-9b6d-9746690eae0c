<?php

namespace App\Modules\Marketplaces\Services\Ozon\Jobs;

use App\Modules\Marketplaces\Services\Ozon\Actions\Carriages\SyncCarriageDetailsAction;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

/**
 * Джоба для синхронизации детальной информации об отгрузках с Ozon
 */
class SyncCarriageDetailsJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    public int $timeout = 300; // 5 минут
    public int $tries = 3;

    /**
     * Выполнить джобу
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            Log::info('Starting carriage details sync job');

            $action = new SyncCarriageDetailsAction();
            $stats = $action->run(
                maxAttempts: 5,
                batchSize: 50
            );

            Log::info('Carriage details sync job completed', $stats);

        } catch (Exception $e) {
            Log::error('Carriage details sync job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Обработать неудачное выполнение джобы
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('Carriage details sync job failed permanently', [
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
