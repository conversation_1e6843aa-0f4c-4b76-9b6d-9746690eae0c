<?php

namespace App\Modules\Marketplaces\Services\Ozon\Enums;

enum FbsOrderAvaibleActionsEnum: string
{
    case ARBITRATION = 'arbitration';
    case AWAITING_DELIVERY = 'awaiting_delivery';
    case CAN_CREATE_CHAT = 'can_create_chat';
    case CANCEL = 'cancel';
    case CLICK_TRACK_NUMBER = 'click_track_number';
    case CUSTOMER_PHONE_AVAILABLE = 'customer_phone_available';
    case HAS_WEIGHT_PRODUCTS = 'has_weight_products';
    case HIDE_REGION_AND_CITY = 'hide_region_and_city';
    case INVOICE_GET = 'invoice_get';
    case INVOICE_SEND = 'invoice_send';
    case INVOICE_UPDATE = 'invoice_update';
    case LABEL_DOWNLOAD_BIG = 'label_download_big';
    case LABEL_DOWNLOAD_SMALL = 'label_download_small';
    case LABEL_DOWNLOAD = 'label_download';
    case NON_INT_DELIVERED = 'non_int_delivered';
    case NON_INT_DELIVERING = 'non_int_delivering';
    case NON_INT_LAST_MILE = 'non_int_last_mile';
    case PRODUCT_CANCEL = 'product_cancel';
    case SET_CUTOFF = 'set_cutoff';
    case SET_TIMESLOT = 'set_timeslot';
    case SET_TRACK_NUMBER = 'set_track_number';
    case SHIP_ASYNC_IN_PROCESS = 'ship_async_in_process';
    case SHIP_ASYNC_RETRY = 'ship_async_retry';
    case SHIP_ASYNC = 'ship_async';
    case SHIP_WITH_ADDITIONAL_INFO = 'ship_with_additional_info';
    case SHIP = 'ship';
    case UPDATE_CIS = 'update_cis';
}
