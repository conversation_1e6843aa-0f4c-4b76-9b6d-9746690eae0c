<?php

namespace App\Modules\Marketplaces\Services\Ozon\Services\FBS;

use App\Exceptions\NotFoundException;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\CancelFBSOrderAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\CancelFBSOrderItemsAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\CollectFbsOrderAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\ConfirmFBSOrderAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\GetCancelReasonsOrderAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\GetPackageLabelsAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\LoadFBSRFBSOrdersAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBS\SetFbsOrderItemCountryAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\GetOrdersAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\GetTimeslotChangeRestrictionsAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\SetCutoffDateAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\SetDeliveredStatusAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\SetDeliveringStatusAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\SetLastMileStatusAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\SetTimeslotAction;
use App\Modules\Marketplaces\Services\Ozon\Enums\OrderDeliveryTypeEnum;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Throwable;

class FBSOrdersService
{
    public function setCutoffDate(string $orderId, string $newCutoffDate): void
    {
        (new SetCutoffDateAction())->run($orderId, $newCutoffDate);
    }

    /**
     * Получение списка заказов с фильтрацией
     */
    public function getOrders(string $integrationId, array $filters = []): Collection
    {
        return (new GetOrdersAction())->run($integrationId, OrderDeliveryTypeEnum::FBS->value, $filters);
    }

    /**
     * @throws NotFoundException
     */
    public function confirmFBSOrder(string $orderId): void
    {
        (new ConfirmFBSOrderAction())->run($orderId);
    }

    /**
     * Загрузка FBS заказов
     *
     * @throws BindingResolutionException
     * @throws Exception
     * @throws Throwable
     */
    public function loadOrders(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadFBSRFBSOrdersAction())->run($integrationId, $dateFrom, $dateTo);
    }

    public function getCancelReasons(string $integrationId, array $relatedPostingNumbers): object|array
    {
        return (new GetCancelReasonsOrderAction())->run($integrationId, $relatedPostingNumbers);
    }

    public function cancelOrder(
        int $cancelReasonId,
        string $orderId,
        ?string $cancelReasonMessage,
    ): void {
        (new CancelFBSOrderAction())->run(
            orderId: $orderId,
            cancelReasonId: $cancelReasonId,
            cancelReasonMessage: $cancelReasonMessage
        );
    }

    public function cancelOrderItems(
        int $cancelReasonId,
        string $orderId,
        string $cancelReasonMessage,
        array $items,
    ): bool {
        return (new CancelFBSOrderItemsAction())->run(
            orderId: $orderId,
            cancelReasonId: $cancelReasonId,
            cancelReasonMessage: $cancelReasonMessage,
            items: $items
        );
    }

    public function setProductCountry(
        string $orderId,
        int $productId,
        string $countryIsoCode,
    ): void {
        (new SetFbsOrderItemCountryAction())->run(
            orderId: $orderId,
            productId: $productId,
            countryIsoCode: $countryIsoCode
        );
    }

    public function collectOrder(string $orderId): void
    {
        (new CollectFBSOrderAction())->run($orderId);
    }

    public function getPackageLabels(array $orderIds): object
    {
        return (new GetPackageLabelsAction())->run($orderIds);
    }

    public function setTimeslot(string $orderId, array $newTimeslot): void
    {
        (new SetTimeslotAction())->run($orderId, $newTimeslot);
    }

    public function getTimeslotChangeRestrictions(string $orderId): array
    {
        return (new GetTimeslotChangeRestrictionsAction())->run($orderId);
    }

    public function setDeliveringStatus(string $integrationId, array $orderIds): void
    {
        (new SetDeliveringStatusAction())->run($integrationId, $orderIds);
    }

    public function setLastMileStatus(string $integrationId, array $orderIds): void
    {
        (new SetLastMileStatusAction())->run($integrationId, $orderIds);
    }

    public function setDeliveredStatus(string $integrationId, array $orderIds): void
    {
        (new SetDeliveredStatusAction())->run($integrationId, $orderIds);
    }
}
