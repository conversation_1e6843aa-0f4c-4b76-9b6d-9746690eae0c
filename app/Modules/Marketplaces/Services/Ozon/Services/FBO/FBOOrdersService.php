<?php

namespace App\Modules\Marketplaces\Services\Ozon\Services\FBO;

use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\FBO\LoadFBOOrdersAction;
use App\Modules\Marketplaces\Services\Ozon\Actions\Orders\GetOrdersAction;
use App\Modules\Marketplaces\Services\Ozon\Enums\OrderDeliveryTypeEnum;
use Illuminate\Support\Collection;

class FBOOrdersService
{
    public function loadOrders(string $integrationId, ?string $dateFrom = null, ?string $dateTo = null): void
    {
        (new LoadFBOOrdersAction())->run($integrationId, $dateFrom, $dateTo);
    }

    public function get(string $integrationId, array $filters): Collection
    {
        return (new GetOrdersAction())->run($integrationId, OrderDeliveryTypeEnum::FBO->value, $filters);
    }
}
