<?php

use App\Modules\Marketplaces\Enums\SyncStatusEnum;
use App\Modules\Marketplaces\Services\Ozon\Enums\OrderNumberingTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_integrations', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('cabinet_id');
            $table->string('shop_name', 255);

            $table->text('client_id');
            $table->text('api_key');

            $table->boolean('connection_status')->default(false);

            $table->foreignUuid('commission_contract_id')->references('id')->on('contracts');
            $table->foreignUuid('legal_entity_id')->constrained();
            $table->foreignUuid('department_id')->constrained();
            $table->foreignUuid('contractor_id')->constrained();
            $table->foreignUuid('sales_channel_id')->nullable()->references('id')->on('sales_channels');
            $table->timestamps();

            $table->unique(['client_id', 'cabinet_id']);
            $table->index('cabinet_id');
        });

        Schema::create('ozon_price_settings', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_id');

            $table->foreignUuid('price_id')->nullable()->references('id')->on('cabinet_prices');
            $table->foreignUuid('prediscount_price_id')->nullable()->references('id')->on('cabinet_prices');
            $table->foreignUuid('min_price')->nullable()->references('id')->on('cabinet_prices');

            $table->boolean('auto_sync')->default(false);
            $table->string('sync_status')->default(SyncStatusEnum::NOT->value);

            $table->timestamps();

            $table->foreign('integration_id')->references('id')->on('ozon_integrations')->cascadeOnDelete();
            $table->index('integration_id');
        });

        Schema::create('ozon_order_settings', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_id');

            $table->string('numbering_type', 50)->default(OrderNumberingTypeEnum::OZON->value);

            $table->boolean('add_prefix_to_orders')->default(false);
            $table->string('order_prefix', 50)->default('Ozon');

            $table->boolean('use_common_agreement')->default(false);
            $table->boolean('sync_statuses')->default(false);
            $table->boolean('auto_confirm_orders')->default(false);
            $table->boolean('reserve_from_inventory')->default(true);
            $table->boolean('send_mark_codes')->default(false);

            $table->boolean('auto_sync')->default(false);
            $table->string('sync_status')->default(SyncStatusEnum::NOT->value);

            $table->timestamps();

            $table->unique('integration_id');
            $table->foreign('integration_id')->references('id')->on('ozon_integrations')->cascadeOnDelete();
            $table->index('integration_id');
            $table->index('auto_confirm_orders');
        });

        Schema::create('ozon_report_settings', static function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_id');

            $table->boolean('auto_sync')->default(false);
            $table->string('sync_status')->default(SyncStatusEnum::NOT->value);

            $table->timestamps();

            $table->unique('integration_id');
            $table->foreign('integration_id')->references('id')->on('ozon_integrations')->cascadeOnDelete();
            $table->index('integration_id');
            $table->index('auto_sync');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_report_settings');
        Schema::dropIfExists('ozon_order_settings');
        Schema::dropIfExists('ozon_price_settings');
        Schema::dropIfExists('ozon_integrations');
    }
};
