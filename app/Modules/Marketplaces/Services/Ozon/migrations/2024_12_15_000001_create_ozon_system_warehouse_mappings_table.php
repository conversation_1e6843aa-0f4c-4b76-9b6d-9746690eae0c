<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ozon_system_warehouse_mappings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->timestamps();

            // ID склада в системе Ozon
            $table->unsignedBigInteger('ozon_warehouse_id');
            
            // ID склада в нашей системе
            $table->foreignUuid('system_warehouse_id')->references('id')->on('warehouses')->cascadeOnDelete();

            // Уникальный индекс для предотвращения дублирования (включает оба поля для быстрого поиска)
            $table->unique(['ozon_warehouse_id', 'system_warehouse_id'], 'unique_ozon_system_warehouse_mapping');
            
            // Дополнительный индекс для поиска по system_warehouse_id
            $table->index('system_warehouse_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ozon_system_warehouse_mappings');
    }
}; 