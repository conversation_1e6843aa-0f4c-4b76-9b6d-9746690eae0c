<?php

namespace App\Jobs;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;
use App\Contracts\Repositories\SaleChannelTypesRepositoryContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Repositories\WarehousesRepositoryContract;
use App\Enums\Api\Internal\NumberingType;
use App\Enums\Api\Internal\StatusTypeEnum;
use App\Models\User;
use App\Traits\HasOrderedUuid;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class CreateCabinetSettingsJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable;
    use HasOrderedUuid;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly string $cabinetId,
        private readonly User $user
    ) {
    }

    public function uniqueId(): string
    {
        return static::class . ':' . $this->cabinetId;
    }

    public function uniqueFor(): int
    {
        return 600;
    }

    public function handle(
        CabinetSettingRepositoryContract $repostory,
        EmployeeRepositoryContract $employeesRepository,
        CabinetEmployeeRepositoryContract $cabinetEmployeeRepository,
        DepartmentsRepositoryContract $departmentsRepository,
        VatRatesRepositoryContract $vatRatesRepository,
        ProfitTaxRatesRepositoryContract $taxRatesRepository,
        GlobalCurrenciesRepositoryContract $currenciesRepository,
        CabinetCurrenciesRepositoryContract $cabinetCurrenciesRepository,
        SalesChannelsRepositoryContract $salesChannelsRepository,
        SaleChannelTypesRepositoryContract $salesChannelTypesRepository,
        WarehousesRepositoryContract $warehouseRepository
    ): void {
        $id = $this->cabinetId;
        $user = $this->user;

        DB::transaction(function () use ($id, $user, $repostory, $employeesRepository, $cabinetEmployeeRepository, $departmentsRepository, $vatRatesRepository, $taxRatesRepository, $currenciesRepository, $cabinetCurrenciesRepository, $salesChannelsRepository, $salesChannelTypesRepository, $warehouseRepository) {

            $existingSettings = $repostory->show($id);
            if ($existingSettings) {
                return;
            }

            $repostory->insert([
                'cabinet_id' => $id,
                'numbering_type' => NumberingType::CPNumbers
                ]);

            $employeeId = $this->generateUuid();
            $departmentId = $this->generateUuid();


            $departmentsRepository->insert([
                'id' => $departmentId,
                'name' => 'Общий',
                'cabinet_id' => $id,
                'is_default' => true,
            ]);
            $employeesRepository->insert([
                'id' => $employeeId,
                'user_id' => $user->id,
                'lastname' => $user->lastname,
                'firstname' => $user->firstname,
                'patronymic' => $user->patronymic,
                'email' => $user->email,
                'department_id' => $departmentId
            ]);
            $cabinetEmployeeRepository->insert([
                'cabinet_id' => $id,
                'employee_id' => $employeeId,
            ]);

            $salesChannelId = $this->generateUuid();
            $salesChannelTypes = $salesChannelTypesRepository->get(
                ['id', 'name']
            );
            $salesChannelsRepository->insert(
                [
                    'id' => $salesChannelId,
                    'cabinet_id' => $id,
                    'name' => 'Общий',
                    'is_default' => true,
                    'employee_id' => $employeeId,
                    'department_id' => $departmentId,
                    'sales_channel_type_id' => $salesChannelTypes->where('name', 'Розничные продажи')->first()->id
                ]
            );

            $departmentsRepository->update($departmentId, ['sales_channel_id' => $salesChannelId]);

            $vatRates = [0, 5, 7, 10, 20];
            $taxRates = [0, 1, 5, 20, 25];

            $insertVatRates = [];
            $insertTaxRates = [];
            foreach ($vatRates as $rate) {
                $insertVatRates[] = [
                    'id' => $this->generateUuid(),
                    'cabinet_id' => $id,
                    'employee_id' => $employeeId,
                    'department_id' => $departmentId,
                    'rate' => $rate,
                    'description' => "НДС {$rate}%",
                    'is_default' => true
                ];
            }
            foreach ($taxRates as $taxRate) {
                $insertTaxRates [] = [
                    'id' => $this->generateUuid(),
                    'cabinet_id' => $id,
                    'employee_id' => $employeeId,
                    'department_id' => $departmentId,
                    'rate' => $taxRate,
                    'description' => $taxRate == 0 ? 'Без налога' : "Налог {$taxRate}%",
                    'is_default' => true
                ];
            }

            $rub = $currenciesRepository->getByExternalId('R00000');
            $cabinetCurrenciesRepository->insert(
                [
                    'id' => $this->generateUuid(),
                    'cabinet_id' => $id,
                    'is_accouting' => true,
                    'currency_id' => $rub->id,
                    'external_id' => 'R00000',
                    'employee_id' => $employeeId,
                    'department_id' => $departmentId
                ]
            );

            $type = StatusTypeEnum::CONTRACTORS->value;

            $time = Carbon::now();
            DB::table('statuses')
                ->insert(
                    [
                        [
                            'id' => $this->generateUuid(),
                            'cabinet_id' => $id,
                            'name' => 'Новый',
                            'color' => '#fff',
                            'type' => $type,
                            'created_at' => $time,
                            'updated_at' => $time
                        ],
                        [
                            'id' => $this->generateUuid(),
                            'cabinet_id' => $id,
                            'name' => 'Выслано предложение',
                            'color' => '#fff',
                            'type' => $type,
                            'created_at' => $time,
                            'updated_at' => $time
                        ],
                        [
                            'id' => $this->generateUuid(),
                            'cabinet_id' => $id,
                            'name' => 'Переговоры',
                            'color' => '#fff',
                            'type' => $type,
                            'created_at' => $time,
                            'updated_at' => $time
                        ],
                        [
                            'id' => $this->generateUuid(),
                            'cabinet_id' => $id,
                            'name' => 'Сделка заключена',
                            'color' => '#fff',
                            'type' => $type,
                            'created_at' => $time,
                            'updated_at' => $time
                        ],
                    ]
                );

            $vatRatesRepository->insert($insertVatRates);
            $taxRatesRepository->insert($insertTaxRates);
            $warehouseRepository->insert([
                'id' => $this->generateUuid(),
                'cabinet_id' => $id,
                'name' => 'Основной склад',
                'employee_id' => $employeeId,
                'department_id' => $departmentId,
                'is_default' => true,
                'is_common' => true
            ]);
        });
    }
}
