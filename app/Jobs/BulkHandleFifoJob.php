<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Contracts\Services\Internal\FifoServiceContract;
use Illuminate\Contracts\Container\BindingResolutionException;

class BulkHandleFifoJob implements ShouldQueue
{
    use Queueable;

    protected Collection $shipmentItems;
    protected FifoServiceContract $fifoService;
    protected bool $delete;

    /**
     * @throws BindingResolutionException
     */
    public function __construct(Collection $shipmentItems, bool $delete = false)
    {
        $this->shipmentItems = $shipmentItems;
        $this->fifoService = app()->make(FifoServiceContract::class);
        $this->delete = $delete;
    }

    public function handle(): void
    {
        // Группируем элементы отгрузки по cabinet_id и product_id для оптимизации
        $groupedItems = $this->shipmentItems->groupBy(function ($item) {
            return $item->cabinet_id . '_' . $item->product_id;
        });

        foreach ($groupedItems as $items) {
            // Обрабатываем каждую группу элементов одним вызовом
            $this->fifoService->handleBulk($items->pluck('shipment_item_id')->toArray(), $this->delete);
        }
    }
}
