<?php

namespace App\Jobs\FIFOJobs;

use App\Contracts\Repositories\AcceptanceItemsRepositoryContract;
use App\Contracts\Repositories\ShipmentsRepositoryContract;
use App\Contracts\Repositories\WarehouseItemsRepositoryContract;
use App\Contracts\Services\Internal\FifoServiceContract;
use App\Services\Api\Internal\Procurement\Acceptances\AcceptancesService\DTO\AcceptanceDto;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Queue;

class RecalculationAfterUpdateAcceptanceJob
{
    use Queueable;

    protected string $resourceId;
    protected object $oldResource;
    protected AcceptanceDto $newResource;
    protected FifoServiceContract $fifoService;
    protected ShipmentsRepositoryContract $shipmentRepository;
    protected WarehouseItemsRepositoryContract $warehouseItemsRepository;
    protected AcceptanceItemsRepositoryContract $acceptanceItemRepository;

    public function __construct(object $oldResource, AcceptanceDto $newResource)
    {
        $this->oldResource = $oldResource;
        $this->newResource = $newResource;
        $this->resourceId = $newResource->resourceId;
    }

    /**
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        $this->acceptanceItemRepository = app()->make(AcceptanceItemsRepositoryContract::class);
        $this->warehouseItemsRepository = app()->make(WarehouseItemsRepositoryContract::class);
        $this->shipmentRepository = app()->make(ShipmentsRepositoryContract::class);
        $this->fifoService = app()->make(FifoServiceContract::class);

        $acceptanceDate = Carbon::parse($this->oldResource->date_from)->toDateString();
        $requestDate = Carbon::parse($this->newResource->dateFrom)->toDateString();

        if (
            $acceptanceDate !== $requestDate
            ||
            $this->oldResource->warehouse_id !== $this->newResource->warehouseId
            ||
            $this->oldResource->held !== $this->newResource->held
        ) {
            //Если изменили дату приемки, склад, проведение
            // Пересчитать все отгурзки по продукту этой приемки начиная с меньшей из двух этих дат
            $items = $this->acceptanceItemRepository->get($this->resourceId);
            $date = min($acceptanceDate, $requestDate);

            foreach ($items as $item) {

                $updateArray = [
                    'received_at' => $requestDate,
                    'warehouse_id' => $this->newResource->warehouseId
                ];

                $this->warehouseItemsRepository->update(
                    $updateArray,
                    $this->resourceId,
                    $item->product_id
                );

                $shipmentItem = $this->shipmentRepository
                    ->findShipmentItemIdByProductAndDate($item->product_id, $date);

                if ($shipmentItem) {
                    $this->fifoService->handle($shipmentItem);
                }
            }
        }
    }

}
