<?php

namespace App\Jobs\FIFOJobs;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Contracts\Services\Internal\GoodsTransferFifoServiceContract;

class BulkHandleGoodsTransferFifoJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use Batchable;

    protected Collection $transferItems;
    protected GoodsTransferFifoServiceContract $fifoService;
    public bool $delete = false;

    public function __construct(Collection $transferItems, bool $delete = false)
    {
        $this->transferItems = $transferItems;
        $this->delete = $delete;
    }

    public function handle(): void
    {
        $this->fifoService = app()->make(GoodsTransferFifoServiceContract::class);

        // Группируем элементы перемещения по cabinet_id и product_id для оптимизации
        $groupedItems = $this->transferItems->groupBy(function ($item) {
            return $item->cabinet_id . '_' . $item->product_id;
        });

        foreach ($groupedItems as $items) {
            // Обрабатываем каждую группу элементов одним вызовом
            $this->fifoService->handleBulk($items->pluck('goods_transfer_item_id')->toArray(), $this->delete);
        }
    }
}
