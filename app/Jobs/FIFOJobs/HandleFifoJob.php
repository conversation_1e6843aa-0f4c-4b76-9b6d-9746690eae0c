<?php

namespace App\Jobs\FIFOJobs;

use Illuminate\Bus\Queueable;
use Illuminate\Bus\Batchable;
use Illuminate\Support\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Contracts\Services\Internal\FifoServiceContract;

class HandleFifoJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;
    use Batchable;

    protected Collection|string $shipmentItems;
    protected FifoServiceContract $fifoService;
    public bool $delete = false;

    public function __construct(Collection|string $shipmentItems, bool $delete = false)
    {
        $this->shipmentItems = $shipmentItems;
        $this->delete = $delete;
    }

    public function handle(): void
    {
        $this->fifoService = app()->make(FifoServiceContract::class);

        if ($this->shipmentItems instanceof Collection) {
            foreach ($this->shipmentItems as $shipmentItem) {
                $this->fifoService->handle($shipmentItem->shipment_item_id, $this->delete);
            }
        } else {
            $this->fifoService->handle($this->shipmentItems, $this->delete);
        }
    }

}
