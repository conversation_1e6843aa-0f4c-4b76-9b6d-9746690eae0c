<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Contracts\Repositories\FilesRepositoryContract;
use Throwable;

class BulkDeleteFilesJob implements ShouldQueue
{
    use Queueable;
    use Dispatchable;
    use SerializesModels;
    use InteractsWithQueue;

    private array $relatedIds;
    private FilesRepositoryContract $filesRepository;
    public int $tries = 3;
    public int $maxExceptions = 3;
    public int $backoff = 60;

    public function __construct(array $relatedIds)
    {
        $this->relatedIds = $relatedIds;
        $this->filesRepository = app()->make(FilesRepositoryContract::class);
    }

    /**
     * @throws Throwable
     */
    public function handle(): void
    {
        foreach ($this->relatedIds as $relatedId) {
            try {
                $toDeleteIds = [];
                foreach ($this->filesRepository->getByRelatedId($relatedId) as $file) {
                    try {
                        if (Storage::disk('s3-docs')->exists($file->path)) {
                            Storage::disk('s3-docs')->delete($file->path);
                        }
                        $toDeleteIds[] = $relatedId;
                    } catch (Throwable $e) {
                        Log::error('Failed to delete file from storage', [
                            'file_path' => $file->path,
                            'related_id' => $relatedId,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
                $this->filesRepository->bulkDeleteByRelatedId($toDeleteIds);
            } catch (Throwable $e) {
                Log::error('Failed to process files deletion', [
                    'related_id' => $relatedId,
                    'error' => $e->getMessage()
                ]);
                throw $e; // Пробрасываем ошибку для повторных попыток
            }
        }
    }

    public function failed(Throwable $exception): void
    {
        Log::error('BulkDeleteFilesJob failed', [
            'related_ids' => $this->relatedIds,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
