<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Purchases\AcceptanceItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemCalculateDTO;
use App\Services\Api\Internal\Procurement\AcceptanceItemsService\DTO\AcceptanceItemDto;

readonly class AcceptanceItemPolicy implements AcceptanceItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof AcceptanceItemDto) {
            return;
        }

        $acceptance = $this->authService->validateRelationAccess(
            'acceptances',
            $dto->acceptanceId,
            null,
            PermissionNameEnum::ACCEPTANCES->value,
            'update',
        );

        $product = $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $acceptance->cabinet_id,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );

        if ($product->archived_at) {
            throw new AccessDeniedException('Product is archived');
        }

        if ($dto->vat_rate_id) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vat_rate_id,
                $acceptance->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }

        if ($dto->countryId) {
            $this->authService->validateRelationAccess(
                'countries',
                $dto->countryId,
                $acceptance->cabinet_id,
            );
        }
    }

    /**
     * @throws AccessDeniedException
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof AcceptanceItemDto) {
            return;
        }

        // Проверяем права на acceptance_items, используя cabinet_id из acceptance
        $item = $this->authService->validateRelationAccess(
            'acceptance_items',
            $dto->resourceId,
            null,
            PermissionNameEnum::ACCEPTANCES->value,
            'update',
            null,
            [
                'table' => 'acceptances',
                'field' => 'acceptance_id',
                'value' => $dto->resourceId
            ]
        );

        if ($dto->vat_rate_id && $item->vat_rate_id !== $dto->vat_rate_id) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vat_rate_id,
                $item->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }

        if ($dto->countryId && $item->country_id !== $dto->countryId) {
            $this->authService->validateRelationAccess(
                'countries',
                $dto->countryId,
                $item->cabinet_id,
            );
        }
    }

    public function index(string $acceptanceId): void
    {
        $this->authService->validateRelationAccess(
            'acceptances',
            $acceptanceId,
            null,
            PermissionNameEnum::ACCEPTANCES->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'acceptance_items',
            $resourceId,
            null,
            PermissionNameEnum::ACCEPTANCES->value,
            'view',
            null,
            [
                'table' => 'acceptances',
                'field' => 'acceptance_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'acceptance_items',
            $resourceId,
            null,
            PermissionNameEnum::ACCEPTANCES->value,
            'update',
            null,
            [
                'table' => 'acceptances',
                'field' => 'acceptance_id',
                'value' => $resourceId
            ]
        );
    }

    public function calculateItemMetrics(AcceptanceItemCalculateDTO $dto): void
    {
        $this->authService->hasAccessToCabinet(
            $dto->cabinetId,
            [PermissionNameEnum::GOODS_AND_SERVICES->value => 'view']
        );

        $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $dto->cabinetId,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );

        $this->authService->validateRelationAccess(
            'warehouses',
            $dto->warehouseId,
            $dto->cabinetId,
            PermissionNameEnum::WAREHOUSES->value,
            'view'
        );
    }
}
