<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseAddressPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Warehouses\WarehouseAddressService\DTO\WarehouseAddressDTO;

readonly class WarehouseAddressPolicy implements WarehouseAddressPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseAddressDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinet_id);

        $this->authService->validateRelationAccess(
            'countries',
            $dto->country_id,
            $dto->cabinet_id
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof WarehouseAddressDTO) {
            return;
        }

        $warehouseAddress = $this->authService->validateRelationAccess(
            entity: 'warehouse_addresses',
            entityId: $dto->resourceId
        );

        $this->authService->validateRelationAccess(
            'countries',
            $dto->country_id,
            $warehouseAddress->cabinet_id
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_addresses',
            entityId: $resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'warehouse_addresses',
            entityId: $resourceId
        );
    }
}
