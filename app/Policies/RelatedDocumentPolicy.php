<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\RelatedDocumentPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Documents\RelatedDocumentsService\DTO\RelatedDocumentDTO;

readonly class RelatedDocumentPolicy implements RelatedDocumentPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof RelatedDocumentDTO) {
            return;
        }

        $this->authService->validateRelationAccess('documents', $dto->documentId);
        $this->authService->validateRelationAccess('documents', $dto->bindedDocumentId);
    }

    public function update(User $user, DtoContract $dto): void
    {
        //
    }

    public function delete(User $user, string $resourceId): void
    {
        //
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('documents', $resourceId);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->validateRelationAccess('documents', $cabinetId);
    }
}
