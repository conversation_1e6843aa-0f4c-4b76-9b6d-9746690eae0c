<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\AttributePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Goods\Other\AttributesService\DTO\AttributeDTO;

readonly class AttributePolicy implements AttributePolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof AttributeDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);

        if ($dto->groupId) {
            $this->authService->validateRelationAccess('attribute_groups', $dto->groupId, $dto->cabinetId);
        }
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof AttributeDTO) {
            return;
        }

        $attribute = $this->authService->validateRelationAccess('attributes', $dto->resourceId);

        if ($attribute->attribute_groups_id != $dto->groupId && $dto->groupId) {
            $this->authService->validateRelationAccess(
                'attribute_groups',
                $dto->groupId,
                $attribute->cabinet_id
            );
        }
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('attributes', $resourceId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('attributes', $resourceId);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
