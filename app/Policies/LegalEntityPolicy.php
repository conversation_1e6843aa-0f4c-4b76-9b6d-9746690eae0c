<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\LegalEntityPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\References\LegalEntitiesService\DTO\LegalEntityDTO;
use App\Traits\HasEmployeeAndDepartment;
use Exception;
use RuntimeException;

readonly class LegalEntityPolicy implements LegalEntityPolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws Exception
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof LegalEntityDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId, [PermissionNameEnum::LEGAL_ENTITIES->value => 'create']);

        if (isset($dto->detail['vat_rate'])) {
            $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->detail['vat_rate'],
                $dto->cabinetId,
                PermissionNameEnum::VAT_RATES->value,
                'view'
            );
        }
        if (isset($dto->detail['tax_rate'])) {
            $this->authService->validateRelationAccess(
                'profit_tax_rates',
                $dto->detail['tax_rate'],
                $dto->cabinetId
            );
        }

        if ($dto->logo) {
            $logo = $this->authService->validateRelationAccess(
                'files',
                $dto->logo,
                $dto->cabinetId
            );
            if (!in_array($logo->mime_type, ['image/jpeg', 'image/png', 'image/jpg'])) {
                throw new RuntimeException('Allowed mime types: image/jpeg, image/png, image/jpg');
            }
        }

        if (isset($dto->head['head_signature_image_id'])) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->head['head_signature_image_id'],
                $dto->cabinetId
            );

            if ($image->mime_type !== 'image/png') {
                throw new RuntimeException('Allowed mime type: image/png');
            }
        }
        if (isset($dto->head['accountant_signature_image_id'])) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->head['accountant_signature_image_id'],
                $dto->cabinetId
            );

            if ($image->mime_type !== 'image/png') {
                throw new RuntimeException('Allowed mime type: image/png');
            }
        }
        if (isset($dto->head['stamp_image_id'])) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->head['stamp_image_id'],
                $dto->cabinetId
            );

            if ($image->mime_type !== 'image/png') {
                throw new RuntimeException('Allowed mime type: image/png');
            }
        }
        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof LegalEntityDTO) {
            return;
        }

        $legalEntity = $this->authService->validateRelationAccess(
            'legal_entities',
            $dto->resourceId,
            null,
            PermissionNameEnum::LEGAL_ENTITIES->value,
            'update'
        );

        if ($dto->logo && $legalEntity->logo_image_id !== $dto->logo) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->logo,
                $legalEntity->cabinet_id
            );

            if (!in_array($image->mime_type, ['image/jpeg', 'image/png', 'image/jpg'])) {
                throw new RuntimeException('Allowed mime types: image/jpeg, image/png, image/jpg');
            }
        }

        if (isset($dto->head['head_signature_image_id'])) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->head['head_signature_image_id'],
                $legalEntity->cabinet_id
            );

            if ($image->mime_type !== 'image/png') {
                throw new RuntimeException('Allowed mime type: image/png');
            }
        }
        if (isset($dto->head['accountant_signature_image_id'])) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->head['accountant_signature_image_id'],
                $legalEntity->cabinet_id
            );

            if ($image->mime_type !== 'image/png') {
                throw new RuntimeException('Allowed mime type: image/png');
            }
        }

        if (isset($dto->head['stamp_image_id'])) {
            $image = $this->authService->validateRelationAccess(
                'files',
                $dto->head['stamp_image_id'],
                $legalEntity->cabinet_id
            );

            if ($image->mime_type !== 'image/png') {
                throw new RuntimeException('Allowed mime type: image/png');
            }
        }

        if (isset($dto->detail['vat_rate'])) {
            $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->detail['vat_rate'],
                $legalEntity->cabinet_id,
                PermissionNameEnum::VAT_RATES->value,
                'view'
            );
        }
        if (isset($dto->detail['tax_rate'])) {
            $this->authService->validateRelationAccess(
                'profit_tax_rates',
                $dto->detail['tax_rate'],
                $legalEntity->cabinet_id
            );
        }

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $legalEntity->cabinet_id
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'legal_entities',
            $resourceId,
            null,
            PermissionNameEnum::LEGAL_ENTITIES->value,
            'delete'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'legal_entities',
            $resourceId,
            null,
            PermissionNameEnum::LEGAL_ENTITIES->value,
            'view'
        );
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'legal_entities',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::LEGAL_ENTITIES->value,
            'delete',
        );
    }

    public function bulkUpdate(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'legal_entities',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::LEGAL_ENTITIES->value,
            'update',
        );
    }
}
