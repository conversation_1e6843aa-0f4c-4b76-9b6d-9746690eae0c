<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Finances\OutgoingPaymentItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Money\OutgoingPayments\OutgoingPaymentItemsService\DTO\OutgoingPaymentItemDTO;

readonly class OutgoingPaymentItemPolicy implements OutgoingPaymentItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof OutgoingPaymentItemDTO) {
            return;
        }

        $payment = $this->authService->validateRelationAccess(
            'outgoing_payments',
            $dto->outgoing_payment_id,
            null,
            PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'update',
        );

        $this->authService->validateRelationAccess(
            'documents',
            $dto->document_id,
            $payment->cabinet_id,
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof OutgoingPaymentItemDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            'outgoing_payment_items',
            $dto->resourceId,
            null,
            PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'update',
            null,
            [
               'table' => 'outgoing_payments',
               'field' => 'outgoing_payment_id',
               'value' => $dto->resourceId
            ]
        );
    }

    public function index(string $paymentId): void
    {
        $this->authService->validateRelationAccess(
            'outgoing_payments',
            $paymentId,
            null,
            PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'outgoing_payment_items',
            $resourceId,
            null,
            PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'view',
            null,
            [
                'table' => 'outgoing_payments',
                'field' => 'outgoing_payment_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'outgoing_payment_items',
            $resourceId,
            null,
            PermissionNameEnum::OUTGOING_PAYMENTS->value,
            'update',
            null,
            [
                'table' => 'outgoing_payments',
                'field' => 'outgoing_payment_id',
                'value' => $resourceId
            ]
        );
    }
}
