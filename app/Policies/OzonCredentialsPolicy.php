<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\OzonCredentialsPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Ozon\OzonService\OzonCredentialsService\DTO\OzonCredentialsDTO;
use App\Traits\HasEmployeeAndDepartment;

readonly class OzonCredentialsPolicy implements OzonCredentialsPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }


    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof OzonCredentialsDTO) {
            return;
        }
        $this->authService->init();

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        $this->authService->hasAccessToCabinet($dto->cabinetId);

    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof OzonCredentialsDTO) {
            return;
        }

        $this->authService->init();

        $this->authService->validateRelationAccess(
            'ozon_credentials',
            $dto->resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->init();

        $this->authService->validateRelationAccess('ozon_credentials', $resourceId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->init();
        $this->authService->validateRelationAccess('ozon_credentials', $resourceId);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->init();

        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
