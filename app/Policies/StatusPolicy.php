<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\StatusPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\Workspace\StatusesService\DTO\StatusDTO;

readonly class StatusPolicy implements StatusPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof StatusDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof StatusDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            entity: 'statuses',
            entityId: $dto->resourceId,
            operation: 'update'
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'statuses',
            entityId: $resourceId,
            operation: 'delete'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'statuses',
            $resourceId
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
