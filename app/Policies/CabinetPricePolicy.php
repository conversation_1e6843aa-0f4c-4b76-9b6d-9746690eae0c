<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Cabinet\CabinetPricePolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Models\User;
use App\Services\Api\Internal\References\CabinetPricesService\DTO\CabinetPriceDTO;

readonly class CabinetPricePolicy implements CabinetPricePolicyContract
{
    /**
     * Create a new policy instance.
     */
    public function __construct(
        private AuthorizationServiceContract $authService,
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof CabinetPriceDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('cabinet_prices', $resourceId);
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof CabinetPriceDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            'cabinet_prices',
            $dto->resourceId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess('cabinet_prices', $resourceId);
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }
}
