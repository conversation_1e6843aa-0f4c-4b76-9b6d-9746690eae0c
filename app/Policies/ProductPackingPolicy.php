<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Products\ProductPackingPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\Goods\Products\ProductPackingService\DTO\ProductPackingDto;
use App\Traits\HasEmployeeAndDepartment;

readonly class ProductPackingPolicy implements ProductPackingPolicyContract
{
    use HasEmployeeAndDepartment;
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        //
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof ProductPackingDto) {
            return;
        }

        $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            null,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'update'
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        //
    }

    public function view(User $user, string $resourceId): void
    {
        //
        //
    }

    public function index(string $productId): void
    {
        $this->authService->validateRelationAccess(
            entity: 'products',
            entityId: $productId,
            permission: PermissionNameEnum::GOODS_AND_SERVICES->value,
            operation: 'view'
        );
    }
}
