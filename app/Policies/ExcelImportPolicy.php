<?php

namespace App\Policies;

use App\Models\User;
use App\Contracts\DtoContract;
use App\Traits\HasEmployeeAndDepartment;
use App\Contracts\Policies\ExcelImportPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;

readonly class ExcelImportPolicy implements ExcelImportPolicyContract
{
    use HasEmployeeAndDepartment;

    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }


    public function create(User $user, DtoContract $dto): void
    {
        $this->authService->init();

        $this->checkEmployeeAndDepartmentIds(
            $dto->employeeId,
            $dto->departmentId,
            $dto->cabinetId
        );

        $this->authService->hasAccessToCabinet($dto->cabinetId);

    }

    public function update(User $user, DtoContract $dto): void
    {

    }

    public function delete(User $user, string $resourceId): void
    {

    }

    public function view(User $user, string $resourceId): void
    {

    }

    public function index(string $cabinetId): void
    {

    }
}
