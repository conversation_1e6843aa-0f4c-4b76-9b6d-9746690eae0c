<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Directories\MeasurementUnitPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Models\User;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\DTO\MeasurementUnitDTO;

readonly class MeasurementUnitPolicy implements MeasurementUnitPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof MeasurementUnitDTO) {
            return;
        }

        $this->authService->hasAccessToCabinet($dto->cabinetId);
        $this->authService->validateRelationAccess(
            'measurement_unit_groups',
            $dto->groupId,
            $dto->cabinetId
        );
    }

    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof MeasurementUnitDTO) {
            return;
        }

        $this->authService->validateRelationAccess(
            'measurement_units',
            $dto->resourceId,
            $dto->cabinetId
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'measurement_units',
            $resourceId,
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'measurement_units',
            $resourceId,
        );
    }

    public function index(string $cabinetId): void
    {
        $this->authService->hasAccessToCabinet($cabinetId);
    }

    public function bulkDelete(array $data): void
    {
        $this->authService->validateResourcesAccess(
            'measurement_units',
            $data['cabinet_id'],
            $data['ids'],
            PermissionNameEnum::MEASUREMENT_UNITS->value,
            'delete',
        );
    }
}
