<?php

namespace App\Policies;

use App\Contracts\DtoContract;
use App\Contracts\Policies\Sales\ReceivedComissionReportsRealizedItemPolicyContract;
use App\Contracts\Services\AuthorizationServiceContract;
use App\Enums\Api\Internal\PermissionNameEnum;
use App\Exceptions\AccessDeniedException;
use App\Models\User;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\DTO\RealizedItemDTO;

readonly class ReceivedComissionReportRealizedItemPolicy implements ReceivedComissionReportsRealizedItemPolicyContract
{
    public function __construct(
        private AuthorizationServiceContract $authService
    ) {
    }

    /**
     * @throws AccessDeniedException
     */
    public function create(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof RealizedItemDTO) {
            return;
        }

        $report = $this->authService->validateRelationAccess(
            'received_comission_reports',
            $dto->reportId,
            null,
            PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'update',
        );

        $product = $this->authService->validateRelationAccess(
            'products',
            $dto->productId,
            $report->cabinet_id,
            PermissionNameEnum::GOODS_AND_SERVICES->value,
            'view'
        );
        if ($product->archived_at) {
            throw new AccessDeniedException('Product is archived');
        }

        if ($dto->vatRateId) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vatRateId,
                $report->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }
    }

    /**
     * @throws AccessDeniedException
     */
    public function update(User $user, DtoContract $dto): void
    {
        if (!$dto instanceof RealizedItemDTO) {
            return;
        }

        $item = $this->authService->validateRelationAccess(
            'received_comission_report_realized_items',
            $dto->id,
            null,
            PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'update',
            null,
            [
                'table' => 'received_comission_reports',
                'field' => 'report_id',
                'value' => $dto->id
            ]
        );

        if ($dto->vatRateId && $item->vat_rate_id !== $dto->vatRateId) {
            $vatRate = $this->authService->validateRelationAccess(
                'vat_rates',
                $dto->vatRateId,
                $item->cabinet_id,
            );
            if ($vatRate->archived_at) {
                throw new AccessDeniedException('Vat rate is archived');
            }
        }
    }

    public function index(string $shipmentId): void
    {
        $this->authService->validateRelationAccess(
            'received_comission_reports',
            $shipmentId,
            null,
            PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'view'
        );
    }

    public function view(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'received_comission_report_realized_items',
            $resourceId,
            null,
            PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'view',
            null,
            [
                'table' => 'received_comission_reports',
                'field' => 'report_id',
                'value' => $resourceId
            ]
        );
    }

    public function delete(User $user, string $resourceId): void
    {
        $this->authService->validateRelationAccess(
            'received_comission_report_realized_items',
            $resourceId,
            null,
            PermissionNameEnum::RECEIVED_COMISSIONER_REPORTS->value,
            'update',
            null,
            [
                'table' => 'received_comission_reports',
                'field' => 'report_id',
                'value' => $resourceId
            ]
        );
    }
}
