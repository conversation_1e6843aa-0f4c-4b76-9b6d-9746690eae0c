<?php

namespace App\Http\Requests\Api\Internal\CabinetCurrencies;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class CabinetCurrencySetAccoutingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'global_currency_id' => 'required|UUID|exists:global_currencies,id',
        ];
    }
}
