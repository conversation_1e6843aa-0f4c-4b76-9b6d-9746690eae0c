<?php

namespace App\Http\Requests\Api\Internal\CabinetCurrencies;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\CurrencyTypeEnum;
use App\Services\Api\Internal\References\CabinetCurrenciesService\DTO\CabinetCurrencyDTO;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CabinetCurrencyStoreRequest extends FormRequest implements ToDtoContract
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'employee_id' => 'required|UUID',
            'is_common' => 'nullable|boolean',

            'currency_id' => ['required_if:type,' . CurrencyTypeEnum::AUTO->value, 'nullable', 'UUID','exists:global_currencies,id'], // Если добавляем системную валюту в кабинет

            'num_code' => 'required|string', // Если добавляем системную валюту в кабинет, то берем значения из нее
            'char_code' => 'required|string|uppercase',
            'short_name' => 'required|string',
            'name' => 'exclude_if:currency_id,null|nullable|string',

            'type' => ['required', Rule::in(CurrencyTypeEnum::cases())],
            'markup' => 'nullable|numeric',

            'nominal' => 'exclude_if:type,' . CurrencyTypeEnum::AUTO->value . '|required|numeric|min:1', //номинал если тип не AUTO
            'value' => 'exclude_if:type,' . CurrencyTypeEnum::AUTO->value . '|required|numeric',
            'is_reverse' => 'nullable|boolean', //Обратный курс

            'pluralization' => 'nullable|array',
            'pluralization.*.gender' => 'in:m,f',
            'pluralization.*.single' => 'nullable|string',
            'pluralization.*.two' => 'nullable|string',
            'pluralization.*.five' => 'nullable|string',
            'is_other' => 'nullable|boolean',
        ];
    }

    public function toDTO(): CabinetCurrencyDTO
    {
        return CabinetCurrencyDTO::fromArray($this->validated());
    }
}
