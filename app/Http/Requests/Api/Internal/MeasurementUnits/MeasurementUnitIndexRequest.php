<?php

namespace App\Http\Requests\Api\Internal\MeasurementUnits;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\MeasurementUnitEntity;
use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Api\Internal\EntitiesTypeEnum;
use App\Enums\Api\Internal\FilterConditionEnum;

class MeasurementUnitIndexRequest extends FormRequest
{
    public function __construct(
        private readonly MeasurementUnitEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'filters' => 'nullable|array',
            'filters.type.value' => 'string|' . Rule::in(EntitiesTypeEnum::cases()),

            'filters.name.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.name.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.name.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.name.value');
                $condition = $this->input('filters.name.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.short_name.value' => 'string',

            'filters.code.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.code.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.code.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.code.value');
                $condition = $this->input('filters.code.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.search.value' => 'string',

            'filters.updated_at.from' => 'nullable|date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'nullable|date_format:d.m.Y H:i|after_or_equal:filters.updated_at.from',

            'fields' => 'nullable|array',
            'fields.*' => ['string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
