<?php

namespace App\Http\Requests\Api\Internal\MeasurementUnits;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementUnitsService\DTO\MeasurementUnitDTO;
use Illuminate\Foundation\Http\FormRequest;

class MeasurementUnitUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'short_name' => 'required|string',
            'code' => 'nullable|string',
            'name' => 'nullable|string',
            'conversion_factor' => 'required|numeric',
        ];
    }

    public function toDTO(): MeasurementUnitDTO
    {
        return MeasurementUnitDTO::fromArray(
            array_merge(
                $this->validated(),
                ['id' => $this->route('id')]
            )
        );
    }
}
