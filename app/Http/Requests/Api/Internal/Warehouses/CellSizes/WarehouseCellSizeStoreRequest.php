<?php

namespace App\Http\Requests\Api\Internal\Warehouses\CellSizes;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Warehouses\WarehouseCellSizesService\DTO\WarehouseCellSizeDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseCellSizeStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'name' => 'required|string',

            'unlimited_size' => 'nullable|boolean',
            'height' => [
                'required_if:unlimited_size,false',
                'required_without:unlimited_size',
                'exclude_if:unlimited_size,true',
                'numeric'
            ],
            'width' => [
                'required_if:unlimited_size,false',
                'required_without:unlimited_size',
                'exclude_if:unlimited_size,true',
                'numeric'
            ],
            'length' => [
                'required_if:unlimited_size,false',
                'required_without:unlimited_size',
                'exclude_if:unlimited_size,true',
                'numeric'
            ],
            'measurement_unit_size_id' => 'exclude_if:unlimited_size,true|nullable|UUID',
            'volume' => [
                'required_if:unlimited_size,false',
                'required_without:unlimited_size',
                'exclude_if:unlimited_size,true',
                'numeric'
            ],
            'measurement_unit_volume_id' => 'exclude_if:unlimited_size,true|nullable|UUID',

            'unlimited_load_capacity' => 'nullable|boolean',
            'load_capacity' => [
                'required_if:unlimited_load_capacity,false',
                'required_without:unlimited_load_capacity',
                'exclude_if:unlimited_load_capacity,true',
                'numeric'
            ],
            'measurement_unit_load_capacity_id' => 'exclude_if:unlimited_load_capacity,true|nullable|UUID',
        ];
    }

    public function toDTO(): WarehouseCellSizeDTO
    {
        return WarehouseCellSizeDTO::fromArray($this->validated());
    }
}
