<?php

namespace App\Http\Requests\Api\Internal\Warehouses\Contacts\Address;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Warehouses\WarehouseAddressService\DTO\WarehouseAddressDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseAddressUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'country_id' => 'required|UUID',
            'postcode' => 'nullable|string',
            'region' => 'nullable|string',
            'city' => 'nullable|string',
            'street' => 'nullable|string',
            'house' => 'nullable|string',
            'office' => 'nullable|string',
            'other' => 'nullable|string',
            'comment' => 'nullable|string',
        ];
    }

    public function toDTO(): WarehouseAddressDTO
    {
        return WarehouseAddressDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
