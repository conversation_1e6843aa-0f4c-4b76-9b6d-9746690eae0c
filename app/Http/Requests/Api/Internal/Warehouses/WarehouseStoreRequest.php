<?php

namespace App\Http\Requests\Api\Internal\Warehouses;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Warehouses\WarehouseService\DTO\WarehouseDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'name' => 'required|string',
            'work_schedule_id' => 'nullable|UUID',
            'control_free_residuals' => 'nullable|boolean',

            'group_id' => 'nullable|UUID',

            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'responsible_employee_id' => 'nullable|UUID',
            'is_common' => 'nullable|boolean',

            'contacts.phone_id' => 'nullable|uuid',
            'contacts.address_id' => 'nullable|uuid',

            'order_scheme' => 'nullable|array',

            'order_scheme.on_coming_from' => 'nullable|date',
            'order_scheme.on_shipment_from' => 'nullable|date',
            'order_scheme.control_operational_balances' => 'nullable|boolean',

            'structure' => 'nullable|array',
            'structure.use_premises_from' => 'nullable|date',
            'structure.cells' => 'nullable|boolean',
            'structure.use_cells_from' => 'nullable|date',

            'is_default' => 'nullable|boolean',
        ];
    }

    public function toDTO(): WarehouseDTO
    {
        return WarehouseDTO::fromArray($this->validated());
    }
}
