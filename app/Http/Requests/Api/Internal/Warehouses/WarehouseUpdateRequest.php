<?php

namespace App\Http\Requests\Api\Internal\Warehouses;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Warehouses\WarehouseService\DTO\WarehouseDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class WarehouseUpdateRequest extends FormRequest implements ToDTOContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string',
            'work_schedule_id' => 'nullable|UUID',
            'control_free_residuals' => 'nullable|boolean',
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',

            'group_id' => 'nullable|UUID',

            'responsible_employee_id' => 'nullable|UUID',

            'is_common' => 'nullable|boolean',

            'contacts.phone_id' => 'nullable|uuid',
            'contacts.address_id' => 'nullable|uuid',

            'order_scheme.control_operational_balances' => 'nullable|boolean',

            'is_default' => 'nullable|boolean',
        ];
    }

    public function toDTO(): WarehouseDTO
    {
        return WarehouseDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
