<?php

namespace App\Http\Requests\Api\Internal\GoodsTransferItems;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\GoodsTransferItemService\DTO\GoodsTransferItemDto;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class GoodsTransferItemStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'goods_transfer_id' => 'required|UUID',
            'product_id' => 'required|UUID',
            'quantity' => 'required|integer|min:1',
            'price' => 'nullable|integer|min:0',
        ];
    }

    public function toDTO(): GoodsTransferItemDto
    {
        return GoodsTransferItemDto::fromArray($this->validated());
    }
}
