<?php

namespace App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports;

use App\Contracts\Requests\ToDtoContract;
use App\DTO\IndexRequestDTO;
use App\Entities\ReceivedComissionReportEntity;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ReceivedComissionReportsIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly ReceivedComissionReportEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],

            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],

            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
