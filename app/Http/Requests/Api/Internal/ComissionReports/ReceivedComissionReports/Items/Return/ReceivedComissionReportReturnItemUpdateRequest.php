<?php

namespace App\Http\Requests\Api\Internal\ComissionReports\ReceivedComissionReports\Items\Return;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Sales\ComissionReports\ReceivedReports\Items\DTO\ReturnItemDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class ReceivedComissionReportReturnItemUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'quantity' => 'required|integer|min:0',
            'price' => ['nullable', 'numeric'],
            'vat_rate_id' => 'required|UUID',
            'comission_return_value' => ['nullable', 'numeric'],
        ];
    }

    public function toDTO(): ReturnItemDTO
    {
        return ReturnItemDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
