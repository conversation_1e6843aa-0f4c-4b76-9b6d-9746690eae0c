<?php

namespace App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\ComissionTypeEnum;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\DTO\IssuedComissionReportDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IssuedComissionReportUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'employee_id' => 'required|UUID',
            'department_id' => 'required|UUID',
            'is_common' => 'nullable|boolean',
            'is_held' => 'nullable|boolean',
            'number' => 'nullable|string',
            'date_from' => 'nullable|date',
            'status_id' => 'nullable|UUID',
            'legal_entity_id' => 'required|UUID',
            'contractor_id' => 'required|UUID',
            'sales_channel_id' => 'nullable|UUID',
            'currency_id' => 'required|UUID',
            'period_from' => 'required|date|before:period_to',
            'period_to' => 'required|date|after:period_from',
            'contract_id' => 'required|UUID',
            'comission_type' => ['required', 'string', Rule::in(ComissionTypeEnum::cases())],
            'comission_value' => 'nullable|string',
            'comment' => 'nullable|string',

            'files' => 'nullable|array',
            'files.*' => 'uuid',
        ];
    }

    public function toDTO(): IssuedComissionReportDTO
    {
        return IssuedComissionReportDTO::fromArray(
            array_merge(
                $this->validated(),
                ['id' => $this->route('id')]
            )
        );
    }
}
