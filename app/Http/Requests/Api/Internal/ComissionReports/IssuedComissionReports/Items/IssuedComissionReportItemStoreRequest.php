<?php

namespace App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\Items;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Sales\ComissionReports\IssuedReports\Items\DTO\IssuedItemDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class IssuedComissionReportItemStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'report_id' => 'required|UUID',
            'product_id' => 'required|UUID',
            'quantity' => 'required|integer|min:0',
            'price' => ['nullable', 'numeric'],
            'vat_rate_id' => 'required|UUID',
            'comission_value' => ['nullable', 'numeric'],
        ];
    }

    public function toDTO(): IssuedItemDTO
    {
        return IssuedItemDTO::fromArray($this->validated());
    }
}
