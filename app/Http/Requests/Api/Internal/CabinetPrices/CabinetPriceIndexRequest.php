<?php

namespace App\Http\Requests\Api\Internal\CabinetPrices;

use App\DTO\IndexRequestDTO;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Entities\CabinetPriceEntity;
use App\Contracts\Requests\ToDtoContract;
use Illuminate\Foundation\Http\FormRequest;

class CabinetPriceIndexRequest extends FormRequest implements ToDtoContract
{
    public function __construct(
        private readonly CabinetPriceEntity $entity
    ) {
        parent::__construct();
    }

    public function authorize(): bool
    {
        return true;
    }


    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'fields' => 'nullable|array',
            'fields.*' => ['string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'filters.search.value' => 'string',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
