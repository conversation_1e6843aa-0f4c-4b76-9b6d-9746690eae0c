<?php

namespace App\Http\Requests\Api\Internal\DepartamentPermissions;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\PermissionScopeEnum;
use App\Services\Api\Internal\Workspace\Permissions\DepartmentPermissionsService\DTO\DepartmentPermissionDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DepartamentPermissionUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'permissions' => 'required|array',
            'permissions.*.id' => 'nullable|UUID',
            'permissions.*.permission_id' => 'required|UUID',
            'permissions.*.scope' => 'nullable|' . Rule::in(PermissionScopeEnum::cases()),
        ];
    }

    public function toDTO(): DepartmentPermissionDTO
    {
        return DepartmentPermissionDTO::fromArray(
            array_merge($this->validated(), ['department_id' => $this->route('id')])
        );
    }
}
