<?php

namespace App\Http\Requests\Api\Internal\Excel;

use Illuminate\Foundation\Http\FormRequest;

class ExcelDownloadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $data = $this->all();

        return [
            'export_id' => 'required|UUID|exists:export,id',
        ];
    }
}
