<?php

namespace App\Http\Requests\Api\Internal\Products;

use App\Enums\Api\Internal\AgeCategoryEnum;
use App\Enums\Api\Internal\LegalEntityTaxation;
use App\Enums\Api\Internal\PackTypeInProductAccountingEnum;
use App\Enums\Api\Internal\ProductThresholdsEnum;
use App\Enums\Api\Internal\TargetGenderEnum;
use App\Enums\Api\Internal\TypeAccountingEnum;
use App\Enums\Api\Internal\TypeProductEnum;
use App\Enums\Api\Internal\TypeProductIndicationSubjectCalculationEnum;
use App\Enums\Api\Internal\TypeProductionEnum;
use App\Services\Api\Internal\Goods\Products\ProductsService\DTO\ProductDto;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    // public function authorize(): bool
    // {
    //     return true;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        // dump($this->id);
        return [
            'contractor_id' => 'nullable|UUID', // id контрагента - поставщик
            'category_id' => 'nullable|UUID',    // id категории

            // 'cabinet_id' => 'required|UUID',
            'department_id' => 'required|UUID', // id отдела
            'employee_id' => 'required|UUID', // id отдела
            'title' => 'required|string|max:255',               // Название
            'short_title' => 'required|string|max:255',               // Название
            'type' => 'required|integer|'. Rule::in(TypeProductEnum::cases()),     // Тип Товар, Услуга, Комплект
            'description' => 'nullable|string',                 // описание
            'short_description' => 'nullable|string|max:100',   // Короткое описание
            'discounts_retail_sales' => 'nullable|boolean',     // Запретить скидки при продаже в розницу (boolean)
            'country_id' => 'nullable|UUID',                     // страна
            'article' => ['nullable', 'string'],// артикул товара                    // артикул
            'code' => ['required', 'string'],                   // код товара для кабинета
            // 'inner_code' => 'nullable|integer',              // внутренний код товара для генерации кода

            'external_code' => 'nullable|string',               // внешний код
            'measurement_unit_id' => 'nullable|UUID',           // Единица измерения
            'brand_id' => 'nullable|UUID',                      // Бренд id

            'min_price'        => 'sometimes|nullable|array',
            'min_price.value' => 'nullable|numeric',
            'min_price.currency_id' => 'required_with:min_price.value|UUID',
            'purchase_price'        => 'sometimes|nullable|array',
            'purchase_price.value' => 'nullable|numeric',
            'purchase_price.currency_id' => 'required_with:purchase_price.value|UUID',

            'dimensions'        => 'sometimes|nullable|array',
            'dimensions.length' => 'nullable|numeric',                     // Длина
            'dimensions.width' => 'nullable|numeric',                      // Ширина
            'dimensions.height' => 'nullable|numeric',                     // Высота
            'dimensions.weight' => 'nullable|numeric',                     // вес
            'dimensions.volume' => 'nullable|numeric',                     // Объем

            'tax_id' => 'nullable|UUID',                         // налог НДС - селект
            'tax_system' => 'nullable|string|'. Rule::in(LegalEntityTaxation::cases()),                     // Кассовый чек. Система налогообложения - селект
            'indication_subject_calculation' => 'nullable|string|'. Rule::in(TypeProductIndicationSubjectCalculationEnum::cases()),   // Признак предмета расчета - селект

            'images.*' => 'nullable|mimes:jpeg,jpg,png|max:5240',
            'files.*' => 'nullable|file|max:10240', // максимум 10MB

            'pack_type' => 'nullable|string|'.  Rule::in(PackTypeInProductAccountingEnum::cases()),             // Тип фасовки
            'type_accounting' => 'nullable|string|'.  Rule::in(TypeAccountingEnum::cases()),             // Тип учета
            'accounting_series' => 'nullable|boolean',          // Учет по сериям           boolean
            'product_siz_name_id' => 'nullable|UUID',         // Наименование для Средства инд защиты - из БД
            'product_siz_type_id' => 'nullable|UUID',         // Тип для средств инд защиты - из БД
            'product_type_code' => 'nullable|string',           // Код вида продукции       string
            'container_capacity' => 'nullable|numeric',         // Емкость тары, л.         integer
            'strength' => 'nullable|numeric',                   // Крепость, максимум 100
            'excise' => 'nullable|boolean',                     // Содержит акцизную марку  boolean
            'product_type_id' => 'nullable|integer',             // Тип продукции   из БД
           // TODO тут будет вопрос по полиси
            'tnwed_id' => 'nullable|UUID', // ТН ВЭД из полной таблицы string
            'target_gender' => 'nullable|string|'. Rule::in(TargetGenderEnum::cases()),               // Целевой пол              enum
            'type_production' => 'nullable|string|'.  Rule::in(TypeProductionEnum::cases()),             // Тип производства         enum
            'age_category' => 'nullable|string|'.  Rule::in(AgeCategoryEnum::cases()),                // Возрастная категория     enum
            'set' => 'nullable|boolean',                        // Комплект                 boolean
            'partial_sale' => 'nullable|boolean',               // Частичная продажа        boolean
            'model' => 'nullable|string',                       // Модель                   string
            'traceable' => 'nullable|boolean',                  // Прослеживаемый           boolean

            // TODO временно отключены
            // 'egais_code.*.id' => 'nullable|UUID|exists:product_egais_codes,id',  // Коды ЕГАИС id
            // 'egais_code.*.code' => 'nullable|string',                // Коды ЕГАИС

            'attributes'                                => 'sometimes|nullable|array',
            'attributes.*.attribute_values_id'          => 'required|UUID',
            'attributes.*.attribute_id'                 => 'required|UUID',
            'attributes.*.sort_order'                   => 'nullable|integer',

            'sale_price'                                => 'sometimes|nullable|array',
            'sale_price.*.cabinet_price_id'             => 'required|UUID',
            'sale_price.*.amount'                       => 'nullable|integer',
            'sale_price.*.currency_id'                  => 'required|UUID',
            'sale_price.*.sort'                         => 'nullable|integer',

            'product_group_id' => 'nullable|UUID',

            'thresholds'                                => 'sometimes|nullable|array',
            'thresholds.type'                           => 'required_with:thresholds|integer|'. Rule::in(ProductThresholdsEnum::cases()),
            'thresholds.warehouses'                     => 'nullable|array',
            'thresholds.warehouses.*.warehouse_id'      => 'required_with:thresholds.warehouses|UUID',
            'thresholds.warehouses.*.threshold_count'   => 'required_with:thresholds.warehouses.*.warehouse_id|integer',
            'thresholds.threshold_count'                => 'nullable|integer',

        ];
    }

    public function withValidator($validator)
    {
        $validator->sometimes('thresholds.warehouses', 'required|array', function ($input) {
            return isset($input->thresholds['type']) && $input->thresholds['type'] == ProductThresholdsEnum::SET_FOR_EACH_WAREHOUSE->value;
        });
    }

    public function toDTO(): ProductDto
    {
        return ProductDto::fromArray($this->validated());
    }
}
