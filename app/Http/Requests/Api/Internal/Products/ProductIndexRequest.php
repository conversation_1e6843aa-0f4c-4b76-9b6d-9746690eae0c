<?php

namespace App\Http\Requests\Api\Internal\Products;

use App\DTO\IndexRequestDTO;
use App\Entities\ProductEntity;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Enums\Api\Internal\ShowOnlyEnum;
use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Api\Internal\FilterConditionEnum;

class ProductIndexRequest extends FormRequest
{
    public function __construct(
        private readonly ProductEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'filters' => 'nullable|array',

            'filters.type.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.type.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.type.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.type.value');
                    $condition = $this->input('filters.type.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.title.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.title.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.title.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.title.value');
                    $condition = $this->input('filters.title.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.description.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.description.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.description.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.description.value');
                    $condition = $this->input('filters.description.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],


            'filters.article.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.article.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.article.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.article.value');
                    $condition = $this->input('filters.article.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.code.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.code.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.code.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.code.value');
                    $condition = $this->input('filters.code.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.external_code.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.external_code.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.external_code.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.external_code.value');
                    $condition = $this->input('filters.external_code.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.barcode.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.barcode.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.barcode.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.barcode.value');
                    $condition = $this->input('filters.barcode.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.packing.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.packing.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.packing.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.packing.value');
                    $condition = $this->input('filters.packing.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.show_only.value' => 'string|' . Rule::in(ShowOnlyEnum::cases()),

            'filters.search.value' => 'string',
            'filters.shared_access.value' => 'boolean',

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employee_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.employee_owners.value');
                    $condition = $this->input('filters.employee_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],
            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.department_owners.value');
                    $condition = $this->input('filters.department_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'

        ];

    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
