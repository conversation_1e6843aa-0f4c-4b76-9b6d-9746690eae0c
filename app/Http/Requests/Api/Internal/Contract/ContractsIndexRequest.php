<?php

namespace App\Http\Requests\Api\Internal\Contract;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Entities\ContractEntity;
use App\Enums\Api\Internal\ShowOnlyEnum;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Enums\Api\Internal\FilterConditionEnum;

class ContractsIndexRequest extends FormRequest
{
    public function __construct(
        private readonly ContractEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',

            'filters' => 'array',

            'filters.show_only.value' => 'string|' . Rule::in(ShowOnlyEnum::cases()),

            'filters.contractors.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractors.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractors.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.contractors.value');
                $condition = $this->input('filters.contractors.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.contractor_groups.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.contractor_groups.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.contractor_groups.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.contractor_groups.value');
                $condition = $this->input('filters.contractor_groups.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.legals.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.legals.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.legals.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.legals.value');
                $condition = $this->input('filters.legals.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.statuses.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.statuses.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.statuses.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.statuses.value');
                $condition = $this->input('filters.statuses.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.sended.value' => 'boolean',
            'filters.printed.value' => 'boolean',
            'filters.shared_access.value' => 'boolean',

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.employee_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.employee_owners.value');
                $condition = $this->input('filters.employee_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],

            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
            'string',
            Rule::in(FilterConditionEnum::cases()),
            Rule::excludeIf(function () {
                $value = $this->input('filters.department_owners.value');
                $condition = $this->input('filters.department_owners.condition');

                return empty($value) && !in_array($condition, [
                        FilterConditionEnum::NOT_EMPTY->value,
                        FilterConditionEnum::EMPTY->value
                    ], true);
            }),
            ],


            'filters.search.value' => 'string',

            'filters.period.from' => 'required_with:filters.created_at.to|date_format:d.m.Y H:i',
            'filters.period.to' => 'required_with:filters.created_at.from|date_format:d.m.Y H:i|after_or_equal:filters.created_at.from',
            'filters.updated_at.from' => 'required_with:filters.updated_at.to|date_format:d.m.Y H:i',
            'filters.updated_at.to' => 'required_with:filters.updated_at.from|date_format:d.m.Y H:i|after_or_equal:filters.updated_at.from',

            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100'
        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
