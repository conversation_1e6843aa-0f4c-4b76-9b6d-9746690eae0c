<?php

namespace App\Http\Requests\Api\Internal\ProductAttributes;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Goods\Products\ProductAttributesService\DTO\ProductAttributeDto;
use Illuminate\Foundation\Http\FormRequest;

class ProductAttributeStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'product_id' => 'required|UUID',
            'attribute_id' => 'required|UUID',
            'attribute_values_id' => 'required|UUID',
            'sort_order' => 'required|integer',
        ];
    }

    public function toDTO(): ProductAttributeDto
    {
        return ProductAttributeDto::fromArray($this->validated());
    }
}
