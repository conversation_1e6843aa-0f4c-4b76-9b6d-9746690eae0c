<?php

namespace App\Http\Requests\Api\Internal\Ozon;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Ozon\OzonService\OzonV3PostingFbsListService\DTO\OzonV3PostingFbsListDTO;
use Illuminate\Foundation\Http\FormRequest;

class OzonV3PostingFbsListRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|uuid',                                        // id компании
            'department_id' => 'required|uuid',
            'employee_id' => 'required|uuid',
            'ozon_credential_id' => 'required|uuid',                                // id получить API-ключи Ozon
            'filter.is_quantum' => 'nullable|boolean',                              // Фильтр временный для тестов
            'filter.last_changed_status_date.from' => 'required|date_format:Y-m-d H:i:s',
            'filter.last_changed_status_date.to' => 'required|date_format:Y-m-d H:i:s|after_or_equal:filter.last_changed_status_date.from',
            'filter.since' => 'required|date_format:Y-m-d H:i:s',
            'filter.to' => 'required|date_format:Y-m-d H:i:s|after_or_equal:filter.since',
        ];
    }

    public function toDTO(): OzonV3PostingFbsListDTO
    {
        return OzonV3PostingFbsListDTO::fromArray($this->validated());
    }
}
