<?php

namespace App\Http\Requests\Api\Internal\Ozon;

use App\DTO\IndexRequestDTO;
use Illuminate\Validation\Rule;
use App\Rules\AllowedFieldsRule;
use App\Rules\ValidSortFieldRule;
use App\Enums\Api\Internal\ShowOnlyEnum;
use Illuminate\Foundation\Http\FormRequest;
use App\Entities\OzonV3PostingFbsListEntity;
use App\Enums\Api\Internal\FilterConditionEnum;

class OzonV3PostingFbsListIndexRequest extends FormRequest 
{
    public function __construct(
        private readonly OzonV3PostingFbsListEntity $entity
    ) {
        parent::__construct();
    }
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',   
            'fields' => 'nullable|array',
            'fields.*' => ['nullable', 'string', new AllowedFieldsRule($this->entity)],
            'sortField' => ['nullable', 'string', new ValidSortFieldRule($this->entity, $this)],
            'sortDirection' => 'nullable|string|in:asc,desc',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            
            'filters' => 'nullable|array',

            'filters.ozon_company_id.value' => 'nullable|integer',

            'filters.order_number.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.order_number.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.order_number.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.order_number.value');
                    $condition = $this->input('filters.order_number.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

             'filters.name.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.name.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.name.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.name.value');
                    $condition = $this->input('filters.name.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.type.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.type.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.type.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.type.value');
                    $condition = $this->input('filters.type.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.sku.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.sku.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.sku.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.sku.value');
                    $condition = $this->input('filters.sku.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],


            'filters.offer_id.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.offer_id.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.offer_id.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.offer_id.value');
                    $condition = $this->input('filters.offer_id.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],


            'filters.posting_number.value' => 'string|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.posting_number.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.posting_number.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.posting_number.value');
                    $condition = $this->input('filters.posting_number.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],


              
            'filters.show_only.value' => 'string|' . Rule::in(ShowOnlyEnum::cases()),

            'filters.search.value' => 'string',
            'filters.shared_access.value' => 'boolean',

            'filters.employee_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.employee_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            
            'filters.employee_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.employee_owners.value');
                    $condition = $this->input('filters.employee_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],

            'filters.department_owners.value.*' => 'uuid|' . Rule::excludeIf(function () {
                $condition = $this->input('filters.department_owners.condition');
                return in_array($condition, [
                    FilterConditionEnum::NOT_EMPTY->value,
                    FilterConditionEnum::EMPTY->value
                ], true);
            }),
            'filters.department_owners.condition' => [
                'string',
                Rule::in(FilterConditionEnum::cases()),
                Rule::excludeIf(function () {
                    $value = $this->input('filters.department_owners.value');
                    $condition = $this->input('filters.department_owners.condition');

                    return empty($value) && !in_array($condition, [
                            FilterConditionEnum::NOT_EMPTY->value,
                            FilterConditionEnum::EMPTY->value
                        ], true);
                }),
            ],


        ];
    }

    public function toDTO(): IndexRequestDTO
    {
        $data = $this->validated();
        return IndexRequestDTO::fromArray(
            array_merge(
                ['id' => $data['cabinet_id']],
                $data
            )
        );
    }
}
