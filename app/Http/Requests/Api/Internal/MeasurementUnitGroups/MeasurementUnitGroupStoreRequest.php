<?php

namespace App\Http\Requests\Api\Internal\MeasurementUnitGroups;

use App\Contracts\Requests\ToDtoContract;
use App\Enums\Api\Internal\MeasurementUnitTypeEnum;
use App\Services\Api\Internal\References\MeasurementUnits\MeasurementGroupsService\DTO\MeasurementUnitGroupDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MeasurementUnitGroupStoreRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'cabinet_id' => 'required|UUID',
            'name' => 'required|string',
            'tech_type' => 'required|' . Rule::in(MeasurementUnitTypeEnum::cases())
        ];
    }

    public function toDTO(): MeasurementUnitGroupDTO
    {
        return MeasurementUnitGroupDTO::fromArray($this->validated());
    }
}
