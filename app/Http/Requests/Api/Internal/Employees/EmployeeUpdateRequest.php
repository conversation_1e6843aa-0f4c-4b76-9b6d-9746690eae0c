<?php

namespace App\Http\Requests\Api\Internal\Employees;

use App\Contracts\Requests\ToDtoContract;
use App\Services\Api\Internal\Workspace\EmployeesService\DTO\EmployeeDTO;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class EmployeeUpdateRequest extends FormRequest implements ToDtoContract
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'lastname' => 'required|string',
            'firstname' => 'required|string',
            'patronymic' => 'nullable|string',
            'telephone' => 'nullable|string',
            'email' => 'required|email',
            'status' => 'required|' . Rule::in(['active', 'inactive']),
            'role_id' => 'nullable|UUID',
            'department_id' => 'nullable|UUID',
            'job_number' => 'nullable|string',
            'citizenship' => 'nullable|string',
            'gender' => 'nullable|in:m,f',
            'inn' => 'nullable|string|max_digits:12',
            'id_card' => 'nullable|string',
            'passport_series' => 'nullable|string',
            'passport_number' => 'nullable|string',
            'passport_issue_date' => 'nullable|date',
            'who_issued_passport' => 'nullable|string',
            'division_code' => 'nullable|string',
            'registration_address' => 'nullable|string',
            'temporary_registration_address' => 'nullable|string',
            'driver_license_series' => 'nullable|string',
            'driver_license_number' => 'nullable|string',
            'driver_license_issue_date' => 'nullable|date',
            'driver_license_expiration_date' => 'nullable|date',
            'driver_license_category' => 'nullable|string',
            'military_card' => 'nullable|string',
            'hire_date' => 'nullable|date',
            'dismissal_date' => 'nullable|date',
            'position' => 'nullable|string',
            'salary' => 'nullable|numeric',
            'labor_fund' => 'nullable|string',
            'planned_advance' => 'nullable|string',
            'work_schedule' => 'nullable|array',
            'work_schedule.monday_from' => 'nullable|date_format:H:i',
            'work_schedule.monday_to' => 'nullable|date_format:H:i',
            'work_schedule.tuesday_from' => 'nullable|date_format:H:i',
            'work_schedule.tuesday_to' => 'nullable|date_format:H:i',
            'work_schedule.wednesday_from' => 'nullable|date_format:H:i',
            'work_schedule.wednesday_to' => 'nullable|date_format:H:i',
            'work_schedule.thursday_from' => 'nullable|date_format:H:i',
            'work_schedule.thursday_to' => 'nullable|date_format:H:i',
            'work_schedule.friday_from' => 'nullable|date_format:H:i',
            'work_schedule.friday_to' => 'nullable|date_format:H:i',
            'work_schedule.saturday_from' => 'nullable|date_format:H:i',
            'work_schedule.saturday_to' => 'nullable|date_format:H:i',
            'work_schedule.sunday_from' => 'nullable|date_format:H:i',
            'work_schedule.sunday_to' => 'nullable|date_format:H:i',
        ];
    }

    public function toDTO(): EmployeeDTO
    {
        return EmployeeDTO::fromArray(
            array_merge($this->validated(), ['id' => $this->route('id')])
        );
    }
}
