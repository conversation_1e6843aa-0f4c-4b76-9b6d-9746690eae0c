<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Employee;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\Permissions\EmployeePermissionsPolicyContract;
use App\Contracts\Services\Internal\EmployeePermissionsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\EmployeePermissions\EmployeePermissionUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class EmployeePermissionController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly EmployeePermissionsServiceContract $service,
        private readonly EmployeePermissionsPolicyContract $policy
    ) {
    }
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(EmployeePermissionUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
