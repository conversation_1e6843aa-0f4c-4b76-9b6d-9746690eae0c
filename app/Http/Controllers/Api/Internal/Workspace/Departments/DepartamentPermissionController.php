<?php

namespace App\Http\Controllers\Api\Internal\Workspace\Departments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\Permissions\DepartmentPermissionPolicyContract;
use App\Contracts\Services\Internal\DepartamentPermissionsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\DepartamentPermissions\DepartamentPermissionStoreRequest;
use App\Http\Requests\Api\Internal\DepartamentPermissions\DepartamentPermissionUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DepartamentPermissionController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly DepartamentPermissionsServiceContract $service,
        private readonly DepartmentPermissionPolicyContract $policy
    ) {
    }

    public function store(DepartamentPermissionStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(DepartamentPermissionUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
