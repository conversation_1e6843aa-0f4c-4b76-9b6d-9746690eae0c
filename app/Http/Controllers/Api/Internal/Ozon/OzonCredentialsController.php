<?php

namespace App\Http\Controllers\Api\Internal\Ozon;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\OzonCredentialsPolicyContract;
use App\Contracts\Services\Internal\OzonCredentialsServiceContract;
use App\Http\Requests\Api\Internal\Ozon\Credentials\OzonCredentialsIndexRequest;
use App\Http\Requests\Api\Internal\Ozon\Credentials\OzonCredentialsStoreRequest;
use App\Http\Requests\Api\Internal\Ozon\Credentials\OzonCredentialsUpdateRequest;
use App\Http\Resources\Ozon\Credentials\OzonCredentialsIndexCollection;

class OzonCredentialsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly OzonCredentialsServiceContract $service,
        private readonly OzonCredentialsPolicyContract $policy
    ) {
    }

    /**
    * Display a listing of the resource.
    */
    /**
     * @response OzonCredentialsIndexCollection<OzonCredentialsIndexResource>
     */
    public function index(OzonCredentialsIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->validated());
            $collection = new OzonCredentialsIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(OzonCredentialsStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response OzonCredentialsShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(OzonCredentialsShowResource::make($data));
        });
    }


    public function update(OzonCredentialsUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    /**
    * Remove the specified resource from storage.
    */
    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }

}
