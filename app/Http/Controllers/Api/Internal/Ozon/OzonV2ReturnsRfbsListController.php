<?php

namespace App\Http\Controllers\Api\Internal\Ozon;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\OzonV2ReturnsRfbsListPolicyContract;
use App\Http\Requests\Api\Internal\Ozon\Returns\OzonV2ReturnsRfbsListRequest;
use App\Contracts\Services\Internal\OzonV2ReturnsRfbsListServiceContract;
use App\Http\Requests\Api\Internal\Ozon\Returns\OzonV2ReturnsRfbsListIndexRequest;

class OzonV2ReturnsRfbsListController extends Controller
{
  use ApiPolicy;
 
  public function __construct(
      private readonly OzonV2ReturnsRfbsListServiceContract $service,
      private readonly OzonV2ReturnsRfbsListPolicyContract $policy
  ) {
  }
  
    /**
   * Display a listing of the resource.
   */
  public function index(OzonV2ReturnsRfbsListIndexRequest $request): JsonResponse
  {
      return $this->executeAction(function () use ($request) {
          $data = $this->service->index($request->validated());
          return $this->successResponse($data);
      });
  }

  public function store(OzonV2ReturnsRfbsListRequest $request): ?JsonResponse
  {
      return $this->executeAction(function () use ($request) {
          $data = $request->toDto();

          $this->authorizeCreate($request, $data);

          $this->service->create($data);
          return $this->successResponse();
      });
  }

  /**
   * Display the specified resource.
   */
  public function show(Request $request, string $id): JsonResponse
  {
      return $this->executeAction(function () use ($request, $id) {
          $this->authorizeView($request, $id);

          $data = $this->service->show($id);
          return $this->successResponse($data);
      });
  }

  protected function getPolicy(): BaseResourcePolicyContract
  {
      return $this->policy;
  }
  
}