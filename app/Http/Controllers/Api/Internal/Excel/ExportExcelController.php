<?php

namespace App\Http\Controllers\Api\Internal\Excel;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Excel\ExcelDownloadRequest;
use App\Http\Resources\Excel\ExportExcelIndexCollection;
use App\Services\Api\Internal\Other\ExcelService\ExportExcelContractorsService;
use App\Services\Api\Internal\Other\ExcelService\ExportExcelProductsService;
use App\Services\Api\Internal\Other\ExcelService\ExportExcelService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ExportExcelController extends Controller
{
    /**
     * @var ExportExcelService
     * @var ExportExcelProductsService
     * @var ExportExcelContractorsService
     */
    protected ExportExcelService $exportExcelService;
    protected ExportExcelProductsService $exportExcelProductsService;
    protected ExportExcelContractorsService $exportExcelContractorsService;

    public function __construct(ExportExcelService $exportExcelService, ExportExcelProductsService $exportExcelProductsService, ExportExcelContractorsService $exportExcelContractorsService)
    {
        // $this->middleware('check-cabinet-access');
        $this->exportExcelService = $exportExcelService;
        $this->exportExcelProductsService = $exportExcelProductsService;
        $this->exportExcelContractorsService = $exportExcelContractorsService;
    }

    /**
     * @response ExportExcelIndexCollection<ExportExcelIndexResource>
     */
    public function index(string $cabinetId): JsonResponse
    {

        $export = DB::table('export')->where('cabinet_id', $cabinetId)->get();
        $collection = new ExportExcelIndexCollection($export);

        return response()->json($collection, 201);
    }

    public function download(ExcelDownloadRequest $request, string $cabinetId): JsonResponse
    {

        $file = DB::table('export')->where('cabinet_id', $cabinetId)->find($request->export_id);

        $fileCont = Storage::disk('s3-docs')->temporaryUrl($file->path, now()->addMinutes(5));
        // $fileCont = Storage::disk('s3-docs')->download($file->url, $file->file, ['ResponseContentType' => 'application/octet-stream']);

        return response()->json($fileCont, 200);
    }


    public function generateExcelProducts(string $cabinetId): JsonResponse
    {

        $dataArray = $this->exportExcelProductsService->exportExcelProducts($cabinetId);

        $this->exportExcelService->generateExcelFile($cabinetId, $dataArray, 'products');

        return response()->json([], 204);

    }

    public function generatePdfProducts(string $cabinetId): JsonResponse
    {

        $dataArray = $this->exportExcelProductsService->exportExcelProducts($cabinetId);

        $this->exportExcelService->generatePdfFile($cabinetId, $dataArray, 'products');

        return response()->json([], 204);
    }

    public function generateExcelContractors(string $cabinetId): JsonResponse
    {

        $dataArray = $this->exportExcelContractorsService->exportExcelContractors($cabinetId);

        $this->exportExcelService->generateExcelFile($cabinetId, $dataArray, 'contractors');

        return response()->json([], 204);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ExcelDownloadRequest $request, string $cabinetId): JsonResponse
    {
        $this->exportExcelService->destroyFile($cabinetId, $request->export_id);

        return response()->json([], 204);
    }
}
