<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers;

use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\WarehouseCalendarService;
use App\Http\Resources\Warehouses\WarehouseCalendarShowResource;
use App\Contracts\Policies\Directories\Warehouses\WarehouseCalendarPolicyContract;

class WarehouseCalendarController extends Controller
{
    use ApiResponse;

    public function __construct(
        private readonly WarehouseCalendarService $service,
        private readonly WarehouseCalendarPolicyContract $policy
    ) {
    }

    /**
     * @response WarehouseCalendarShowResource[]
     */
    public function show(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($id, $request) {
            $this->policy->view($request->user(), $id);
            $data = $this->service->show($id);
            return $this->successResponse(WarehouseCalendarShowResource::collection($data));
        });
    }
}
