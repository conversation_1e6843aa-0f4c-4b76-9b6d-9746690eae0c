<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers;

use App\Traits\ApiPolicy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseStorageAreasServiceContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseStorageAreaPolicyContract;
use App\Http\Requests\Api\Internal\Warehouses\StorageArea\WarehouseStorageAreaIndexRequest;
use App\Http\Requests\Api\Internal\Warehouses\StorageArea\WarehouseStorageAreaStoreRequest;
use App\Http\Requests\Api\Internal\Warehouses\StorageArea\WarehouseStorageAreaUpdateRequest;

class WarehouseStorageAreaController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseStorageAreasServiceContract $service,
        private readonly WarehouseStorageAreaPolicyContract $policy
    ) {
    }

    public function index(WarehouseStorageAreaIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
    }

    public function store(WarehouseStorageAreaStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(WarehouseStorageAreaUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
