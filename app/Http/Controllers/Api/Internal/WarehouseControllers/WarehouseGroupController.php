<?php

namespace App\Http\Controllers\Api\Internal\WarehouseControllers;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\Warehouses\WarehouseGroupsPolicyContract;
use App\Contracts\Services\Internal\Warehouses\WarehouseGroupsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Warehouses\Groups\WarehouseGroupIndexRequest;
use App\Http\Requests\Api\Internal\Warehouses\Groups\WarehouseGroupStoreRequest;
use App\Http\Requests\Api\Internal\Warehouses\Groups\WarehouseGroupUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class WarehouseGroupController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly WarehouseGroupsServiceContract $service,
        private readonly WarehouseGroupsPolicyContract $policy
    ) {
    }

    public function index(WarehouseGroupIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);
            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
    }

    public function store(WarehouseGroupStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(WarehouseGroupUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
