<?php

namespace App\Http\Controllers\Api\Internal\User;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Random\RandomException;

class EmailVerificationNotificationController extends Controller
{
    /**
     * Send a new email verification notification.
     * @throws RandomException
     */
    public function store(Request $request): JsonResponse
    {
        if ($request->user()->hasVerifiedEmail()) {
            return response()->json([
                "message" => 'Your mail already verified.',
                'errors' => [
                    'code' => ['Your mail already verified.']
                ]
            ], 422);
        }

        if (app()->isLocal()) {
            $code = str_pad((string)random_int(1, 9999), 4, '0', STR_PAD_LEFT);
            $expiresAt = now()->addMinutes(config('auth.verification.expire', 60));
            DB::table('email_verification_codes')->updateOrInsert(
                ['user_id' => $request->user()->id],
                [
                    'code' => $code,
                    'expires_at' => $expiresAt,
                    'updated_at' => now(),
                ]
            );

            return response()->json([
                'code' => $code,
                'expires_at' => $expiresAt
            ], 200);
        }

        $request->user()->sendEmailVerificationNotification();

        return response()->json(['Send new code'], 200);
    }
}
