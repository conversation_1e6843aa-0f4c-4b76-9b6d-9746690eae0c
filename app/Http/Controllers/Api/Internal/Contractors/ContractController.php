<?php

namespace App\Http\Controllers\Api\Internal\Contractors;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Contractors\ContractPolicyContract;
use App\Contracts\Repositories\ContractRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\Contractors\ContractServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Contract\ContractBulkRequest;
use App\Http\Requests\Api\Internal\Contract\ContractsIndexRequest;
use App\Http\Requests\Api\Internal\Contract\ContractStoreRequest;
use App\Http\Requests\Api\Internal\Contract\ContractUpdateRequest;
use App\Http\Requests\Api\Internal\Contractors\ContractorBulkRequest;
use App\Http\Resources\Contractors\Contracts\ContractIndexCollection;
use App\Http\Resources\Contractors\Contracts\ContractShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContractController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ContractServiceContract $service,
        private readonly ContractPolicyContract $policy
    ) {
    }

    /**
     * @response ContractIndexCollection<ContractIndexResource>
     */
    public function index(ContractsIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new ContractIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(ContractStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ContractShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ContractShowResource::make($data));
        });
    }

    public function update(ContractUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkCopy(ContractBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            $this->service->bulkCopy($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkDelete(ContractBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function archive(
        ContractBulkRequest $request,
        ArchiveServiceContract $archiveService,
        ContractRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $repository, $archiveService) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->archive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }
    public function unarchive(
        ContractorBulkRequest $request,
        ArchiveServiceContract $archiveService,
        ContractRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $archiveService, $repository) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->unarchive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
