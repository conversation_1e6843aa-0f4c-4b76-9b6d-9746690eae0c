<?php

namespace App\Http\Controllers\Api\Internal\Contractors\Contractors;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Contractors\ContractorGroupsPolicyContract;
use App\Contracts\Services\Internal\Contractors\ContractorGroupsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ContractorGroups\ContractorGroupsBulkRequest;
use App\Http\Requests\Api\Internal\ContractorGroups\ContractorGroupsCreateRequest;
use App\Http\Requests\Api\Internal\ContractorGroups\ContractorGroupsIndexRequest;
use App\Http\Requests\Api\Internal\ContractorGroups\ContractorGroupsUpdateRequest;
use App\Http\Resources\Contractors\Groups\ContractorGroupIndexCollection;
use App\Http\Resources\Contractors\Groups\ContractorGroupShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ContractorGroupsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ContractorGroupsServiceContract $service,
        private readonly ContractorGroupsPolicyContract $policy
    ) {
    }

    /**
     * @response ContractorGroupIndexCollection<ContractorGroupIndexResource>
     */
    public function index(ContractorGroupsIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new ContractorGroupIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(ContractorGroupsCreateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ContractorGroupShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ContractorGroupShowResource::make($data));
        });
    }

    public function update(ContractorGroupsUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(ContractorGroupsBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
