<?php

namespace App\Http\Controllers\Api\Internal\Sales\ComissionerReports\IssuedComissionReports;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Sales\IssuedComissionReportsItemPolicyContract;
use App\Contracts\Services\Internal\Sales\IssuedComissionReportItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\Items\IssuedComissionReportItemsIndexRequest;
use App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\Items\IssuedComissionReportItemStoreRequest;
use App\Http\Requests\Api\Internal\ComissionReports\IssuedComissionReports\Items\IssuedComissionReportItemUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class IssuedComissionReportsItemsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly IssuedComissionReportItemsServiceContract $service,
        private readonly IssuedComissionReportsItemPolicyContract $policy
    ) {
    }

    public function index(IssuedComissionReportItemsIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            return $this->successResponse($data);
        });
    }

    public function store(IssuedComissionReportItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse($data);
        });
    }

    public function update(IssuedComissionReportItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
