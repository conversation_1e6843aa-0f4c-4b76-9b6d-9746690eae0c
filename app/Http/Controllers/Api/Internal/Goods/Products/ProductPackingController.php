<?php

namespace App\Http\Controllers\Api\Internal\Goods\Products;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\ProductPackingPolicyContract;
use App\Contracts\Services\Internal\Products\ProductPackingServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ProductPackings\ProductPackingUpdateRequest;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductPackingController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ProductPackingServiceContract $service,
        private readonly ProductPackingPolicyContract $policy
    ) {
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }

    public function index(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($id) {
            $this->policy->index($id);

            $products = $this->service->index(['id' => $id]);
            return $this->successResponse($products);
        });
    }

    public function update(ProductPackingUpdateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();
            $this->authorizeUpdate($request, $data);
            $this->service->update($data);
            return $this->noContentResponse();
        });

    }
}
