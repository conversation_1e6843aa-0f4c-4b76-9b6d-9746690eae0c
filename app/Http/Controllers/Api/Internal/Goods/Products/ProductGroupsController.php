<?php

namespace App\Http\Controllers\Api\Internal\Goods\Products;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\ProductGroupsPolicyContract;
use App\Contracts\Services\Internal\Products\ProductGroupsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\ProductGroups\ProductGroupsBulkRequest;
use App\Http\Requests\Api\Internal\ProductGroups\ProductGroupsCreateRequest;
use App\Http\Requests\Api\Internal\ProductGroups\ProductGroupsIndexRequest;
use App\Http\Requests\Api\Internal\ProductGroups\ProductGroupsUpdateRequest;
use App\Http\Resources\Goods\Products\Groups\ProductGroupIndexCollection;
use App\Http\Resources\Goods\Products\Groups\ProductGroupShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ProductGroupsController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly ProductGroupsServiceContract $service,
        private readonly ProductGroupsPolicyContract $policy
    ) {
    }

    /**
     * @response ProductGroupIndexCollection<ProductGroupIndexResource>
     */
    public function index(ProductGroupsIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new ProductGroupIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(ProductGroupsCreateRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response ProductGroupShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(ProductGroupShowResource::make($data));
        });
    }

    public function update(ProductGroupsUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(ProductGroupsBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
