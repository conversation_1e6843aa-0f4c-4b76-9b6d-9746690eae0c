<?php

namespace App\Http\Controllers\Api\Internal\Goods\Attributes;

use App\Contracts\Policies\AttributePolicyContract;
use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Services\Internal\AttributesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Attributes\AttributeIndexRequest;
use App\Http\Requests\Api\Internal\Attributes\AttributeStoreRequest;
use App\Http\Requests\Api\Internal\Attributes\AttributeUpdateRequest;
use App\Http\Resources\Goods\Attributes\AttributeIndexCollection;
use App\Http\Resources\Goods\Attributes\AttributeShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AttributeController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly AttributesServiceContract $service,
        private readonly AttributePolicyContract $policy
    ) {
    }

    /**
     * @response AttributeIndexCollection<AttributeIndexResource>
     */
    public function index(AttributeIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $result = $this->service->index($data);
            $collection = new AttributeIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    public function store(AttributeStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response AttributeShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(AttributeShowResource::make($data));
        });
    }

    public function update(AttributeUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
