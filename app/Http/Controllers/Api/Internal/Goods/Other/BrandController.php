<?php

namespace App\Http\Controllers\Api\Internal\Goods\Other;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Products\BrandPolicyContract;
use App\Contracts\Services\Internal\BrandsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Brands\BrandIndexRequest;
use App\Http\Requests\Api\Internal\Brands\BrandStoreRequest;
use App\Http\Requests\Api\Internal\Brands\BrandUpdateRequest;
use App\Http\Resources\Goods\Brands\BrandIndexCollection;
use App\Http\Resources\Goods\Brands\BrandShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class BrandController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly BrandsServiceContract $service,
        private readonly BrandPolicyContract $policy
    ) {
    }

    /**
     * Display a listing of the resource.
     */
    /**
     * @response BrandIndexCollection<BrandIndexResource>
     */
    public function index(BrandIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();
            $this->policy->index($data->id);

            $data = $this->service->index($data);
            $collection = new BrandIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BrandStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * Display the specified resource.
     */
    /**
     * @response BrandShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(BrandShowResource::make($data));
        });
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BrandUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
