<?php

namespace App\Http\Controllers\Api\Internal\Money\IncomingPayments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Finances\IncomingPaymentPolicyContract;
use App\Contracts\Services\Internal\Finances\IncomingPaymentsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\IncomingPayments\IncomingPaymentIndexRequest;
use App\Http\Requests\Api\Internal\IncomingPayments\IncomingPaymentStoreRequest;
use App\Http\Requests\Api\Internal\IncomingPayments\IncomingPaymentUpdateRequest;
use App\Http\Resources\Money\IncomingPayments\IncomingPaymentIndexCollection;
use App\Http\Resources\Money\IncomingPayments\IncomingPaymentShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class IncomingPaymentController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly IncomingPaymentsServiceContract $service,
        private readonly IncomingPaymentPolicyContract $policy
    ) {
    }

    /**
     * @response IncomingPaymentIndexCollection<IncomingPaymentIndexResource>
     */
    public function index(IncomingPaymentIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->validated());
            $collection = new IncomingPaymentIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(IncomingPaymentStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response IncomingPaymentShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(IncomingPaymentShowResource::make($data));
        });
    }

    public function update(IncomingPaymentUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
