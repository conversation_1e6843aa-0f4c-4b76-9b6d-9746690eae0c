<?php

namespace App\Http\Controllers\Api\Internal\Money\IncomingPayments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Finances\IncomingPaymentItemPolicyContract;
use App\Contracts\Services\Internal\Finances\IncomingPaymentItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\IncomingPaymentItems\IncomingPaymentItemIndexRequest;
use App\Http\Requests\Api\Internal\IncomingPaymentItems\IncomingPaymentItemStoreRequest;
use App\Http\Requests\Api\Internal\IncomingPaymentItems\IncomingPaymentItemUpdateRequest;
use App\Http\Resources\Money\IncomingPayments\IncomingPaymentItemIndexCollection;
use App\Http\Resources\Money\IncomingPayments\IncomingPaymentItemShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class IncomingPaymentItemController extends Controller
{
    use ApiPolicy;
    public function __construct(
        private readonly IncomingPaymentItemsServiceContract $service,
        private readonly IncomingPaymentItemPolicyContract $policy
    ) {
    }

    /**
     * @response IncomingPaymentItemIndexCollection<IncomingPaymentItemIndexResource>
     */
    public function index(IncomingPaymentItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDTO();

            $this->policy->index($data->id);
            $data = $this->service->index($data);
            $collection = new IncomingPaymentItemIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(IncomingPaymentItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response IncomingPaymentItemShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(IncomingPaymentItemShowResource::make($data));
        });
    }

    public function update(IncomingPaymentItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
