<?php

namespace App\Http\Controllers\Api\Internal\Money;

use App\Contracts\Policies\Finances\FinancePolicyContract;
use App\Contracts\Services\Internal\Finances\FinanceServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\Finances\FinanceIndexRequest;
use App\Http\Requests\Api\Internal\Finances\PaymentBulkRequest;
use App\Http\Resources\Money\Finance\FinanceIndexCollection;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class FinanceController extends Controller
{
    use ApiResponse;
    public function __construct(
        private readonly FinanceServiceContract $service,
        private readonly FinancePolicyContract $policy
    ) {
    }

    /**
     * @response FinanceIndexCollection<FinanceIndexResource>
     */
    public function index(FinanceIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new FinanceIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function bulkDelete(PaymentBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return response()->json('OK!', 200);
        });
    }
    public function bulkCopy(PaymentBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            $this->service->bulkCopy($data['ids']);
            return response()->json('OK!', 200);
        });
    }
    public function bulkHeld(PaymentBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkHeld($data['ids']);
            return response()->json('OK!', 200);
        });
    }
    public function bulkUnheld(PaymentBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkUnheld($data['ids']);
            return response()->json('OK!', 200);
        });
    }
}
