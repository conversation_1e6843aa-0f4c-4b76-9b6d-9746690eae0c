<?php

namespace App\Http\Controllers\Api\Internal\Money\OutgoingPayments;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Finances\OutgoingPaymentItemPolicyContract;
use App\Contracts\Services\Internal\Finances\OutgoingPaymentItemsServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\OutgoingPaymentItems\OutgoingPaymentItemIndexRequest;
use App\Http\Requests\Api\Internal\OutgoingPaymentItems\OutgoingPaymentItemStoreRequest;
use App\Http\Requests\Api\Internal\OutgoingPaymentItems\OutgoingPaymentItemUpdateRequest;
use App\Http\Resources\Money\OutgoingPayments\OutgoingPaymentItemIndexCollection;
use App\Http\Resources\Money\OutgoingPayments\OutgoingPaymentItemShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OutgoingPaymentItemController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly OutgoingPaymentItemsServiceContract $service,
        private readonly OutgoingPaymentItemPolicyContract $policy
    ) {
    }

    /**
     * @response OutgoingPaymentItemIndexCollection<OutgoingPaymentItemIndexResource>
     */
    public function index(OutgoingPaymentItemIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $orderId = $request->validated('outgoing_payment_id');

            $this->policy->index($orderId);
            $data = $this->service->index(['outgoing_payment_id' => $orderId]);
            $collection = new OutgoingPaymentItemIndexCollection($data);
            return $this->successResponse($collection);
        });
    }

    public function store(OutgoingPaymentItemStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response OutgoingPaymentItemShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(OutgoingPaymentItemShowResource::make($data));
        });
    }

    public function update(OutgoingPaymentItemUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
