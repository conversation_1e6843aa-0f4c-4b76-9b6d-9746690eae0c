<?php

namespace App\Http\Controllers\Api\Internal\Procurement\VendorOrders;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Purchases\VendorOrderPolicyContract;
use App\Contracts\Services\Internal\Purchases\VendorOrderServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\VendorOrders\VendorOrderBulkRequest;
use App\Http\Requests\Api\Internal\VendorOrders\VendorOrderIndexRequest;
use App\Http\Requests\Api\Internal\VendorOrders\VendorOrderStoreRequest;
use App\Http\Requests\Api\Internal\VendorOrders\VendorOrderUpdateRequest;
use App\Http\Resources\Procurement\VendorOrders\VendorOrderIndexCollection;
use App\Http\Resources\Procurement\VendorOrders\VendorOrderShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class VendorOrderController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly VendorOrderServiceContract $service,
        private readonly VendorOrderPolicyContract $policy
    ) {
    }

    /**
     * @response VendorOrderIndexCollection<VendorOrderIndexResource>
     */
    public function index(VendorOrderIndexRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $result = $this->service->index($request->toDTO());
            
            $collection = new VendorOrderIndexCollection($result['data']);
            return $this->successResponse($collection->additional(['meta' => $result['meta']]));
        });
    }

    public function store(VendorOrderStoreRequest $request): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response VendorOrderShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(VendorOrderShowResource::make($data));
        });
    }

    public function update(VendorOrderUpdateRequest $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): ?JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(VendorOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkHeld(VendorOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkHeld($data['ids']);
            return $this->noContentResponse();
        });
    }

    public function bulkUnheld(VendorOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkHeld($data);

            $this->service->bulkUnheld($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkWaiting(VendorOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $this->service->bulkWaiting($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkUnwaiting(VendorOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $this->service->bulkUnwaiting($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function bulkCopy(VendorOrderBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkCopy($data);

            $this->service->bulkCopy($data['ids']);
            return $this->noContentResponse();
        });
    }
    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
