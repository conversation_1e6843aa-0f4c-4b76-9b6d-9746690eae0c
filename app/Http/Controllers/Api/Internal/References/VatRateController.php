<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\VatRatePolicyContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Services\Internal\ArchiveServiceContract;
use App\Contracts\Services\Internal\Directories\VatRatesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\VatRates\VatRateBulkRequest;
use App\Http\Requests\Api\Internal\VatRates\VatRateIndexRequest;
use App\Http\Requests\Api\Internal\VatRates\VatRateStoreRequest;
use App\Http\Requests\Api\Internal\VatRates\VatRateUpdateRequest;
use App\Http\Resources\References\VatRates\VatRateIndexCollection;
use App\Http\Resources\References\VatRates\VatRateShowResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class VatRateController extends Controller
{
    use ApiPolicy;

    public function __construct(
        public readonly VatRatesServiceContract $service,
        private readonly VatRatePolicyContract $policy
    ) {
    }

    /**
     * @response VatRateIndexCollection<VatRateIndexResource>
     */
    public function index(VatRateIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            $collection = new VatRateIndexCollection($data['data']);
            return $this->successResponse($collection->additional(['meta' => $data['meta']]));
        });
    }

    public function store(VatRateStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    /**
     * @response VatRateShowResource
     */
    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);
            return $this->successResponse(VatRateShowResource::make($data));
        });
    }

    public function update(VatRateUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    public function bulkDelete(VatRateBulkRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->validated();
            $this->policy->bulkDelete($data);

            $this->service->bulkDelete($data['ids']);
            return $this->noContentResponse();
        });
    }
    public function archive(
        VatRateBulkRequest $request,
        ArchiveServiceContract $archiveService,
        VatRatesRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $repository, $archiveService) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->archive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }
    public function unarchive(
        VatRateBulkRequest $request,
        ArchiveServiceContract $archiveService,
        VatRatesRepositoryContract $repository
    ): JsonResponse {
        return $this->executeAction(function () use ($request, $archiveService, $repository) {
            $data = $request->validated();
            $this->policy->bulkUpdate($data);

            $archiveService->unarchive($repository, $data['ids']);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
