<?php

namespace App\Http\Controllers\Api\Internal\References;

use App\Contracts\Policies\BaseResourcePolicyContract;
use App\Contracts\Policies\Directories\LegalEntityPolicyContract;
use App\Contracts\Services\Internal\Directories\LegalEntitiesServiceContract;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Internal\LegalEntities\LegalEntityIndexRequest;
use App\Http\Requests\Api\Internal\LegalEntities\LegalEntityStoreRequest;
use App\Http\Requests\Api\Internal\LegalEntities\LegalEntityUpdateRequest;
use App\Http\Resources\LegalEntityResource;
use App\Traits\ApiPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LegalEntityController extends Controller
{
    use ApiPolicy;

    public function __construct(
        private readonly LegalEntitiesServiceContract $service,
        private readonly LegalEntityPolicyContract $policy
    ) {
    }

    public function index(LegalEntityIndexRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $this->service->index($request->toDTO());
            return $this->successResponse($data);
        });
    }

    public function store(LegalEntityStoreRequest $request): JsonResponse
    {
        return $this->executeAction(function () use ($request) {

            $data = $request->toDto();

            $this->authorizeCreate($request, $data);

            $id = $this->service->create($data);
            return $this->createdResponse($id);
        });
    }

    public function show(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeView($request, $id);

            $data = $this->service->show($id);

            return $this->successResponse(
                new LegalEntityResource($data)
            );
        });
    }

    public function update(LegalEntityUpdateRequest $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request) {
            $data = $request->toDto();

            $this->authorizeUpdate($request, $data);

            $this->service->update($data);
            return $this->noContentResponse();
        });
    }

    public function destroy(Request $request, string $id): JsonResponse
    {
        return $this->executeAction(function () use ($request, $id) {
            $this->authorizeDelete($request, $id);

            $this->service->delete($id);
            return $this->noContentResponse();
        });
    }

    protected function getPolicy(): BaseResourcePolicyContract
    {
        return $this->policy;
    }
}
