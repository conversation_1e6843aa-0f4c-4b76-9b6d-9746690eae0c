<?php

namespace App\Http\Resources\Goods\Attributes\Groups;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AttributeGroupShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор группы атрибутов */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $name Название группы */
            'name' => (string) $this->name,
            /** @var string|null $description Описание группы */
            'description' => $this->description,
            /** @var int $sort_order Порядок сортировки */
            'sort_order' => (int) $this->sort_order,
            /** @var bool $status Статус активности */
            'status' => (bool) $this->status,
            /** @var array{id: string, name: string, description: string|null, sort_order: int, status: bool, cabinet_id: string, attribute_groups_id: string|null}[] $attributes Атрибуты в группе */
            'attributes' => !empty($this->attributes) ? array_map(function($attribute) {
                return [
                    'id' => $attribute['id'],
                    'name' => $attribute['name'],
                    'description' => $attribute['description'] ?? null,
                    'sort_order' => (int) $attribute['sort_order'],
                    'status' => (bool) $attribute['status'],
                    'cabinet_id' => $attribute['cabinet_id'],
                    'attribute_groups_id' => $attribute['attribute_groups_id'] ?? null,
                ];
            }, $this->attributes) : [],
        ];
    }
}
