<?php

namespace App\Http\Resources\Marketplaces\Ozon\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FBSWarehouseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор склада */
            'id' => $this->id,
            /** @var string $created_at Дата создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата обновления */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $integration_id Идентификатор интеграции <PERSON>on */
            'integration_id' => $this->integration_id,
            /** @var string|null $name Название склада */
            'name' => $this->name,
            /** @var int $ozon_warehouse_id Идентификатор склада в Ozon */
            'ozon_warehouse_id' => (int) $this->ozon_warehouse_id,
            /** @var string $warehouse_id Идентификатор склада в системе */
            'warehouse_id' => $this->warehouse_id,
            /** @var bool $is_rfbs Признак работы по схеме rFBS */
            'is_rfbs' => (bool) $this->is_rfbs,
            /** @var bool $is_able_to_set_price Возможность установки цены */
            'is_able_to_set_price' => (bool) $this->is_able_to_set_price,
            /** @var bool $has_entrusted_acceptance Доверенная приемка */
            'has_entrusted_acceptance' => (bool) $this->has_entrusted_acceptance,
            /** @var bool $is_kgt Признак КГТ */
            'is_kgt' => (bool) $this->is_kgt,
            /** @var bool $can_print_act_in_advance Возможность печати акта заранее */
            'can_print_act_in_advance' => (bool) $this->can_print_act_in_advance,
            /** @var int|null $min_working_days Минимальные рабочие дни */
            'min_working_days' => $this->min_working_days ? (int) $this->min_working_days : null,
            /** @var bool $is_karantin Признак карантина */
            'is_karantin' => (bool) $this->is_karantin,
            /** @var bool $has_postings_limit Наличие лимита отправлений */
            'has_postings_limit' => (bool) $this->has_postings_limit,
            /** @var int|null $postings_limit Лимит отправлений */
            'postings_limit' => $this->postings_limit ? (int) $this->postings_limit : null,
            /** @var array $working_days Рабочие дни */
            'working_days' => $this->working_days ?? [],
            /** @var int|null $min_postings_limit Минимальный лимит отправлений */
            'min_postings_limit' => $this->min_postings_limit ? (int) $this->min_postings_limit : null,
            /** @var bool $is_timetable_editable Возможность редактирования расписания */
            'is_timetable_editable' => (bool) $this->is_timetable_editable,
            /** @var string|null $status Статус склада */
            'status' => $this->status,
            /** @var bool $is_economy Признак экономичности */
            'is_economy' => (bool) $this->is_economy,
            /** @var bool $is_presorted Признак предсортировки */
            'is_presorted' => (bool) $this->is_presorted,
            /** @var array $first_mile_type Настройки первой мили */
            'first_mile_type' => $this->first_mile_type ?? [],

            /** @var array<int, array{id: string, ozon_delivery_method_id: int, name: string, status: string, cutoff: string|null, sla_cut_in: string|null, provider_id: int|null, template_id: int|null, created_at: string|null, updated_at: string|null}> $delivery_methods Методы доставки */
            'delivery_methods' => $this->delivery_methods ?? [],
        ];
    }
}
