<?php

namespace App\Http\Resources\Marketplaces\Wildberries\Products;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class ProductToMatchCollection extends ResourceCollection
{
    public $collects = ProductToMatchResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'products_to_match' => $this->collection,
        ];
    }
}
