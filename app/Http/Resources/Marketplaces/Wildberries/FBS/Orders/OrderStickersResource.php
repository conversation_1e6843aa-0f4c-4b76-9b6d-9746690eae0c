<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderStickersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var array<int, array{orderId: int, partA: string, partB: string, barcode: string, file: string}> $stickers Массив стикеров заказов */
            'stickers' => collect($this->resource['stickers'])->map(function ($sticker) {
                return [
                    /** @var int $orderId Идентификатор заказа */
                    'orderId' => $sticker->orderId,
                    /** @var string $partA Первая часть штрихкода */
                    'partA' => $sticker->partA,
                    /** @var string $partB Вторая часть штрихкода */
                    'partB' => $sticker->partB,
                    /** @var string $barcode Штрихкод */
                    'barcode' => $sticker->barcode,
                    /** @var string $file Base64 закодированный файл стикера */
                    'file' => $sticker->file,
                ];
            })->toArray(),
            /** @var string $filetype Тип файла стикеров */
            'filetype' => $this->resource['filetype'],
        ];
    }
}
