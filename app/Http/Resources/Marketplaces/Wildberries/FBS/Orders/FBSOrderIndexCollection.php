<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Orders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class FBSOrderIndexCollection extends ResourceCollection
{
    public $collects = FBSOrderIndexResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'orders' => $this->collection,
            'page' => $this->additional['page'] ?? 1,
            'per_page' => $this->additional['per_page'] ?? 15,
        ];
    }
}
