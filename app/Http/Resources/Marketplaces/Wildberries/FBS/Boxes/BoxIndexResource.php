<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Boxes;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BoxIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор коробки */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $integration_id Идентификатор интеграции */
            'integration_id' => $this->when(
                property_exists($this->resource, 'integration_id'),
                function() {
                    return $this->resource->integration_id;
                }
            ),
            /** @var string $trbxIds Идентификатор коробки в системе Wildberries */
            'trbxIds' => $this->when(
                property_exists($this->resource, 'trbxIds'),
                function() {
                    return $this->resource->trbxIds;
                }
            ),
            /** @var string $supply_id Идентификатор поставки */
            'supply_id' => $this->when(
                property_exists($this->resource, 'supply_id'),
                function() {
                    return $this->resource->supply_id;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
        ];
    }
}
