<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class WarehouseIndexCollection extends ResourceCollection
{
    public $collects = WarehouseIndexResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
        ];
    }
}
