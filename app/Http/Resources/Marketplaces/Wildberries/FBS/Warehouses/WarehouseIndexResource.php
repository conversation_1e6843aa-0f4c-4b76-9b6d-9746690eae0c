<?php

namespace App\Http\Resources\Marketplaces\Wildberries\FBS\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $wildberries_integration_id Идентификатор интеграции Wildberries */
            'wildberries_integration_id' => $this->when(
                property_exists($this->resource, 'wildberries_integration_id'),
                function() {
                    return $this->resource->wildberries_integration_id;
                }
            ),
            /** @var string $status Статус сопоставления склада */
            'status' => $this->when(
                property_exists($this->resource, 'status'),
                function() {
                    return $this->resource->status;
                }
            ),
            /** @var string|null $name Название склада */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return $this->resource->name;
                }
            ),
            /** @var int $office_id ID склада WB */
            'office_id' => $this->when(
                property_exists($this->resource, 'office_id'),
                function() {
                    return $this->resource->office_id;
                }
            ),
            /** @var int $seller_warehouse_id ID склада продавца */
            'seller_warehouse_id' => $this->when(
                property_exists($this->resource, 'seller_warehouse_id'),
                function() {
                    return $this->resource->seller_warehouse_id;
                }
            ),
            /** @var string $warehouse_id ID склада в системе */
            'warehouse_id' => $this->when(
                property_exists($this->resource, 'warehouse_id'),
                function() {
                    return $this->resource->warehouse_id;
                }
            ),
            /** @var int $cargo_type Тип товара (1-МГТ, 2-СГТ, 3-КГТ+) */
            'cargo_type' => $this->when(
                property_exists($this->resource, 'cargo_type'),
                function() {
                    return $this->resource->cargo_type;
                }
            ),
            /** @var int $delivery_type Тип доставки (1-FBS, 2-DBS, 3-DBW) */
            'delivery_type' => $this->when(
                property_exists($this->resource, 'delivery_type'),
                function() {
                    return $this->resource->delivery_type;
                }
            ),
        ];
    }
}
