<?php

namespace App\Http\Resources\Workspace\Departments;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор отдела */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $name Название отдела */
            'name' => (string) $this->name,
            /** @var int $sorting Порядок сортировки */
            'sorting' => (int) $this->sorting,
            /** @var bool $is_default Отдел по умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->sales_channel_id,
            /** @var bool $is_common Общий отдел */
            'is_common' => (bool) $this->is_common,
            /** @var int $sort Сортировка */
            'sort' => (int) $this->sort,
        ];
    }
}
