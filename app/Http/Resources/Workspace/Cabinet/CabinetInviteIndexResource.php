<?php

namespace App\Http\Resources\Workspace\Cabinet;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CabinetInviteIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор приглашения */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var string $email Email приглашенного */
            'email' => $this->when(
                property_exists($this->resource, 'email'),
                function() {
                    return $this->resource->email;
                }
            ),
            /** @var string $status Статус приглашения */
            'status' => $this->when(
                property_exists($this->resource, 'status'),
                function() {
                    return $this->resource->status;
                }
            ),
            /** @var string $token Токен приглашения */
            'token' => $this->when(
                property_exists($this->resource, 'token'),
                function() {
                    return $this->resource->token;
                }
            ),
            /** @var string|null $role_id Идентификатор роли */
            'role_id' => $this->when(
                property_exists($this->resource, 'role_id'),
                function() {
                    return $this->resource->role_id;
                }
            ),
            /** @var array{id: string, firstname: string, lastname: string|null, email: string}|null $employee Сотрудник */
            'employee' => $this->when(property_exists($this->resource, 'employee'),
                function() {
                    return [
                        'id' => $this->employee['id'],
                        'firstname' => $this->when(isset($this->employee['firstname']), $this->employee['firstname']),
                        'lastname' => $this->employee['lastname'] ?? null,
                        'email' => $this->when(isset($this->employee['email']), $this->employee['email']),
                    ];
                }),
            /** @var array{id: string, name: string}|null $department Отдел */
            'department' => $this->when(property_exists($this->resource, 'department'),
                function() {
                    return [
                        'id' => $this->department['id'],
                        'name' => $this->when(isset($this->department['name']), $this->department['name']),
                    ];
            }),
            /** @var array{id: string, name: string}|null $role Роль */
            'role' => $this->when(property_exists($this->resource, 'role'),
            function() {
                return [
                    'id' => $this->role['id'],
                    'name' => $this->when(isset($this->role['name']), $this->role['name']),
                ];
            }),
        ];
    }
}
