<?php

namespace App\Http\Resources\Procurement\Acceptances;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AcceptanceItemShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $acceptance_id Идентификатор приемки */
            'acceptance_id' => $this->acceptance_id,
            /** @var string $product_id Идентификатор товара */
            'product_id' => $this->product_id,
            /** @var int $quantity Количество товара */
            'quantity' => (int) $this->quantity,
            /** @var string $price Цена товара */
            'price' => (string) $this->price,
            /** @var string|null $vat_rate_id Идентификатор ставки НДС */
            'vat_rate_id' => $this->vat_rate_id,
            /** @var string $discount Размер скидки */
            'discount' => (string) ($this->discount ?? 0),
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
            /** @var string|null $country_id Идентификатор страны */
            'country_id' => $this->country_id,
            /** @var string|null $gtd_number Номер ГТД */
            'gtd_number' => $this->gtd_number,
        ];
    }
}
