<?php

namespace App\Http\Resources\Warehouses;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseCalendarShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи календаря */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string $schedule_id Идентификатор расписания */
            'schedule_id' => $this->schedule_id,
            /** @var string $date Дата */
            'date' => $this->date,
            /** @var bool $is_working_day Рабочий день */
            'is_working_day' => (bool) $this->is_working_day,
            /** @var bool $is_holiday Праздничный день */
            'is_holiday' => (bool) $this->is_holiday,
            /** @var string|null $template_reference Ссылка на шаблон */
            'template_reference' => $this->template_reference,
        ];
    }
}
