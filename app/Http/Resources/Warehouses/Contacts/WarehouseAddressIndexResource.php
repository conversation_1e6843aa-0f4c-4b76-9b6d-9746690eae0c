<?php

namespace App\Http\Resources\Warehouses\Contacts;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WarehouseAddressIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор адреса склада */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $country_id Идентификатор страны */
            'country_id' => $this->when(
                property_exists($this->resource, 'country_id'),
                function() {
                    return $this->resource->country_id;
                }
            ),
            /** @var string|null $postcode Почтовый индекс */
            'postcode' => $this->when(
                property_exists($this->resource, 'postcode'),
                function() {
                    return $this->resource->postcode;
                }
            ),
            /** @var string|null $region Регион */
            'region' => $this->when(
                property_exists($this->resource, 'region'),
                function() {
                    return $this->resource->region;
                }
            ),
            /** @var string|null $city Город */
            'city' => $this->when(
                property_exists($this->resource, 'city'),
                function() {
                    return $this->resource->city;
                }
            ),
            /** @var string|null $street Улица */
            'street' => $this->when(
                property_exists($this->resource, 'street'),
                function() {
                    return $this->resource->street;
                }
            ),
            /** @var string|null $house Дом */
            'house' => $this->when(
                property_exists($this->resource, 'house'),
                function() {
                    return $this->resource->house;
                }
            ),
            /** @var string|null $office Офис */
            'office' => $this->when(
                property_exists($this->resource, 'office'),
                function() {
                    return $this->resource->office;
                }
            ),
            /** @var string|null $other Другое */
            'other' => $this->when(
                property_exists($this->resource, 'other'),
                function() {
                    return $this->resource->other;
                }
            ),
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function() {
                    return $this->resource->comment;
                }
            ),
        ];
    }
}
