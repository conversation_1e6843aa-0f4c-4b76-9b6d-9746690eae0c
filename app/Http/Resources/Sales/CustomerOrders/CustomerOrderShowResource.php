<?php

namespace App\Http\Resources\Sales\CustomerOrders;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerOrderShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор записи */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания записи */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления записи */
            'updated_at' => $this->updated_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_common Флаг общего доступа */
            'is_common' => (bool) $this->is_common,
            /** @var string $number Номер документа */
            'number' => (string) $this->number,
            /** @var string $date_from Дата начала действия */
            'date_from' => $this->date_from,
            /** @var string $payment_status Статус оплаты (paid|unpaid) */
            'payment_status' => (string) $this->payment_status,
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->status_id,
            /** @var bool $held Флаг удержания */
            'held' => (bool) $this->held,
            /** @var bool $reserve Флаг резерва */
            'reserve' => (bool) $this->reserve,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->legal_entity_id,
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->contractor_id,
            /** @var string|null $plan_date Плановая дата */
            'plan_date' => $this->plan_date,
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->sales_channel_id,
            /** @var string|null $warehouse_id Идентификатор склада */
            'warehouse_id' => $this->warehouse_id,
            /** @var string $total_price Общая стоимость */
            'total_price' => (string) $this->total_price,
            /** @var string|null $comment Комментарий */
            'comment' => $this->comment,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var bool $has_vat Наличие НДС */
            'has_vat' => (bool) $this->has_vat,
            /** @var bool $price_includes_vat Цена включает НДС */
            'price_includes_vat' => (bool) $this->price_includes_vat,

            /** @var array{id: string, title: string} $contractor Контрагент */
            'contractor' => $this->when(!empty($this->contractor),
                [
                    'id' => $this->contractor['id'],
                    'title' => $this->contractor['title'],
                ]),

            /** @var array{id: string, name: string}|null $status Статус */
            'status' => $this->status ? [
                'id' => $this->status['id'],
                'name' => $this->status['name'],
            ] : [],

            /** @var array{id: string, name: string}|null $warehouse Склад */
            'warehouse' => $this->warehouse ? [
                'id' => $this->warehouse['id'],
                'name' => $this->warehouse['name']
            ] : [],

            /** @var AnonymousResourceCollection<FileResource>|null $files Файлы */
            'files' => $this->files ? \App\Http\Resources\FileResource::collection($this->files) : [],

            /** @var CustomerOrderDeliveryInfoResource|null $delivery_info Информация о доставке */
            'delivery_info' => $this->delivery_info ? new CustomerOrderDeliveryInfoResource($this->delivery_info) : [],
        ];
    }
}
