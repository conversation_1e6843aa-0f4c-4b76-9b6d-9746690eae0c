<?php

namespace App\Http\Resources\Sales\ComissionReports;

use App\Http\Resources\Relations\CabinetCurrencyRelationResource;
use App\Http\Resources\Relations\ContractorRelationResource;
use App\Http\Resources\Relations\ContractRelationResource;
use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\LegalEntityRelationResource;
use App\Http\Resources\Relations\SalesChannelRelationResource;
use App\Http\Resources\Relations\StatusRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IssuedComissionReportIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор отчета */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string|null $status_id Идентификатор статуса */
            'status_id' => $this->when(
                property_exists($this->resource, 'status_id'),
                function () {
                    return $this->resource->status_id;
                }
            ),
            /** @var string $number Номер отчета */
            'number' => (string) $this->number,
            /** @var string $date_from Дата отчета */
            'date_from' => $this->when(
                property_exists($this->resource, 'date_from'),
                function () {
                    return $this->resource->date_from;
                }
            ),
            /** @var bool $is_held Проведен */
            'is_held' => (bool) $this->is_held,
            /** @var bool $is_printed Напечатан */
            'is_printed' => (bool) $this->is_printed,
            /** @var string $legal_entity_id Идентификатор юридического лица */
            'legal_entity_id' => $this->when(
                property_exists($this->resource, 'legal_entity_id'),
                function () {
                    return $this->resource->legal_entity_id;
                }
            ),
            /** @var string $contractor_id Идентификатор контрагента */
            'contractor_id' => $this->when(
                property_exists($this->resource, 'contractor_id'),
                function () {
                    return $this->resource->contractor_id;
                }
            ),
            /** @var string|null $sales_channel_id Идентификатор канала продаж */
            'sales_channel_id' => $this->when(
                property_exists($this->resource, 'sales_channel_id'),
                function () {
                    return $this->resource->sales_channel_id;
                }
            ),
            /** @var string $currency_id Идентификатор валюты */
            'currency_id' => $this->when(
                property_exists($this->resource, 'currency_id'),
                function () {
                    return $this->resource->currency_id;
                }
            ),
            /** @var string $sum Сумма */
            'sum' => (string) $this->sum,
            /** @var string|null $comment Комментарий */
            'comment' => $this->when(
                property_exists($this->resource, 'comment'),
                function () {
                    return $this->resource->comment;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function () {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function () {
                    return $this->resource->department_id;
                }
            ),
            /** @var string $comission_type Тип комиссии */
            'comission_type' => (string) $this->comission_type,
            /** @var string $comission_value Значение комиссии */
            'comission_value' => (string) $this->comission_value,
            /** @var bool $is_common Общий отчет */
            'is_common' => (bool) $this->is_common,
            /** @var string $period_from Период с */
            'period_from' => $this->when(
                property_exists($this->resource, 'period_from'),
                function () {
                    return $this->resource->period_from;
                }
            ),
            /** @var string $period_to Период по */
            'period_to' => $this->when(
                property_exists($this->resource, 'period_to'),
                function () {
                    return $this->resource->period_to;
                }
            ),
            /** @var string $contract_id Идентификатор контракта */
            'contract_id' => $this->when(
                property_exists($this->resource, 'contract_id'),
                function () {
                    return $this->resource->contract_id;
                }
            ),

            /** @var StatusRelationResource|null $statuses Информация о статусе */
            'statuses' => $this->when(property_exists($this->resource, 'statuses'), function () {
                return new StatusRelationResource($this->resource->statuses ?? []);
            }),
            /** @var LegalEntityRelationResource|null $legal_entities Информация о юридическом лице */
            'legal_entities' => $this->when(property_exists($this->resource, 'legal_entities'), function () {
                return new LegalEntityRelationResource($this->resource->legal_entities ?? []);
            }),
            /** @var ContractorRelationResource|null $contractors Информация о контрагенте */
            'contractors' => $this->when(property_exists($this->resource, 'contractors'), function () {
                return new ContractorRelationResource($this->resource->contractors ?? []);
            }),
            /** @var SalesChannelRelationResource|null $sales_channels Информация о канале продаж */
            'sales_channels' => $this->when(property_exists($this->resource, 'sales_channels'), function () {
                return new SalesChannelRelationResource($this->resource->sales_channels ?? []);
            }),
            /** @var CabinetCurrencyRelationResource|null $cabinet_currencies Информация о валюте */
            'cabinet_currencies' => $this->when(property_exists($this->resource, 'cabinet_currencies'), function () {
                return new CabinetCurrencyRelationResource($this->resource->cabinet_currencies ?? []);
            }),
            /** @var EmployeeRelationResource|null $employees Информация о сотруднике */
            'employees' => $this->when(property_exists($this->resource, 'employees'), function () {
                return new EmployeeRelationResource($this->resource->employees ?? []);
            }),
            /** @var DepartmentRelationResource|null $departments Информация об отделе */
            'departments' => $this->when(property_exists($this->resource, 'departments'), function () {
                return new DepartmentRelationResource($this->resource->departments ?? []);
            }),
            /** @var ContractRelationResource|null $contracts Информация о контракте */
            'contracts' => $this->when(property_exists($this->resource, 'contracts'), function () {
                return new ContractRelationResource($this->resource->contracts ?? []);
            }),
        ];
    }
}
