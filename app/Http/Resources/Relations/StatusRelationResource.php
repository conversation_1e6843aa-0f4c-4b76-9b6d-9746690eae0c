<?php

namespace App\Http\Resources\Relations;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StatusRelationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Идентификатор статуса */
            'id' => $this->when(isset($this->resource['id']), function () {
                return $this->resource['id'];
            }),
            /** @var string|null $created_at Дата создания */
            'created_at' => $this->when(isset($this->resource['created_at']), function () {
                return $this->resource['created_at'];
            }),
            /** @var string|null $updated_at Дата обновления */
            'updated_at' => $this->when(isset($this->resource['updated_at']), function () {
                return $this->resource['updated_at'];
            }),
            /** @var string|null $deleted_at Дата удаления */
            'deleted_at' => $this->when(isset($this->resource['deleted_at']), function () {
                return $this->resource['deleted_at'];
            }),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(isset($this->resource['cabinet_id']), function () {
                return $this->resource['cabinet_id'];
            }),
            /** @var string $name Название статуса */
            'name' => $this->when(isset($this->resource['name']), function () {
                return $this->resource['name'];
            }),
            /** @var string|null $color Цвет статуса */
            'color' => $this->when(isset($this->resource['color']), function () {
                return $this->resource['color'];
            }),
            /** @var string|null $type_id Идентификатор типа статуса */
            'type' => $this->when(isset($this->resource['type']), function () {
                return $this->resource['type'];
            }),
        ];
    }
}
