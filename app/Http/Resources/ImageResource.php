<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use App\Services\Storage\S3StorageService;
use Illuminate\Http\Resources\Json\JsonResource;

class ImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $storageService = app(S3StorageService::class);

        return [
                'id' => $this->id,
                'path' => $storageService->getUrl($this->path, $this->is_private),
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
                'name' => $this->name,
                'size' => $this->size,
                'mime_type' => $this->mime_type,
                'employee_id' => $this->employee_id,
                'type' => $this->type,
        ];
    }
}
