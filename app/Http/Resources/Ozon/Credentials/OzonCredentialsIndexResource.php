<?php

namespace App\Http\Resources\Ozon\Credentials;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OzonCredentialsIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор учетных данных Ozon */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var string $name Название учетных данных */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string $client_id Client ID для Ozon API */
            'client_id' => $this->when(
                property_exists($this->resource, 'client_id'),
                function() {
                    return (string) $this->resource->client_id;
                }
            ),
            /** @var string $api_key API ключ для Ozon API */
            'api_key' => $this->when(
                property_exists($this->resource, 'api_key'),
                function() {
                    return (string) $this->resource->api_key;
                }
            ),
        ];
    }
}
