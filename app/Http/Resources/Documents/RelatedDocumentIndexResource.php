<?php

namespace App\Http\Resources\Documents;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class RelatedDocumentIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $documentable_id Идентификатор документа */
            'documentable_id' => $this->when(
                property_exists($this->resource, 'documentable_id'),
                function() {
                    return $this->resource->documentable_id;
                }
            ),
            /** @var string $documentable_type Тип документа */
            'documentable_type' => $this->when(
                property_exists($this->resource, 'documentable_type'),
                function() {
                    return (string) $this->resource->documentable_type;
                }
            ),
            /** @var string $tree_id Идентификатор дерева */
            'tree_id' => $this->when(
                property_exists($this->resource, 'tree_id'),
                function() {
                    return $this->resource->tree_id;
                }
            ),
            /** @var int $lft Левая граница узла */
            'lft' => $this->when(
                property_exists($this->resource, 'lft'),
                function() {
                    return (int) $this->resource->lft;
                }
            ),
            /** @var int $rgt Правая граница узла */
            'rgt' => $this->when(
                property_exists($this->resource, 'rgt'),
                function() {
                    return (int) $this->resource->rgt;
                }
            ),
            /** @var string|null $parent_id Идентификатор родительского документа */
            'parent_id' => $this->when(
                property_exists($this->resource, 'parent_id'),
                function() {
                    return $this->resource->parent_id;
                }
            ),
            /** @var array $children Дочерние документы */
            'children' => $this->when(
                property_exists($this->resource, 'children'),
                function() use ($request) {
                    return $this->resource->children ? array_map(function ($child) use ($request) {
                        return RelatedDocumentIndexResource::make($child)->toArray($request);
                    }, $this->resource->children) : [];
                }
            ),
        ];
    }
}
