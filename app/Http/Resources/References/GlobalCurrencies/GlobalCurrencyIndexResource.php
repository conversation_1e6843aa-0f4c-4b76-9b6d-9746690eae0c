<?php

namespace App\Http\Resources\References\GlobalCurrencies;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GlobalCurrencyIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор валюты */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string $num_code Числовой код валюты */
            'num_code' => $this->when(
                property_exists($this->resource, 'num_code'),
                function() {
                    return (string) $this->resource->num_code;
                }
            ),
            /** @var string $char_code Символьный код валюты */
            'char_code' => $this->when(
                property_exists($this->resource, 'char_code'),
                function() {
                    return (string) $this->resource->char_code;
                }
            ),
            /** @var string $short_name Краткое название валюты */
            'short_name' => $this->when(
                property_exists($this->resource, 'short_name'),
                function() {
                    return (string) $this->resource->short_name;
                }
            ),
            /** @var string $external_id Внешний идентификатор */
            'external_id' => $this->when(
                property_exists($this->resource, 'external_id'),
                function() {
                    return (string) $this->resource->external_id;
                }
            ),
            /** @var string|null $name Полное название валюты */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return $this->resource->name;
                }
            ),
            /** @var string $value Курс валюты */
            'value' => $this->when(
                property_exists($this->resource, 'value'),
                function() {
                    return (string) $this->resource->value;
                }
            ),
            /** @var string $currency_date Дата курса */
            'currency_date' => $this->when(
                property_exists($this->resource, 'currency_date'),
                function() {
                    return $this->resource->currency_date;
                }
            ),
            /** @var bool $is_default Валюта по умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function() {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var array $pluralization Склонения валюты */
            'pluralization' => $this->when(
                property_exists($this->resource, 'pluralization'),
                function() {
                    return $this->resource->pluralization ? (array) $this->resource->pluralization : [];
                }
            ),
        ];
    }
}
