<?php

namespace App\Http\Resources\References\SalesChannels;

use App\Http\Resources\Relations\DepartmentRelationResource;
use App\Http\Resources\Relations\EmployeeRelationResource;
use App\Http\Resources\Relations\SalesChannelTypeRelationResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SalesChannelIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор канала продаж */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function () {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function () {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function () {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function () {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function () {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $name Название канала продаж */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function () {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string $sales_channel_type_id Идентификатор типа канала продаж */
            'sales_channel_type_id' => $this->when(
                property_exists($this->resource, 'sales_channel_type_id'),
                function () {
                    return $this->resource->sales_channel_type_id;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function () {
                    return $this->resource->description;
                }
            ),
            /** @var string|null $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function () {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string|null $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function () {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function () {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var bool $is_common Общий */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function () {
                    return (bool) $this->resource->is_common;
                }
            ),
            /** @var int $sort Порядок сортировки */
            'sort' => $this->when(
                property_exists($this->resource, 'sort'),
                function () {
                    return (int) $this->resource->sort;
                }
            ),
            /** @var EmployeeRelationResource|null $employees Информация о сотруднике */
            'employees' => $this->when(property_exists($this->resource, 'employees'), function () {
                return new EmployeeRelationResource($this->resource->employees ?? []);
            }),
            /** @var DepartmentRelationResource|null $departments Информация об отделе */
            'departments' => $this->when(property_exists($this->resource, 'departments'), function () {
                return new DepartmentRelationResource($this->resource->departments ?? []);
            }),
            /** @var SalesChannelTypeRelationResource|null $sales_channel_types Информация о типе канала продаж */
            'sales_channel_types' => $this->when(property_exists($this->resource, 'sales_channel_types'), function () {
                return new SalesChannelTypeRelationResource($this->resource->sales_channel_types ?? []);
            }),
        ];
    }
}
