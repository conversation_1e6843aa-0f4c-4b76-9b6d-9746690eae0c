<?php

namespace App\Http\Resources\References\LegalEntities;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LegalEntityShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор юридического лица */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->created_at,
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->updated_at,
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->deleted_at,
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->archived_at,
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->cabinet_id,
            /** @var string $short_name Краткое название */
            'short_name' => (string) $this->short_name,
            /** @var string|null $code Код */
            'code' => $this->code,
            /** @var string|null $phone Телефон */
            'phone' => $this->phone,
            /** @var string|null $fax Факс */
            'fax' => $this->fax,
            /** @var string|null $email Email */
            'email' => $this->email,
            /** @var string|null $discount_card Дисконтная карта */
            'discount_card' => $this->discount_card,
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->employee_id,
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->department_id,
            /** @var bool $is_default По умолчанию */
            'is_default' => (bool) $this->is_default,
            /** @var bool $shared_access Общий доступ */
            'shared_access' => (bool) $this->shared_access,
            /** @var array{id: string, name: string, path: string, size: int, mime_type: string} $logo_image Логотип */
            'logo_image' => !empty($this->logo_image) ? [
                'id' => $this->logo_image['id'],
                'name' => $this->logo_image['name'],
                'path' => $this->logo_image['path'],
                'size' => (int) $this->logo_image['size'],
                'mime_type' => $this->logo_image['mime_type'],
            ] : (object) [],
            /** @var array{postcode: string|null, country: string|null, region: string|null, city: string|null, street: string|null, house: string|null, office: string|null, other: string|null, comment: string|null} $address Адрес */
            'address' => !empty($this->address) ? [
                'postcode' => $this->address['postcode'] ?? null,
                'country' => $this->address['country'] ?? null,
                'region' => $this->address['region'] ?? null,
                'city' => $this->address['city'] ?? null,
                'street' => $this->address['street'] ?? null,
                'house' => $this->address['house'] ?? null,
                'office' => $this->address['office'] ?? null,
                'other' => $this->address['other'] ?? null,
                'comment' => $this->address['comment'] ?? null,
            ] : (object) [],
            /** @var array{taxation_type: string|null, tax_rate: string|null, vat_rate: string|null, type: string|null, prefix: string|null, inn: string|null, kpp: string|null, ogrn: string|null, okpo: string|null, full_name: string|null, firstname: string|null, patronymic: string|null, lastname: string|null, ogrnip: string|null, certificate_number: string|null, certificate_date: string|null} $detail Детали */
            'detail' => !empty($this->detail) ? [
                'taxation_type' => $this->detail['taxation_type'] ?? null,
                'tax_rate' => $this->detail['tax_rate'] ?? null,
                'vat_rate' => $this->detail['vat_rate'] ?? null,
                'type' => $this->detail['type'] ?? null,
                'prefix' => $this->detail['prefix'] ?? null,
                'inn' => $this->detail['inn'] ?? null,
                'kpp' => $this->detail['kpp'] ?? null,
                'ogrn' => $this->detail['ogrn'] ?? null,
                'okpo' => $this->detail['okpo'] ?? null,
                'full_name' => $this->detail['full_name'] ?? null,
                'firstname' => $this->detail['firstname'] ?? null,
                'patronymic' => $this->detail['patronymic'] ?? null,
                'lastname' => $this->detail['lastname'] ?? null,
                'ogrnip' => $this->detail['ogrnip'] ?? null,
                'certificate_number' => $this->detail['certificate_number'] ?? null,
                'certificate_date' => $this->detail['certificate_date'] ?? null,
            ] : (object) [],
            /** @var array{head_name: string|null, head_position: string|null, accountant_name: string|null} $head Руководитель */
            'head' => !empty($this->head) ? [
                'head_name' => $this->head['head_name'] ?? null,
                'head_position' => $this->head['head_position'] ?? null,
                'accountant_name' => $this->head['accountant_name'] ?? null,
            ] : (object) [],
            /** @var array{bank_name: string|null, bik: string|null, correspondent_account: string|null, account_number: string|null}[] $accounts Банковские счета */
            'accounts' => !empty($this->accounts) ? array_map(function($account) {
                return [
                    'bank_name' => $account['bank_name'] ?? null,
                    'bik' => $account['bik'] ?? null,
                    'correspondent_account' => $account['correspondent_account'] ?? null,
                    'account_number' => $account['account_number'] ?? null,
                ];
            }, $this->accounts) : [],
        ];
    }
}
