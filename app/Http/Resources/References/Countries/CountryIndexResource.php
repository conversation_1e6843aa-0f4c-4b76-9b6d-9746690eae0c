<?php

namespace App\Http\Resources\References\Countries;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CountryIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор страны */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $name Название страны */
            'name' => $this->when(
                property_exists($this->resource, 'name'),
                function() {
                    return (string) $this->resource->name;
                }
            ),
            /** @var string|null $full_name Полное название страны */
            'full_name' => $this->when(
                property_exists($this->resource, 'full_name'),
                function() {
                    return $this->resource->full_name;
                }
            ),
            /** @var string|null $code Код страны */
            'code' => $this->when(
                property_exists($this->resource, 'code'),
                function() {
                    return $this->resource->code;
                }
            ),
            /** @var string|null $iso2 ISO2 код */
            'iso2' => $this->when(
                property_exists($this->resource, 'iso2'),
                function() {
                    return $this->resource->iso2;
                }
            ),
            /** @var string|null $iso3 ISO3 код */
            'iso3' => $this->when(
                property_exists($this->resource, 'iso3'),
                function() {
                    return $this->resource->iso3;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function() {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var bool $is_common Общая страна */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function() {
                    return (bool) $this->resource->is_common;
                }
            ),
        ];
    }
}
