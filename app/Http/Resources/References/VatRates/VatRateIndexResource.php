<?php

namespace App\Http\Resources\References\VatRates;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VatRateIndexResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            /** @var string $id Уникальный идентификатор ставки НДС */
            'id' => $this->id,
            /** @var string $created_at Дата и время создания */
            'created_at' => $this->when(
                property_exists($this->resource, 'created_at'),
                function() {
                    return $this->resource->created_at;
                }
            ),
            /** @var string $updated_at Дата и время последнего обновления */
            'updated_at' => $this->when(
                property_exists($this->resource, 'updated_at'),
                function() {
                    return $this->resource->updated_at;
                }
            ),
            /** @var string|null $archived_at Дата и время архивирования */
            'archived_at' => $this->when(
                property_exists($this->resource, 'archived_at'),
                function() {
                    return $this->resource->archived_at;
                }
            ),
            /** @var string|null $deleted_at Дата и время удаления */
            'deleted_at' => $this->when(
                property_exists($this->resource, 'deleted_at'),
                function() {
                    return $this->resource->deleted_at;
                }
            ),
            /** @var string $cabinet_id Идентификатор кабинета */
            'cabinet_id' => $this->when(
                property_exists($this->resource, 'cabinet_id'),
                function() {
                    return $this->resource->cabinet_id;
                }
            ),
            /** @var string $employee_id Идентификатор сотрудника */
            'employee_id' => $this->when(
                property_exists($this->resource, 'employee_id'),
                function() {
                    return $this->resource->employee_id;
                }
            ),
            /** @var string $department_id Идентификатор отдела */
            'department_id' => $this->when(
                property_exists($this->resource, 'department_id'),
                function() {
                    return $this->resource->department_id;
                }
            ),
            /** @var int $rate Ставка НДС в процентах */
            'rate' => $this->when(
                property_exists($this->resource, 'rate'),
                function() {
                    return (int) $this->resource->rate;
                }
            ),
            /** @var string|null $description Описание */
            'description' => $this->when(
                property_exists($this->resource, 'description'),
                function() {
                    return $this->resource->description;
                }
            ),
            /** @var bool $is_default По умолчанию */
            'is_default' => $this->when(
                property_exists($this->resource, 'is_default'),
                function() {
                    return (bool) $this->resource->is_default;
                }
            ),
            /** @var bool $is_common Общая ставка */
            'is_common' => $this->when(
                property_exists($this->resource, 'is_common'),
                function() {
                    return (bool) $this->resource->is_common;
                }
            ),
        ];
    }
}
