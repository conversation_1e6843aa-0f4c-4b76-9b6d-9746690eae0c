<?php

namespace App\Enums\Api\Internal;

enum WarehouseCellTypeEnum: string
{
    case STORAGE = 'storage';      // Хранение
    case RECEIVING = 'receiving';  // Приемка
    case SHIPPING = 'shipping';    // Отгрузка
    case ARCHIVE = 'archive';      // Архив

    public function getType(): string
    {
        return match($this) {
            self::STORAGE      =>  __('Хранение'),
            self::RECEIVING    =>  __('Приемка'),
            self::SHIPPING     =>  __('Отгрузка'),
            self::ARCHIVE      =>  __('Архив'),
        };
    }
}
