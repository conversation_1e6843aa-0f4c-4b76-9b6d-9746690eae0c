<?php

namespace App\Enums\Api\Internal;

enum PackTypeInProductAccountingEnum: string
{
    case PIECE = 'piece';     
    case WEIGHT = 'weight';              
    case BOTTLING = 'bottling';  

    public function getStatus(): string
    {
        return match($this) {
            self::PIECE =>  __('Штучная'),
            self::WEIGHT =>  __('Весовая'),
            self::BOTTLING =>  __('Разливаня'),
        };
    }

}
