<?php

namespace App\Enums\Api\Internal;

enum CabinetPriceEnum: int
{
    case MIN_PRICE      = 1;     // Минимальная цена
    case PURCHASE_PRICE = 2;     // Закупочная цена
    case SALE_PPRICE    = 3;     // Цена продажи

    public function geTypePrice(): array
    {
        return match($this) {
            self::MIN_PRICE       =>  __('Минимальная цена'),
            self::PURCHASE_PRICE  =>  __('Закупочная цена'),
            self::SALE_PPRICE     =>  __('Цена продажи'),
        };
    }

}
