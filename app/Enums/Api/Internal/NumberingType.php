<?php

namespace App\Enums\Api\Internal;

enum NumberingType: string
{
    // 000001 - 999999 (для всех документов)
    case OnlyNumbers = 'only_numbers';

    // 000001 - 999999 (для всех документов, кроме отгрузочных)
    // Отгрузочные:
    // Юр. лицо - ГЛ240528-01 (где ГЛ первые буквы компании, 240528 - текущая дата, -01 - нумерация документа отгрузки на текущий день
    // ИП - ПАС240528-01 (где ПАС первые буквы ФИО, 240528 - текущая дата, -01 - нумерация документа отгрузки на текущий день
    case CPNumbers = 'cp_numbers';
}
