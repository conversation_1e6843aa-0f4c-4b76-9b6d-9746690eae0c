<?php

namespace App\Enums\Api\Internal;

enum TypeProductionEnum: string
{
    case PRODUCED_RUSSIA       = 'produced_russia';     // Произведено в РФ
    case IMPORTED_RUSSIAN      = 'imported_russian';    // Ввезён в РФ

    public function getTypeProduction(): string
    {
        return match($this) {
            self::PRODUCED_RUSSIA      =>  __('Произведено в РФ'),
            self::IMPORTED_RUSSIAN     =>  __('Ввезён в РФ'),
        };
    }
}
