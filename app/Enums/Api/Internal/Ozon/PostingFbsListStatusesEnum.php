<?php

namespace App\Enums\Api\Internal\Ozon;

enum PostingFbsListStatusesEnum: string
{
  case ACCEPTANCE_IN_PROGRESS = 'Идёт приёмка';
  case ARBITRATION = 'Арбитраж';
  case AWAITING_APPROVE = 'Ожидает подтверждения';
  case AWAITING_DELIVER = 'Ожидает отгрузки';
  case AWAITING_PACKAGING = 'Ожидает упаковки';
  case AWAITING_REGISTRATION = 'Ожидает регистрации';
  case AWAITING_VERIFICATION = 'Создано';
  case CANCELLED = 'Отменено';
  case CANCELLED_FROM_SPLIT_PENDING = 'Отменён из-за разделения отправления';
  case CLIENT_ARBITRATION = 'Клиентский арбитраж доставки';
  case DELIVERING = 'Доставляется';
  case DELIVERED = 'Доставлен';
  case DRIVER_PICKUP = 'У водителя';
  case NOT_ACCEPTED = 'Не принят на сортировочном центре';

  /**
   * Возвращает перевод статуса по английскому названию.
   *
   * @param string $status
   * @return string|null
   */
  public static function getTranslation(string $status): ?string
  {
    return match($status) {
      'acceptance_in_progress' => self::ACCEPTANCE_IN_PROGRESS->value,
      'arbitration' => self::ARBITRATION->value,
      'awaiting_approve' => self::AWAITING_APPROVE->value,
      'awaiting_deliver' => self::AWAITING_DELIVER->value,
      'awaiting_packaging' => self::AWAITING_PACKAGING->value,
      'awaiting_registration' => self::AWAITING_REGISTRATION->value,
      'awaiting_verification' => self::AWAITING_VERIFICATION->value,
      'cancelled' => self::CANCELLED->value,
      'cancelled_from_split_pending' => self::CANCELLED_FROM_SPLIT_PENDING->value,
      'client_arbitration' => self::CLIENT_ARBITRATION->value,
      'delivering' => self::DELIVERING->value,
      'delivered' => self::DELIVERED->value,
      'driver_pickup' => self::DRIVER_PICKUP->value,
      'not_accepted' => self::NOT_ACCEPTED->value,
      default => null
    };
  }
}