<?php

namespace App\Enums\Api\Internal;

enum ResourcesEnum: string
{
    case VENDOR_ORDERS = 'vendor_orders';
    case VENDOR_ORDER_ITEMS = 'vendor_order_items';
    case CUSTOMER_ORDERS = 'customer_orders';
    case CUSTOMER_ORDER_ITEMS = 'customer_order_items';
    case ACCEPTANCES = 'acceptances';
    case ACCEPTANCE_ITEMS = 'acceptance_items';
    case SHIPMENTS = 'shipments';
    case SHIPMENT_ITEMS = 'shipment_items';
    case INCOMING_PAYMENTS = 'incoming_payments';
    case INCOMING_PAYMENT_ITEMS = 'incoming_payment_items';
    case OUTGOING_PAYMENTS = 'outgoing_payments';
    case OUTGOING_PAYMENT_ITEMS = 'outgoing_payment_items';
    case PRODUCTS = 'products';
    case CONTRACTORS = 'contractors';
    case LEGAL_ENTITIES = 'legal_entities';
    case EMPLOYEES = 'employees';
    case WAREHOUSES = 'warehouses';
    case SALES_CHANNELS = 'sales_channels';
    case CURRENCIES = 'currencies';
    case COUNTRIES = 'countries';
    case MEASUREMENT_UNITS = 'measurement_units';
    case VAT_RATES = 'vat_rates';
    case PROFIT_TAX_RATES = 'profit_tax_rates';
    case ROLES = 'roles';
    case PACKINGS = 'packings';
    case DEPARTMENTS = 'departments';
    case CONTRACTOR_GROUPS = 'contractor_groups';
}
