<?php

namespace App\Enums\Api\Internal\WarehouseOrderScheme;

enum ValidationLevelEnum: string
{
    case NONE = 'none';
    case WARNING = 'warning';
    case STRICT = 'strict';

    public function getDescription(): string
    {
        return match ($this) {
            self::NONE => 'Без валидации - разрешены все операции',
            self::WARNING => 'Предупреждения - показывать уведомления о нарушениях',
            self::STRICT => 'Строгая валидация - блокировать нарушения правил',
        };
    }

    public function isStrictMode(): bool
    {
        return $this === self::STRICT;
    }

    public function allowsWarnings(): bool
    {
        return $this === self::WARNING || $this === self::STRICT;
    }
}
