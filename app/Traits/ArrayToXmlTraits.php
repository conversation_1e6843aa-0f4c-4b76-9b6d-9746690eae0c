<?php

namespace App\Traits;

use SimpleXMLElement;

trait ArrayToXmlTraits
{
    /**
     * Converts an associative array to an XML structure recursively.
     *
     * @param array $data        The input array to convert to XML.
     * @param SimpleXMLElement $xmlData The current XML node (passed by reference).
     * @param string|null $elementName The optional element name for array values.
     * @return void
     */
    public function arrayToXml(array $data, SimpleXMLElement $xmlData, ?string $elementName = null): void
    {
        foreach ($data as $key => $value) {


            if ($key === '@attributes' && is_array($value)) {
                // Добавляем атрибуты в текущий XML элемент
                foreach ($value as $attrName => $attrValue) {
                    $xmlData->addAttribute($attrName, (string)$attrValue);
                }
            } elseif (is_array($value)) {

                // Если массив индексированный с числовыми ключами
                if (array_keys($value) === range(0, count($value) - 1)) {

                    // dd(array_keys($value), $key, $value, $xmlData->getName());
                    foreach ($value as $subValue) {

                        $elementName = $key;
                        // Используем переданное имя элемента или ключ по умолчанию
                        $subnode = $xmlData->addChild($elementName ?? $xmlData->getName());
                        $this->arrayToXml($subValue, $subnode, $elementName);
                    }
                } else {
                    // Создаем тег с именем ключа
                    $subnode = $xmlData->addChild($key);
                    $this->arrayToXml($value, $subnode, $key);
                }

            } else {
                $xmlData->addChild("$key", htmlspecialchars((string)$value, ENT_QUOTES | ENT_XML1));
            }
        }
    }
}
