<?php

namespace App\Traits;

use Carbon\Carbon;

trait HasTimestamps
{
    public function setTimestamps(array $data): array
    {
        if (isset($data['created_at'], $data['updated_at']) && $data['created_at'] && $data['updated_at']) {
            return $data;
        }

        if (isset($data[0]) && is_array($data[0])) {

            $baseTime = Carbon::now();
            $usedTimes = [];

            foreach ($data as $i => &$item) {
                $secondsToAdd = $i + 1;

                do {
                    $timestamp = $baseTime->copy()->addSeconds($secondsToAdd);
                    $timeString = $timestamp->toDateTimeString();
                    $secondsToAdd++;
                } while (in_array($timeString, $usedTimes));

                $usedTimes[] = $timeString;

                if (!isset($item['created_at'])) {
                    $item['created_at'] = $timeString;
                }

                if (!isset($item['updated_at'])) {
                    $item['updated_at'] = $timeString;
                }
            }
        } else {

            $now = Carbon::now()->toDateTimeString();

            if (!isset($data['created_at'])) {
                $data['created_at'] = $now;
            }

            if (!isset($data['updated_at'])) {
                $data['updated_at'] = $now;
            }
        }

        return $data;
    }

    public function setUpdatedAt(array $data): array
    {
        $now = Carbon::now();

        if (isset($data[0]) && is_array($data[0])) {
            foreach ($data as &$item) {
                $item['updated_at'] = $now;
            }
        } else {
            $data['updated_at'] = $now;
        }
        return $data;
    }
}
