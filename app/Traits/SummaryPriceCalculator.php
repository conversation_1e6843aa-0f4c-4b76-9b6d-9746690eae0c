<?php

namespace App\Traits;

trait SummaryPriceCalculator
{
    use PrecisionCalculator;

    public function calculateSumPrice(array $item): string
    {
        $price = $this->toStringNumber($item['price']);
        $quantity = $this->toStringNumber($item['quantity']);
        $discount = $this->toStringNumber($item['discount'] ?? '0');

        $totalPrice = $this->multiply($price, $quantity);

        if ($this->compare($discount, '0') > 0) {
            $totalPrice = $this->applyDiscount($totalPrice, $discount);
        }

        return $totalPrice;
    }
}
