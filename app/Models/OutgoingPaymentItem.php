<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OutgoingPaymentItem extends Model
{
    use HasUuids;
    use HasFactory;

    use HasUuids;
    use HasFactory;

    protected $fillable = [
        'outgoing_payment_id',
        'document_id',
        'paid_in'
    ];
}
