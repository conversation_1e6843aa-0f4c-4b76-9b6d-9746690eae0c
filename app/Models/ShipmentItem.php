<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShipmentItem extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'shipment_id', 'product_id','quantity',
        'price', 'cost','total_cost',
        'total_price', 'total_vat_sum', 'profit', 'vat_rate_id',
        'discount'
    ];
}
