<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContractorStatus extends Model
{
    use HasFactory;
    use HasUuids;
    use SoftDeletes;

    // protected $table = 'statuses';
    protected $table = 'contractor_statuses';

    /**
    * The attributes that are mass assignable.
    *
    * @var array<int, string>
    */
    protected $fillable = [
        'id',                   // id
        'cabinet_id',
        'title',
        'type',
        'color',
        'sort',
    ];

    public function cabinet(): BelongsTo
    {
        return $this->belongsTo(Cabinet::class);
    }


}
