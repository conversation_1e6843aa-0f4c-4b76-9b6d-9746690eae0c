<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AcceptanceItem extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'acceptance_id', 'product_id','quantity',
        'price', 'vat_rate_id', 'discount',
        'total_price', 'total_vat_sum', 'country_id',
        'gtd_number'
    ];
}
