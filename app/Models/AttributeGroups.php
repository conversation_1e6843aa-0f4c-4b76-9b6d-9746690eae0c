<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class AttributeGroups extends Model
{
    use HasFactory;
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'cabinet_id',
        'name',
        'description',
        'sort_order',
        'status',
    ];

    /**
     *  @return HasMany<Attribute>
     */
    public function attributes(): HasMany
    {
        return $this->hasMany(Attribute::class);
    }

    /**
    * @return BelongsTo<Cabinet, AttributeGroups>
    */
    public function cabinet(): BelongsTo
    {
        return $this->belongsTo(Cabinet::class);
    }

}
