<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncomingPayment extends Model
{
    use HasUuids;
    use HasFactory;

    protected $fillable = [
        'cabinet_id',
        'status_id',
        'number',
        'date_from',
        'held',
        'legal_entity_id',
        'contractor_id',
        'sales_channel_id',
        'sum',
        'included_vat',
        'comment',
        'incoming_number',
        'incoming_date',
        'currency_id',
        'currency_value',
        'bounded_sum',
        'not_bounded_sum',
        'is_imported',
        'employee_id',
        'department_id',
        'is_default',
        'is_common',
    ];
}
