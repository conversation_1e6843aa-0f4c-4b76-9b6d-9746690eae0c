<?php

namespace App\Contracts\Services;

use App\Entities\BaseEntity;
use App\Entities\EntityBuilder;
use Illuminate\Database\Query\Builder;

interface AuthorizationServiceContract
{
    public function validateResourcesAccess(
        string $entity,
        string $cabinetId,
        array $ids,
        ?string $permission = null,
        ?string $operation = null,
        ?array $relatedEntity = null
    ): void;

    public function init(): void;
    public function loadPermissions(): void;

    /*
     * Так же выполняет проверку на доступ в кабинет
     * Но есть случаи, когда нужно проверить дополнительные права
     * Поэтому вызываем hasAccessToCabinet с дополнительными параметрами после этого метода
     */
    public function validateRelationAccess(
        string $entity,
        string $entityId,
        ?string $expectedCabinetId = null,
        ?string $permission = null,
        ?string $operation = null,
        ?string $parentCabinetId = null,
        ?array $relatedEntity = null // ['table' => 'acceptances', 'field' => 'acceptance_id', 'value' => $acceptanceId]
    ): object;

    /*
     * Проверяет право на манипуляции с конкретным ресурсом
     * Если не были переданы в validateRelationAccess нужные права
     */
    public function hasEntityPermission(object $record, string $permission, ?string $operation = null): void;
    public function hasAccessToCabinet(string $cabinetId, array $permissions = []): void;
    public function queryFilter(string $cabinetId, Builder|BaseEntity|EntityBuilder $query, string $permissionName, ?string $tableName = null): Builder|BaseEntity|EntityBuilder;
    public function validateProductsAccess(array $productIds, string $cabinetId): void;
    public function validatePaymentsAccess(string $cabinetId, array $ids, string $operation): void;
}
