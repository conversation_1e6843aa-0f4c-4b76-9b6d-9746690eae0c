<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface VendorOrdersRepositoryContract extends CRUDRepositoryContract
{
    public function firstByItemId(string $itemId): ?object;
    public function deleteWhereIn(array $ids): int;

    public function updateWhereIn(array $ids, array $data): int;

    public function getWhereInIds(array $ids): Collection;
}
