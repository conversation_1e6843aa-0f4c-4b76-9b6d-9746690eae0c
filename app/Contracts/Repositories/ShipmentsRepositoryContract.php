<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface ShipmentsRepositoryContract extends CRUDRepositoryContract
{
    public function findShipmentItemIdsByProductAndDate(string $productId, string $date): Collection;
    public function findShipmentItemIdByProductAndDate(string $productId, string $date): ?string;
    public function upsert(array $values, array $update): void;
    public function findFirst(string $resourceId): ?object;
    public function deleteWhereIn(array $resourceIds): int;
}
