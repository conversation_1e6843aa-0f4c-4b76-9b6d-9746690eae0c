<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface AttributeValuesRepositoryContract extends CRUDRepositoryContract
{
    public function getWhereAttributeIdInAttributableId(string $attributeId, array $ids): array;
    public function getFirstForAttributeId(string $id): ?object;
    public function getAttributeValues(string $attributeId): Collection;
}
