<?php

namespace App\Contracts\Repositories;

use Illuminate\Support\Collection;
use App\Contracts\CRUDRepositoryContract;

interface ProductAccountingFeaturesRepositoryContract  extends CRUDRepositoryContract
{
    public function getMyCountWhereInIds(array|Collection $ids, string $cabinetId): int;

    public function getFirst(string $resourceId): ?object;

    public function getFirstForProduct(string $resourceId): ?object;

}
